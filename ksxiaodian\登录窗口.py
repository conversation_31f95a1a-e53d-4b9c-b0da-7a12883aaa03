#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快手小店管理系统 - 登录窗口模块
------------------------------------------------
功能概述:
- 用户认证: 验证用户身份并授权进入系统
- 记住密码: 支持本地保存用户凭据以便快速登录
- 服务器验证: 与远程服务器通信验证用户身份

界面特性:
1. 现代磨砂设计: 半透明效果提供高品质视觉体验
2. 表单验证: 防止空白输入，确保数据完整性
3. 视觉反馈: 登录过程中的状态提示和错误信息
4. 键盘导航: 支持回车键提交和Tab键切换字段

使用方法:
1. 输入账号和密码
2. 选择"记住密码"可在下次启动时自动填充
3. 点击"登录"按钮或按回车键提交
4. 登录成功后自动进入主界面

安全考虑:
- 密码验证在服务器端进行
- 本地存储的凭据未加密，生产环境应增加加密措施

开发人员说明:
- 登录验证使用HTTP POST请求访问服务器API
- 默认验证接口: http://**************:8000/database
- 验证成功后通过信号机制通知主程序
- 凭据存储在config/config.json文件中
"""

import sys
import os
import requests
import json
import win32com.client
import tempfile
import shutil
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                           QLabel, QLineEdit, QPushButton, QCheckBox, QFrame, QMessageBox,
                           QGraphicsDropShadowEffect)
from PyQt5.QtGui import QIcon, QPixmap, QColor, QFont, QPainter, QPainterPath, QPen
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QTimer

def get_application_directory():
    """
    获取应用程序所在目录（exe文件所在目录）

    返回:
        str: 应用程序所在目录的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        app_dir = os.path.dirname(sys.executable)
        print(f"打包环境 - exe所在目录: {app_dir}")
    else:
        # 开发环境
        app_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"开发环境 - 脚本所在目录: {app_dir}")

    return app_dir

def get_config_path(relative_path):
    """
    获取配置文件的绝对路径，确保使用exe同目录的config
    如果是打包环境且配置文件不存在，尝试从嵌入资源中提取

    参数:
        relative_path (str): 相对于config目录的文件路径

    返回:
        str: 配置文件的绝对路径
    """
    app_dir = get_application_directory()
    config_path = os.path.join(app_dir, 'config', relative_path)

    # 如果是打包环境且配置文件不存在，尝试从嵌入资源中提取
    if getattr(sys, 'frozen', False) and not os.path.exists(config_path):
        extract_embedded_config_file(relative_path, config_path)

    return config_path

def extract_embedded_config_file(relative_path, target_path):
    """
    从打包的exe中提取嵌入的配置文件到运行目录

    参数:
        relative_path (str): 相对于config目录的文件路径
        target_path (str): 目标文件的完整路径
    """
    try:
        import tempfile
        import shutil

        # 确保目标目录存在
        target_dir = os.path.dirname(target_path)
        if not os.path.exists(target_dir):
            os.makedirs(target_dir)
            print(f"创建配置目录: {target_dir}")

        # 尝试从PyInstaller的临时目录中找到配置文件
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller的临时目录
            embedded_path = os.path.join(sys._MEIPASS, 'config', relative_path)
            if os.path.exists(embedded_path):
                shutil.copy2(embedded_path, target_path)
                print(f"从嵌入资源提取配置文件: {relative_path} -> {target_path}")
                return True

        # 如果找不到嵌入的配置文件，创建默认配置
        if relative_path == 'config.json':
            create_default_config(target_path)
            return True

        print(f"警告: 无法找到嵌入的配置文件: {relative_path}")
        return False

    except Exception as e:
        print(f"提取配置文件时出错: {str(e)}")
        return False

def create_default_config(config_path):
    """
    创建默认的配置文件

    参数:
        config_path (str): 配置文件的完整路径
    """
    try:
        default_config = {
            "database_server_url": "http://**************:8000",
            "saved_credentials": {}
        }

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)

        print(f"创建默认配置文件: {config_path}")

    except Exception as e:
        print(f"创建默认配置文件时出错: {str(e)}")

def get_resource_path(relative_path):
    """
    获取资源文件的绝对路径，确保使用exe同目录的资源

    参数:
        relative_path (str): 相对于应用程序目录的资源路径

    返回:
        str: 资源文件的绝对路径
    """
    app_dir = get_application_directory()
    resource_path = os.path.join(app_dir, relative_path)
    print(f"资源文件完整路径: {resource_path}")
    return resource_path

def get_configured_server_url(default_url="http://**************:8000"):
    """
    获取配置的服务器URL，统一使用应用程序根目录下的config

    参数:
        default_url (str): 默认服务器URL

    返回:
        str: 配置的服务器URL
    """
    config_path = get_config_path('config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            url = config_data.get("database_server_url", default_url)
            if url == default_url and "database_server_url" not in config_data:
                print(f"警告: 在 {config_path} 中未找到 'database_server_url'，使用默认地址: {url}")
            return url
    except FileNotFoundError:
        print(f"错误: 配置文件 {config_path} 未找到，使用默认地址: {default_url}")
        return default_url
    except json.JSONDecodeError:
        print(f"错误: 解析配置文件 {config_path} 失败，使用默认地址: {default_url}")
        return default_url
    except Exception as e:
        print(f"读取配置文件 {config_path} 时发生未知错误: {e}，使用默认地址: {default_url}")
        return default_url

def login_verify(username, password, server_url=None, table_name="账号"):
    """
    使用现有的查询接口验证用户登录

    参数:
    username -- 用户名
    password -- 密码
    server_url -- 服务器地址，如果为None则从配置加载
    table_name -- 用户表名称，默认为"账号"

    返回:
    dict -- 包含登录结果的字典，具有以下字段:
        - success: 布尔值，表示登录是否成功
        - message: 字符串，登录结果消息
        - user: 字典，包含用户信息（仅在成功时）
    """
    if server_url is None:
        server_url = get_configured_server_url()

    try:
        # 使用查询接口获取指定用户名的记录
        query_url = f"{server_url}/database?action=query&table={table_name}"

        # 发送POST请求，使用条件查询只获取特定帐号的记录
        # 账号表中使用"账号"和"密码"字段
        response = requests.post(
            query_url,
            json={"conditions": {"账号": username}},
            headers={"Content-Type": "application/json"}
        )

        # 解析响应
        result = response.json()

        if result.get("status") == "success" and result.get("data"):
            # 找到用户记录，验证密码
            user_data = result["data"][0]
            if user_data.get("密码") == password:
                # 登录成功
                return {
                    "success": True,
                    "message": "登录成功",
                    "user": {
                        "username": username,
                        # 可以返回其他用户信息，但不要包含密码
                        "role": user_data.get("角色", "user")
                    }
                }
            else:
                # 密码错误
                return {
                    "success": False,
                    "message": "密码错误"
                }
        else:
            # 用户不存在或查询失败
            return {
                "success": False,
                "message": "用户不存在或查询失败"
            }
    except Exception as e:
        # 请求出错
        return {
            "success": False,
            "message": f"登录请求出错: {str(e)}"
        }

def extract_icon_to_temp():
    """
    从打包资源中提取图标文件到临时目录

    功能:
        - 检测是否为打包环境
        - 从内部资源提取logo.ico到临时文件
        - 返回临时图标文件路径

    返回:
        str: 临时图标文件路径，如果失败返回None
    """
    try:
        if getattr(sys, 'frozen', False):
            # 打包环境：从内部资源提取图标
            icon_resource_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))

            if os.path.exists(icon_resource_path):
                # 创建临时文件
                temp_dir = tempfile.gettempdir()
                temp_icon_path = os.path.join(temp_dir, "ksxd_logo.ico")

                # 复制图标文件到临时目录
                shutil.copy2(icon_resource_path, temp_icon_path)
                print(f"图标提取到临时文件: {temp_icon_path}")
                return temp_icon_path
            else:
                print(f"打包资源中未找到图标文件: {icon_resource_path}")
                return None
        else:
            # 开发环境：直接返回原始路径
            icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
            if os.path.exists(icon_path):
                print(f"开发环境使用图标: {icon_path}")
                return icon_path
            else:
                print(f"开发环境未找到图标文件: {icon_path}")
                return None

    except Exception as e:
        print(f"提取图标文件失败: {str(e)}")
        return None

def create_desktop_shortcut():
    """
    检查桌面是否存在快捷方式，如果不存在则创建

    功能:
        - 获取用户桌面路径
        - 检查是否已存在"快手小店管理系统.lnk"快捷方式
        - 如果不存在，创建新的快捷方式
        - 设置快捷方式图标为 config/imges/ks.png
        - 目标程序为当前运行的程序

    返回:
        bool: True表示成功创建或已存在，False表示创建失败
    """
    try:
        # 使用 Windows Shell API 获取桌面路径
        shell = win32com.client.Dispatch("WScript.Shell")
        desktop_path = shell.SpecialFolders("Desktop")

        # 如果 Shell API 失败，使用传统方法
        if not desktop_path or not os.path.exists(desktop_path):
            # 获取用户桌面路径，优先使用中文路径
            desktop_path = os.path.join(os.path.expanduser("~"), "桌面")
            if not os.path.exists(desktop_path):
                # 如果中文桌面文件夹不存在，尝试使用英文路径
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")

        # 检查桌面路径是否存在
        if not os.path.exists(desktop_path):
            print(f"桌面路径不存在: {desktop_path}")
            return False

        # 检查桌面路径是否可写
        if not os.access(desktop_path, os.W_OK):
            print(f"桌面路径无写入权限: {desktop_path}")
            return False

        # 快捷方式文件路径
        shortcut_path = os.path.join(desktop_path, "快手小店管理系统.lnk")

        # 获取当前程序的路径
        if getattr(sys, 'frozen', False):
            # 打包后的exe文件
            target_path = sys.executable
        else:
            # 开发环境，指向Python脚本
            target_path = os.path.abspath(__file__)

        # 检查目标文件是否存在
        if not os.path.exists(target_path):
            print(f"目标程序不存在: {target_path}")
            return False

        # 检查快捷方式是否已存在
        if os.path.exists(shortcut_path):
            try:
                # 读取现有快捷方式的目标路径
                shell = win32com.client.Dispatch("WScript.Shell")
                existing_shortcut = shell.CreateShortCut(shortcut_path)
                existing_target = existing_shortcut.Targetpath

                # 比较目标路径是否一致
                if os.path.normpath(existing_target) == os.path.normpath(target_path):
                    print(f"桌面快捷方式已存在且目标路径正确: {shortcut_path}")
                    print(f"目标路径: {existing_target}")
                    return True
                else:
                    print(f"桌面快捷方式目标路径不正确，需要重新创建")
                    print(f"现有目标: {existing_target}")
                    print(f"期望目标: {target_path}")
                    # 删除旧的快捷方式
                    os.remove(shortcut_path)
                    print(f"已删除旧的快捷方式: {shortcut_path}")
            except Exception as e:
                print(f"检查现有快捷方式时出错: {str(e)}")
                # 如果检查失败，删除并重新创建
                try:
                    os.remove(shortcut_path)
                    print(f"已删除有问题的快捷方式: {shortcut_path}")
                except:
                    pass

        # 创建快捷方式
        shell = win32com.client.Dispatch("WScript.Shell")
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = target_path
        shortcut.WorkingDirectory = os.path.dirname(target_path)
        shortcut.Description = "快手小店管理系统"

        # 设置图标，从打包资源中提取到临时文件
        icon_path = extract_icon_to_temp()

        if icon_path and os.path.exists(icon_path):
            # 使用提取的图标文件（高分辨率，适合桌面显示）
            shortcut.IconLocation = f"{icon_path},0"
            print(f"设置快捷方式图标: {icon_path}")
        else:
            # 备用方案：使用程序本身图标
            shortcut.IconLocation = f"{target_path},0"
            print(f"备用方案：使用程序图标: {target_path}")

        # 保存快捷方式
        shortcut.save()

        # 验证快捷方式是否创建成功
        if os.path.exists(shortcut_path):
            print(f"成功创建桌面快捷方式: {shortcut_path}")
            return True
        else:
            print(f"快捷方式创建失败，文件不存在: {shortcut_path}")
            return False

    except Exception as e:
        print(f"创建桌面快捷方式失败: {str(e)}")
        return False

class LoginWindow(QWidget):
    """
    登录窗口类

    提供用户登录界面，包括账号密码输入、记住密码功能和登录验证。
    成功登录后发送信号通知主程序并关闭登录窗口。

    属性:
        server_url (str): 服务器API地址
        loginSuccess (signal): 登录成功时发出的信号，包含用户名、密码和用户信息

    方法:
        initUI: 初始化用户界面
        login: 处理登录逻辑
        save_credentials: 保存用户凭据
        load_saved_credentials: 加载保存的凭据
    """

    # 定义登录成功的信号
    loginSuccess = pyqtSignal(str, str, dict)  # 用户名、密码和用户信息

    def __init__(self, parent=None, server_url=None):
        super().__init__(parent)

        if server_url is None:
            self.server_url = get_configured_server_url()
        else:
            self.server_url = server_url

        self.drag_position = None  # 添加拖动位置属性

        # 异步检查并创建桌面快捷方式，避免阻塞UI
        QTimer.singleShot(100, create_desktop_shortcut)

        self.initUI()

    def initUI(self):
        """
        初始化登录窗口的用户界面

        创建并配置所有UI元素，包括:
        - 窗口基本属性（大小、标题、图标）
        - 输入控件（用户名、密码）
        - 操作按钮（登录、忘记密码）
        - 布局管理
        - 样式设置
        - 事件连接
        """
        # 设置窗口属性
        self.setObjectName("LoginWindow")
        self.setWindowTitle('快手小店管理系统 - 登录')
        self.setFixedSize(388, 300)  # 将高度从259调整为300
        self.setWindowFlags(Qt.FramelessWindowHint)  # 设置无边框窗口
        self.setAttribute(Qt.WA_TranslucentBackground)  # 设置窗口背景透明，以便应用圆角效果

        # 设置窗口背景颜色
        self.setStyleSheet("""
            QWidget#LoginWindow {
                background-color: transparent;
                border-radius: 12px;
            }
        """)

        # 设置窗口图标 - 使用软件运行目录的相对路径
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        print(f"🎨 登录窗口尝试设置图标: {icon_path}")
        print(f"   文件是否存在: {os.path.exists(icon_path)}")

        if os.path.exists(icon_path):
            try:
                icon = QIcon(icon_path)
                if not icon.isNull():
                    self.setWindowIcon(icon)
                    print(f"✅ 登录窗口图标设置成功")
                else:
                    print(f"❌ 登录窗口图标对象创建失败")
            except Exception as e:
                print(f"❌ 登录窗口设置图标异常: {e}")
        else:
            print(f"❌ 登录窗口图标文件不存在")
            print(f"   当前工作目录: {os.getcwd()}")

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 自定义标题栏
        title_bar = QFrame(self)
        title_bar.setObjectName("TitleBar")
        title_bar.setFixedHeight(30)
        title_bar.setStyleSheet("""
            QFrame#TitleBar {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                                  stop:0 #ffffff, stop:1 #f5f5f7);
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                border-bottom: 1px solid #e0e0e5;
            }
        """)

        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(10, 0, 10, 0)

        # 添加标题栏图标
        logo_label = QLabel()
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        logo_pixmap = QPixmap(icon_path)
        if not logo_pixmap.isNull():
            # 缩放图标到16x16像素，确保在容器中有足够边距不被截断
            logo_pixmap = logo_pixmap.scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            logo_label.setPixmap(logo_pixmap)
        logo_label.setFixedSize(26, 20)  # 进一步增加宽度，给图标更多显示空间
        logo_label.setAlignment(Qt.AlignCenter)  # 设置图标居中对齐
        logo_label.setStyleSheet("margin-right: 3px; padding: 2px;")

        title_label = QLabel('快手小店管理系统 - 登录')
        title_label.setStyleSheet("""
            font-family: 'Microsoft YaHei';
            font-size: 13px;
            color: #333333;
            font-weight: 500;
        """)

        close_button = QPushButton("×")
        close_button.setFixedSize(20, 20)
        close_button.setStyleSheet("""
            QPushButton {
                font-family: Arial;
                font-size: 16px;
                color: #666666;
                background-color: transparent;
                border: none;
            }
            QPushButton:hover {
                color: #ff0000;
            }
        """)
        close_button.clicked.connect(self.close)

        title_layout.addWidget(logo_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(close_button)

        main_layout.addWidget(title_bar)

        # 登录表单内容区域
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        content_frame.setStyleSheet("""
            #contentFrame {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                                stop:0 #ffffff, stop:1 #f8f9fc);
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
                border: 1px solid #d8e1ed;
                border-top: none;
            }
        """)

        # 添加阴影效果
        content_shadow = QGraphicsDropShadowEffect()
        content_shadow.setBlurRadius(25)
        content_shadow.setColor(QColor(74, 107, 223, 60))
        content_shadow.setOffset(0, 3)
        content_frame.setGraphicsEffect(content_shadow)

        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(30, 30, 30, 30)  # 增加上边距，弥补顶部区域的移除
        content_layout.setSpacing(12)

        # 用户名输入框
        username_label = QLabel("账号")
        username_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                color: #364f6b;
                font-weight: 500;
            }
        """)

        self.username_input = QLineEdit()
        self.username_input.setFixedHeight(36)
        self.username_input.setPlaceholderText("请输入账号")
        self.username_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #c5d0e0;
                border-radius: 5px;
                padding: 8px 12px;
                background-color: #f6f8fc;
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                color: #364f6b;
                box-shadow: inset 0 2px 3px rgba(0, 0, 0, 0.05);
            }
            QLineEdit:focus {
                border: 1px solid #4e7ae7;
                background-color: #ffffff;
                box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.03), 0 0 5px rgba(78, 122, 231, 0.3);
            }
        """)

        # 密码输入框
        password_label = QLabel("密码")
        password_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                color: #364f6b;
                font-weight: 500;
            }
        """)

        self.password_input = QLineEdit()
        self.password_input.setFixedHeight(36)
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #c5d0e0;
                border-radius: 5px;
                padding: 8px 12px;
                background-color: #f6f8fc;
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                color: #364f6b;
                box-shadow: inset 0 2px 3px rgba(0, 0, 0, 0.05);
            }
            QLineEdit:focus {
                border: 1px solid #4e7ae7;
                background-color: #ffffff;
                box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.03), 0 0 5px rgba(78, 122, 231, 0.3);
            }
        """)

        # 记住密码选项
        remember_layout = QHBoxLayout()

        self.remember_check = QCheckBox("记住密码")
        self.remember_check.setCursor(Qt.PointingHandCursor)
        # 使用系统默认复选框样式，只设置字体
        self.remember_check.setStyleSheet("""
            QCheckBox {
                font-family: 'Microsoft YaHei';
                font-size: 13px;
                color: #546a7b;
            }
        """)

        forgot_password_btn = QPushButton("忘记密码?")
        forgot_password_btn.setCursor(Qt.PointingHandCursor)
        forgot_password_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                font-family: 'Microsoft YaHei';
                font-size: 13px;
                color: #4e7ae7;
                text-align: right;
            }
            QPushButton:hover {
                color: #3a5bd0;
                text-decoration: underline;
            }
        """)

        remember_layout.addWidget(self.remember_check)
        remember_layout.addStretch()
        remember_layout.addWidget(forgot_password_btn)

        # 登录按钮
        self.login_btn = QPushButton("登录")
        self.login_btn.setFixedHeight(38)
        self.login_btn.setCursor(Qt.PointingHandCursor)
        self.login_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #5a86e5, stop:1 #4e7ae7);
                color: white;
                border: none;
                border-radius: 5px;
                font-family: 'Microsoft YaHei';
                font-size: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #4a76e0, stop:1 #3a6be0);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #3a6be0, stop:1 #2f5bc8);
                padding-top: 2px;
            }
        """)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(74, 107, 223, 100))
        shadow.setOffset(0, 4)
        self.login_btn.setGraphicsEffect(shadow)

        # 连接登录按钮点击事件
        self.login_btn.clicked.connect(self.login)

        # 添加所有组件到内容区域
        content_layout.addWidget(username_label)
        content_layout.addWidget(self.username_input)
        content_layout.addWidget(password_label)
        content_layout.addWidget(self.password_input)
        content_layout.addLayout(remember_layout)
        content_layout.addSpacing(6)  # 增加一些间距
        content_layout.addWidget(self.login_btn)
        content_layout.addStretch()

        # 添加标题区域和内容区域到主布局
        main_layout.addWidget(content_frame, 1)

        # 让按下回车键也能登录
        self.password_input.returnPressed.connect(self.login)
        self.username_input.returnPressed.connect(lambda: self.password_input.setFocus())

        # 设置默认焦点到用户名输入框
        self.username_input.setFocus()

    def login(self):
        """
        处理登录逻辑，验证用户输入并调用服务器API进行身份验证

        1. 检查输入字段是否为空
        2. 更新登录按钮状态显示登录中
        3. 调用服务器API验证用户身份
        4. 根据验证结果处理后续流程:
           - 成功: 保存凭据(如勾选)，发送成功信号，关闭窗口
           - 失败: 显示错误消息，清空密码输入框
        5. 恢复登录按钮状态
        """
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username:
            QMessageBox.warning(self, "登录失败", "请输入账号")
            self.username_input.setFocus()
            return

        if not password:
            QMessageBox.warning(self, "登录失败", "请输入密码")
            self.password_input.setFocus()
            return

        # 登录按钮状态改变，表示正在登录
        original_text = self.login_btn.text()
        self.login_btn.setText("登录中...")
        self.login_btn.setEnabled(False)

        # 调用服务器验证函数
        try:
            # 调用login_verify函数进行验证
            result = login_verify(username, password, self.server_url)

            if result["success"]:
                # 登录成功
                if self.remember_check.isChecked():
                    # 如果选择了记住密码，可以在这里保存凭据
                    self.save_credentials(username, password)

                # 发送登录成功信号
                self.loginSuccess.emit(username, password, result.get("user", {}))
                self.close()
            else:
                # 登录失败
                QMessageBox.warning(self, "登录失败", result["message"])
                self.password_input.clear()
                self.password_input.setFocus()
        except Exception as e:
            # 处理异常
            QMessageBox.critical(self, "登录错误", f"发生错误: {str(e)}")
        finally:
            # 恢复登录按钮状态
            self.login_btn.setText(original_text)
            self.login_btn.setEnabled(True)

    def save_credentials(self, username, password):
        """
        保存用户凭据到 config/config.json 文件

        参数:
            username (str): 用户名
            password (str): 密码

        说明:
            将用户凭据以键 "saved_credentials" 保存到 config/config.json 文件中。
            用于实现"记住密码"功能。
            注意: 当前实现未加密存储密码，生产环境应增强安全性。
        """
        try:
            # 使用新的路径获取函数
            config_file_path = get_config_path("config.json")
            config_dir = os.path.dirname(config_file_path)

            # 确保存储目录存在
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            config_data = {}
            # 如果配置文件已存在，则先读取其内容
            if os.path.exists(config_file_path):
                try:
                    with open(config_file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if content.strip():  # 确保文件内容不为空或仅包含空白
                            config_data = json.loads(content)
                        else:
                            # 文件为空，初始化为空字典，避免json.loads错误
                            config_data = {}
                except json.JSONDecodeError:
                    # 文件内容不是有效的JSON，打印警告并重置为空字典
                    print(f"警告: config/config.json 内容不是有效的JSON格式，将重新创建。")
                    config_data = {}
                except Exception as e:
                    # 其他读取错误
                    print(f"读取 config/config.json 时发生错误: {str(e)}，将尝试覆盖。")
                    config_data = {}

            # 更新或添加凭据信息
            config_data["saved_credentials"] = {
                "username": username,
                "password": password  # 再次提醒：实际应用中应加密密码
            }

            # 将更新后的配置写回文件
            with open(config_file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存凭据到 config/config.json 失败: {str(e)}")

    def load_saved_credentials(self):
        """
        从 config/config.json 文件加载保存的用户凭据

        读取 config/config.json 文件，并填充到登录界面的表单中。
        如果文件存在且 "saved_credentials" 键包含有效数据，自动勾选"记住密码"选项。
        如果文件不存在或读取失败，表单保持空白。
        """
        try:
            # 使用新的路径获取函数
            config_file_path = get_config_path("config.json")

            if os.path.exists(config_file_path):
                with open(config_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if not content.strip(): # 如果文件内容为空
                        print("config/config.json 文件为空，不加载凭据。")
                        self.remember_check.setChecked(False)
                        return  # 直接返回，不进行后续操作

                    config_data = json.loads(content) # 解析JSON内容

                # 检查 "saved_credentials" 是否存在且为字典类型
                if "saved_credentials" in config_data and isinstance(config_data["saved_credentials"], dict):
                    credentials = config_data["saved_credentials"]
                    self.username_input.setText(credentials.get("username", ""))
                    self.password_input.setText(credentials.get("password", ""))
                    self.remember_check.setChecked(True)
                else:
                    # 如果 "saved_credentials" 不存在或格式不正确
                    print("在 config/config.json 中未找到 'saved_credentials' 或其格式不正确。")
                    self.remember_check.setChecked(False)
            else:
                # 如果配置文件不存在
                print("config/config.json 文件不存在，不加载凭据。")
                self.remember_check.setChecked(False)
        except json.JSONDecodeError:
            # JSON解析失败
            print(f"解析 config/config.json 失败。文件可能不是有效的JSON格式。")
            self.remember_check.setChecked(False)
        except Exception as e:
            # 其他所有加载错误
            print(f"从 config/config.json 加载凭据失败: {str(e)}")
            self.remember_check.setChecked(False)

    def keyPressEvent(self, event):
        """
        处理键盘事件

        参数:
            event: 键盘事件对象

        功能:
            - ESC键: 关闭登录窗口
            - 其他键: 调用父类默认处理
        """
        if event.key() == Qt.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)

    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)

    def mousePressEvent(self, event):
        """
        鼠标按下事件处理

        参数:
            event (QMouseEvent): 鼠标事件对象
        """
        if event.button() == Qt.LeftButton:
            # 记录拖动起始位置
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """
        鼠标移动事件处理

        参数:
            event (QMouseEvent): 鼠标事件对象
        """
        if event.buttons() == Qt.LeftButton and self.drag_position is not None:
            # 计算新位置并移动窗口
            self.move(event.globalPos() - self.drag_position)
            event.accept()




if __name__ == "__main__":
    app = QApplication(sys.argv)
    login_window = LoginWindow()

    # 尝试加载保存的凭据
    login_window.load_saved_credentials()

    login_window.show()
    sys.exit(app.exec_())