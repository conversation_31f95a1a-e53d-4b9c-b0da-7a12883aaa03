#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
详情统计模块 - 提供详细的订单统计功能
=================================================

功能概述
-------------------------------------------------
此模块提供了详细的订单统计功能，包括但不限于：
1. 按时间段统计订单数量、销售额、利润等
2. 按店铺统计订单详情
3. 按商品类型统计销售情况
4. 生成统计报表和图表

使用方法
-------------------------------------------------
1. 创建DetailStatisticsWindow实例
2. 调用相关方法进行统计分析
"""

import sys
import os
import json
import urllib.parse
import requests
import traceback
import urllib3
import warnings

# 添加快手API导入
try:
    from .快手api import KuaishouAPI
except ImportError:
    try:
        # 尝试不同的导入方式
        import sys
        import os
        tool_dir = os.path.dirname(__file__)
        if tool_dir not in sys.path:
            sys.path.insert(0, tool_dir)
        from 快手api import KuaishouAPI
    except ImportError:
        print("警告：无法导入快手API模块，下架功能将不可用")
        KuaishouAPI = None

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings('ignore', message='Unverified HTTPS request')
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView, QLabel,
                            QPushButton, QFrame, QLineEdit, QApplication, QMenu, QAction,
                            QMessageBox, QSpacerItem, QSizePolicy, QCheckBox, QToolTip,
                            QAbstractItemView, QDialog)
from PyQt5.QtCore import Qt, QTimer, QPoint, QThread, pyqtSignal
from PyQt5.QtGui import QCursor, QFont, QPixmap, QPainter, QClipboard, QColor

def get_application_directory():
    """
    获取应用程序所在目录（exe文件所在目录）

    返回:
        str: 应用程序所在目录的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        app_dir = os.path.dirname(sys.executable)
        print(f"打包环境 - exe所在目录: {app_dir}")
    else:
        # 开发环境
        app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        print(f"开发环境 - 脚本所在目录: {app_dir}")

    return app_dir

def get_config_path(relative_path):
    """
    获取配置文件的绝对路径，确保使用exe同目录的config

    参数:
        relative_path (str): 相对于config目录的文件路径

    返回:
        str: 配置文件的绝对路径
    """
    app_dir = get_application_directory()
    config_path = os.path.join(app_dir, 'config', relative_path)
    return config_path

class ShopDetailTooltip(QLabel):
    """表格1店铺详情店铺信息框类"""

    def __init__(self, parent=None):
        super().__init__(parent)
        # 使用NoDropShadowWindowHint移除系统级阴影效果
        self.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint | Qt.NoDropShadowWindowHint)
        self.setAttribute(Qt.WA_ShowWithoutActivating, True)

        # 关键：设置透明背景，让自定义绘制生效
        self.setAttribute(Qt.WA_TranslucentBackground, True)

        # 设置基础样式（不使用border-radius，避免圆角阴影）
        self.setup_base_style()

        self.hide()

    def setup_base_style(self):
        """设置基础样式 - 不使用border-radius避免圆角阴影"""
        try:
            # 不使用border-radius，通过自定义绘制实现圆角
            self.setStyleSheet("""
                QLabel {
                    background-color: transparent;
                    border: none;
                    padding: 16px;
                    font-family: 'Microsoft YaHei';
                    font-size: 12px;
                    font-weight: 500;
                    color: #374151;
                    line-height: 1.6;
                    min-width: 220px;
                    max-width: 380px;
                }
            """)
            print("✅ 店铺详情提示框基础样式设置成功")

        except Exception as e:
            print(f"❌ 设置店铺详情提示框样式失败: {str(e)}")
            # 最基础的备选方案
            self.setStyleSheet("""
                QLabel {
                    background-color: transparent;
                    border: none;
                    padding: 12px;
                    font-family: 'Microsoft YaHei';
                    font-size: 12px;
                }
            """)

    def paintEvent(self, event):
        """自定义绘制事件 - 绘制完全无阴影的圆角背景"""
        try:
            from PyQt5.QtGui import QPainter, QBrush, QPen, QColor, QPainterPath
            from PyQt5.QtCore import Qt, QRectF

            painter = QPainter(self)
            # 启用高质量抗锯齿
            painter.setRenderHint(QPainter.Antialiasing, True)
            painter.setRenderHint(QPainter.HighQualityAntialiasing, True)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, True)

            # 清除背景，确保透明
            painter.fillRect(self.rect(), QColor(0, 0, 0, 0))

            # 创建圆角路径
            path = QPainterPath()
            rect = QRectF(self.rect()).adjusted(1.5, 1.5, -1.5, -1.5)  # 为边框留出空间
            path.addRoundedRect(rect, 12, 12)  # 12px圆角

            # 设置裁剪区域，确保只在圆角区域内绘制
            painter.setClipPath(path)

            # 绘制背景
            background_color = QColor(255, 255, 255, 245)  # 白色背景
            painter.fillPath(path, QBrush(background_color))

            # 重置裁剪区域
            painter.setClipping(False)

            # 绘制边框
            border_color = QColor(229, 231, 235, 255)  # 灰色边框
            pen = QPen(border_color, 2)
            pen.setJoinStyle(Qt.RoundJoin)  # 圆角连接
            pen.setCapStyle(Qt.RoundCap)    # 圆角端点
            painter.setPen(pen)
            painter.setBrush(Qt.NoBrush)
            painter.drawPath(path)

            painter.end()

            # 调用父类的paintEvent来绘制文本
            super().paintEvent(event)

        except Exception as e:
            print(f"⚠️ 自定义绘制失败: {str(e)}")
            # 如果自定义绘制失败，使用默认绘制
            super().paintEvent(event)

    def show_shop_detail(self, shop_name, pos):
        """显示店铺详情信息"""
        try:
            # 从账号管理.json文件中获取店铺信息
            shop_info = self.get_shop_info_from_config(shop_name)

            if shop_info:
                # 构建详情信息文本
                detail_text = self.format_shop_detail(shop_info)
                self.setText(detail_text)

                # 调整位置，确保不超出屏幕边界
                self.adjustSize()
                screen_geometry = QApplication.desktop().screenGeometry()

                # 计算最佳显示位置
                x = pos.x() + 15  # 鼠标右侧15像素
                y = pos.y() - 10  # 鼠标上方10像素

                # 检查右边界
                if x + self.width() > screen_geometry.width():
                    x = pos.x() - self.width() - 15

                # 检查下边界
                if y + self.height() > screen_geometry.height():
                    y = pos.y() - self.height() - 15

                # 检查上边界
                if y < 0:
                    y = pos.y() + 25

                # 检查左边界
                if x < 0:
                    x = 15

                self.move(x, y)
                self.show()
            else:
                self.hide()

        except Exception as e:
            print(f"显示店铺详情时出错: {str(e)}")
            self.hide()

    def get_shop_info_from_config(self, shop_name):
        """从配置文件中获取店铺信息"""
        try:
            # 如果店铺名称包含店铺分（如 "迪娜精品女鞋(4.04)"），需要去掉店铺分部分
            if '(' in shop_name and shop_name.endswith(')'):
                # 提取括号前的店铺名称
                clean_shop_name = shop_name.split('(')[0].strip()
                print(f"店铺详情查询：原始店铺名称='{shop_name}'，清理后='{clean_shop_name}'")
            else:
                clean_shop_name = shop_name
                print(f"店铺详情查询：店铺名称='{shop_name}'（无需清理）")

            # 使用正确的路径处理函数，确保在打包后也能正确找到配置文件
            config_path = get_config_path('账号管理.json')

            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                return None

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 在data数组中查找匹配的店铺（使用清理后的店铺名称）
            if 'data' in config_data:
                for shop_data in config_data['data']:
                    if shop_data.get('店铺名称') == clean_shop_name:
                        print(f"成功找到店铺信息: {clean_shop_name}")
                        return shop_data

            print(f"未找到店铺信息: {clean_shop_name}")
            return None

        except Exception as e:
            print(f"读取店铺配置文件时出错: {str(e)}")
            return None

    def format_shop_detail(self, shop_info):
        """格式化店铺详情信息"""
        try:
            details = []

            # 店铺名称 - 使用更大的字体和背景色，并在后面添加姓名
            shop_name = shop_info.get('店铺名称', '未知')
            # 获取姓名字段，如果存在则添加到店铺名称后面
            name = shop_info.get('姓名', '')
            if name and name.strip():
                display_name = f"{shop_name}[{name.strip()}]"
            else:
                display_name = shop_name

            details.append(f"""
                <div style='
                    font-size: 15px;
                    font-weight: bold;
                    color: #1e40af;
                    background-color: #eff6ff;
                    margin-bottom: 12px;
                    border-radius: 8px;
                    padding: 8px 12px;
                '>{display_name}</div>
            """)

            # 在售
            zai_shou = shop_info.get('在售', '0/0')
            details.append(f"""
                <div style='margin: 6px 0; padding: 4px 8px; background-color: #f0fdf4; border-radius: 6px;'>
                    <span style='color: #1f2937; font-weight: 600;'>在售:</span>
                    <span style='color: #059669; font-weight: 500;'>{zai_shou}</span>
                </div>
            """)

            # 店铺分
            dian_pu_fen = shop_info.get('店铺分', '0')
            details.append(f"""
                <div style='margin: 6px 0; padding: 4px 8px; background-color: #fef2f2; border-radius: 6px;'>
                    <span style='color: #1f2937; font-weight: 600;'>店铺分:</span>
                    <span style='color: #dc2626; font-weight: 500;'>{dian_pu_fen}</span>
                </div>
            """)

            # 邀约信息
            yao_yue_info = shop_info.get('邀约信息', '暂无权限')
            details.append(f"""
                <div style='margin: 6px 0; padding: 4px 8px; background-color: #faf5ff; border-radius: 6px;'>
                    <span style='color: #1f2937; font-weight: 600;'>邀约信息:</span>
                    <span style='color: #7c3aed; font-weight: 500;'>{yao_yue_info}</span>
                </div>
            """)

            # 保证金
            bao_zheng_jin = shop_info.get('保证金', '0/500')
            details.append(f"""
                <div style='margin: 6px 0; padding: 4px 8px; background-color: #fff7ed; border-radius: 6px;'>
                    <span style='color: #1f2937; font-weight: 600;'>保证金:</span>
                    <span style='color: #ea580c; font-weight: 500;'>{bao_zheng_jin}</span>
                </div>
            """)

            # 运费
            yun_fei = shop_info.get('运费', '未开通')
            details.append(f"""
                <div style='margin: 6px 0; padding: 4px 8px; background-color: #ecfeff; border-radius: 6px;'>
                    <span style='color: #1f2937; font-weight: 600;'>运费:</span>
                    <span style='color: #0891b2; font-weight: 500;'>{yun_fei}</span>
                </div>
            """)

            # 付款30天
            fu_kuan_30 = shop_info.get('付款30天', '0')
            details.append(f"""
                <div style='margin: 6px 0; padding: 4px 8px; background-color: #f0fdf4; border-radius: 6px;'>
                    <span style='color: #1f2937; font-weight: 600;'>付款30天:</span>
                    <span style='color: #16a34a; font-weight: 500;'>{fu_kuan_30}</span>
                </div>
            """)

            # 复制记录
            fu_zhi_record = shop_info.get('复制记录', '无记录')
            details.append(f"""
                <div style='margin: 6px 0; padding: 4px 8px; background-color: #faf5ff; border-radius: 6px;'>
                    <span style='color: #1f2937; font-weight: 600;'>复制记录:</span>
                    <span style='color: #9333ea; font-weight: 500;'>{fu_zhi_record}</span>
                </div>
            """)

            return "".join(details)

        except Exception as e:
            print(f"格式化店铺详情时出错: {str(e)}")
            return f"""
                <div style='color: #dc2626; background-color: #fef2f2; padding: 8px; border-radius: 6px;'>
                    <b>店铺名称:</b> {shop_info.get('店铺名称', '未知')}<br>
                    <b>详情:</b> 信息获取失败
                </div>
            """

class ImagePreviewTooltip(QLabel):
    """图片预览组件类"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
        self.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        self.setFixedSize(110, 110)  # 100x100 图片 + 5px padding
        self.setAlignment(Qt.AlignCenter)
        self.hide()

        # 初始化图片加载器为None
        self.image_loader = None

        # 创建延迟显示定时器
        self.show_timer = QTimer()
        self.show_timer.setSingleShot(True)
        self.show_timer.timeout.connect(self._delayed_show_image)

        # 创建自动隐藏定时器（5秒后自动隐藏）
        self.hide_timer = QTimer()
        self.hide_timer.setSingleShot(True)
        self.hide_timer.timeout.connect(self.safe_hide)

        # 当前要显示的图片URL和位置
        self.pending_url = None
        self.pending_pos = None

        # 简单的图片缓存，避免重复下载
        self.image_cache = {}

        # 默认显示加载中的文本
        self.setText("加载中...")
        self.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 5px;
                color: #666666;
                font-size: 12px;
            }
        """)

    def show_image_preview(self, image_url, pos):
        """显示图片预览（使用延迟显示避免频繁创建线程）"""
        try:
            if not image_url or not image_url.strip():
                self.safe_hide()
                return

            # 如果URL相同且已经在显示，不需要重新加载
            if (hasattr(self, 'current_url') and
                self.current_url == image_url and
                self.isVisible()):
                print(f"图片已在显示，跳过重复加载: {image_url}")
                return

            # 如果待显示的URL相同，也不需要重新设置
            if (hasattr(self, 'pending_url') and
                self.pending_url == image_url and
                self.show_timer.isActive()):
                print(f"图片已在等待显示，跳过重复设置: {image_url}")
                return

            # 立即停止当前的所有定时器和线程
            self.show_timer.stop()
            self.hide_timer.stop()  # 停止自动隐藏定时器
            self.stop_current_loader()

            # 保存要显示的URL和位置
            self.pending_url = image_url
            self.pending_pos = pos

            # 延迟300ms显示，减少延迟时间提高响应速度
            self.show_timer.start(300)
            print(f"设置图片预览延迟显示: {image_url}")

        except Exception as e:
            print(f"显示图片预览时出错: {str(e)}")
            self.safe_hide()

    def _delayed_show_image(self):
        """延迟显示图片的实际实现"""
        try:
            if not self.pending_url or not self.pending_pos:
                return

            # 记录当前URL
            self.current_url = self.pending_url

            # 显示加载中状态
            self.setText("加载中...")
            self.setStyleSheet("""
                QLabel {
                    background-color: #ffffff;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    padding: 5px;
                    color: #666666;
                    font-size: 12px;
                }
            """)

            # 调整位置
            self.adjustSize()
            screen_geometry = QApplication.desktop().screenGeometry()

            # 计算最佳显示位置
            x = self.pending_pos.x() + 15  # 鼠标右侧15像素
            y = self.pending_pos.y() - 10  # 鼠标上方10像素

            # 检查右边界
            if x + self.width() > screen_geometry.width():
                x = self.pending_pos.x() - self.width() - 15

            # 检查下边界
            if y + self.height() > screen_geometry.height():
                y = self.pending_pos.y() - self.height() - 15

            # 检查上边界
            if y < 0:
                y = self.pending_pos.y() + 25

            # 检查左边界
            if x < 0:
                x = 15

            self.move(x, y)
            self.show()

            # 启动5秒自动隐藏定时器
            self.hide_timer.start(5000)  # 5秒后自动隐藏

            # 异步加载图片
            self.load_image_async(self.pending_url)

        except Exception as e:
            print(f"延迟显示图片时出错: {str(e)}")
            self.safe_hide()

    def stop_current_loader(self):
        """停止当前的图片加载线程（强制快速停止）"""
        try:
            if self.image_loader is not None:
                # 立即请求停止线程
                self.image_loader.stop()

                # 断开所有信号连接，避免回调
                try:
                    self.image_loader.image_loaded.disconnect()
                    self.image_loader.load_failed.disconnect()
                    self.image_loader.finished.disconnect()
                except:
                    pass  # 忽略断开连接的错误

                # 强制终止线程
                if self.image_loader.isRunning():
                    self.image_loader.quit()
                    # 如果线程仍在运行，强制终止
                    if self.image_loader.isRunning():
                        self.image_loader.terminate()

                # 立即标记为删除，让Qt的事件循环处理清理
                self.image_loader.deleteLater()
                self.image_loader = None
                print("图片加载线程已强制停止")
        except Exception as e:
            print(f"停止图片加载线程时出错: {str(e)}")
            # 即使出错也要清空引用
            self.image_loader = None

    def load_image_async(self, image_url):
        """异步加载图片（支持缓存）"""
        try:
            # 检查缓存中是否已有该图片
            if image_url in self.image_cache:
                print(f"从缓存加载图片: {image_url}")
                self.on_image_loaded(self.image_cache[image_url])
                return

            # 创建图片加载线程
            self.image_loader = ImageLoader(image_url)
            self.image_loader.image_loaded.connect(self.on_image_loaded_with_cache)
            self.image_loader.load_failed.connect(self.on_load_failed)
            # 线程结束后自动清理
            self.image_loader.finished.connect(self.cleanup_loader)
            self.image_loader.start()

        except Exception as e:
            print(f"异步加载图片时出错: {str(e)}")
            self.on_load_failed("加载失败")

    def on_image_loaded_with_cache(self, pixmap):
        """图片加载成功回调（带缓存）"""
        try:
            # 将图片添加到缓存（限制缓存大小）
            if len(self.image_cache) < 20:  # 最多缓存20张图片
                if hasattr(self, 'pending_url') and self.pending_url:
                    self.image_cache[self.pending_url] = pixmap

            # 调用原来的处理方法
            self.on_image_loaded(pixmap)
        except Exception as e:
            print(f"缓存图片时出错: {str(e)}")
            self.on_image_loaded(pixmap)

    def cleanup_loader(self):
        """清理图片加载线程"""
        try:
            if self.image_loader is not None:
                self.image_loader.deleteLater()
                self.image_loader = None
        except Exception as e:
            print(f"清理图片加载线程时出错: {str(e)}")

    def safe_hide(self):
        """安全隐藏方法，确保线程和定时器正确停止"""
        try:
            print("开始安全隐藏图片预览")
            # 立即停止所有定时器
            if hasattr(self, 'show_timer'):
                self.show_timer.stop()
            if hasattr(self, 'hide_timer'):
                self.hide_timer.stop()

            # 强制停止当前的图片加载线程
            self.stop_current_loader()

            # 清空待显示的数据
            self.pending_url = None
            self.pending_pos = None
            # 清空当前URL
            self.current_url = None

            # 隐藏组件
            if self.isVisible():
                super().hide()
                print("图片预览已隐藏")
            else:
                print("图片预览已经隐藏")

        except Exception as e:
            print(f"安全隐藏时出错: {str(e)}")
            # 即使出错也要隐藏组件
            try:
                super().hide()
            except:
                pass

    def hide(self):
        """重写hide方法，确保隐藏时停止图片加载"""
        self.safe_hide()

    def on_image_loaded(self, pixmap):
        """图片加载成功回调"""
        try:
            # 缩放图片到100x100
            scaled_pixmap = pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            self.setText("")  # 清除文本
            self.setStyleSheet("""
                QLabel {
                    background-color: #ffffff;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    padding: 5px;
                }
            """)
        except Exception as e:
            print(f"设置图片时出错: {str(e)}")
            self.on_load_failed("显示失败")

    def on_load_failed(self, error_msg):
        """图片加载失败回调"""
        self.setText("加载失败")
        self.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 5px;
                color: #dc2626;
                font-size: 12px;
            }
        """)

class ImageLoader(QThread):
    """图片加载线程类（轻量级，非阻塞）"""
    image_loaded = pyqtSignal(QPixmap)
    load_failed = pyqtSignal(str)

    def __init__(self, image_url):
        super().__init__()
        self.image_url = image_url
        self._stop_requested = False
        # 设置线程为守护线程，程序退出时自动结束
        self.setTerminationEnabled(True)

    def stop(self):
        """请求停止线程（非阻塞）"""
        self._stop_requested = True

    def run(self):
        """线程运行方法"""
        try:
            # 快速检查是否被请求停止
            if self._stop_requested:
                print("图片加载线程在开始时被停止")
                return

            # 下载图片，使用更短的超时时间，跳过SSL验证
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            # 在网络请求前再次检查
            if self._stop_requested:
                print("图片加载线程在网络请求前被停止")
                return

            response = requests.get(
                self.image_url,
                timeout=3,  # 减少超时时间到3秒
                stream=True,
                headers=headers,
                verify=False  # 跳过SSL证书验证
            )
            response.raise_for_status()

            # 在处理响应前检查是否被请求停止
            if self._stop_requested:
                print("图片加载线程在处理响应前被停止")
                return

            # 创建QPixmap
            pixmap = QPixmap()
            if pixmap.loadFromData(response.content):
                if not self._stop_requested:
                    self.image_loaded.emit(pixmap)
                    print("图片加载成功并发送信号")
            else:
                if not self._stop_requested:
                    self.load_failed.emit("图片格式不支持")

        except requests.exceptions.Timeout:
            if not self._stop_requested:
                self.load_failed.emit("加载超时")
        except requests.exceptions.RequestException as e:
            if not self._stop_requested:
                self.load_failed.emit(f"网络错误: {str(e)}")
        except Exception as e:
            if not self._stop_requested:
                self.load_failed.emit(f"未知错误: {str(e)}")
        finally:
            # 确保线程结束时清理状态
            print(f"图片加载线程结束，停止状态: {self._stop_requested}")

    def __del__(self):
        """析构方法，确保线程安全结束"""
        try:
            if self.isRunning():
                self._stop_requested = True
                self.quit()
                # 不等待，让Qt自动处理
        except:
            pass

class ImageCopyThread(QThread):
    """图片复制线程类"""
    copy_success = pyqtSignal()
    copy_failed = pyqtSignal(str)

    def __init__(self, image_url):
        super().__init__()
        self.image_url = image_url
        self._stop_requested = False
        # 设置线程为守护线程，程序退出时自动结束
        self.setTerminationEnabled(True)

    def stop(self):
        """请求停止线程（非阻塞）"""
        self._stop_requested = True

    def run(self):
        """线程运行方法"""
        try:
            # 快速检查是否被请求停止
            if self._stop_requested:
                return

            # 下载图片，跳过SSL验证
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(
                self.image_url,
                timeout=5,
                headers=headers,
                verify=False  # 跳过SSL证书验证
            )
            response.raise_for_status()

            # 再次快速检查是否被请求停止
            if self._stop_requested:
                return

            # 创建QPixmap
            pixmap = QPixmap()
            if pixmap.loadFromData(response.content):
                if not self._stop_requested:
                    # 复制图片到剪贴板
                    clipboard = QApplication.clipboard()
                    clipboard.setPixmap(pixmap)
                    self.copy_success.emit()
            else:
                if not self._stop_requested:
                    self.copy_failed.emit("图片格式不支持")

        except requests.exceptions.Timeout:
            if not self._stop_requested:
                self.copy_failed.emit("下载超时")
        except requests.exceptions.RequestException as e:
            if not self._stop_requested:
                self.copy_failed.emit(f"网络错误: {str(e)}")
        except Exception as e:
            if not self._stop_requested:
                self.copy_failed.emit(f"未知错误: {str(e)}")

    def __del__(self):
        """析构方法，确保线程安全结束"""
        try:
            if self.isRunning():
                self._stop_requested = True
                self.quit()
                # 不等待，让Qt自动处理
        except:
            pass

class ImageCopyThread(QThread):
    """图片复制线程类"""
    image_downloaded = pyqtSignal(QPixmap)  # 发送下载的图片到主线程
    copy_failed = pyqtSignal(str)

    def __init__(self, image_url):
        super().__init__()
        self.image_url = image_url
        self._stop_requested = False
        # 设置线程为守护线程，程序退出时自动结束
        self.setTerminationEnabled(True)

    def stop(self):
        """请求停止线程（非阻塞）"""
        self._stop_requested = True

    def run(self):
        """线程运行方法"""
        try:
            # 快速检查是否被请求停止
            if self._stop_requested:
                return

            # 下载图片，跳过SSL验证
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(
                self.image_url,
                timeout=5,
                headers=headers,
                verify=False  # 跳过SSL证书验证
            )
            response.raise_for_status()

            # 再次快速检查是否被请求停止
            if self._stop_requested:
                return

            # 创建QPixmap
            pixmap = QPixmap()
            if pixmap.loadFromData(response.content):
                if not self._stop_requested:
                    # 发送图片到主线程进行剪贴板操作
                    self.image_downloaded.emit(pixmap)
            else:
                if not self._stop_requested:
                    self.copy_failed.emit("图片格式不支持")

        except requests.exceptions.Timeout:
            if not self._stop_requested:
                self.copy_failed.emit("下载超时")
        except requests.exceptions.RequestException as e:
            if not self._stop_requested:
                self.copy_failed.emit(f"网络错误: {str(e)}")
        except Exception as e:
            if not self._stop_requested:
                self.copy_failed.emit(f"未知错误: {str(e)}")

    def __del__(self):
        """析构方法，确保线程安全结束"""
        try:
            if self.isRunning():
                self._stop_requested = True
                self.quit()
                # 不等待，让Qt自动处理
        except:
            pass

class CheckBoxNumberWidget(QWidget):
    """包含复选框和序号的自定义Widget"""

    def __init__(self, number, parent=None):
        super().__init__(parent)
        self.number = number
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        # 创建复选框
        self.checkbox = QCheckBox()
        self.checkbox.setFixedSize(16, 16)  # 设置复选框大小

        # 创建序号标签
        self.number_label = QLabel(str(self.number))
        self.number_label.setAlignment(Qt.AlignCenter)
        self.number_label.setStyleSheet("font-size: 12px; color: #495057;")

        # 添加到布局
        layout.addWidget(self.checkbox)
        layout.addWidget(self.number_label)
        layout.addStretch()  # 添加弹性空间，让内容居中

        # 设置布局对齐方式
        layout.setAlignment(Qt.AlignCenter)

    def set_number(self, number):
        """设置序号"""
        self.number = number
        self.number_label.setText(str(number))

    def is_checked(self):
        """获取复选框状态"""
        return self.checkbox.isChecked()

    def set_checked(self, checked):
        """设置复选框状态"""
        self.checkbox.setChecked(checked)

def get_configured_server_url(default_url="http://150.158.14.242:8000"):
    """获取配置的服务器URL"""
    # 使用正确的路径处理函数，确保在打包后也能正确找到配置文件
    config_path = get_config_path('config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            url = config_data.get("database_server_url", default_url)
            if url == default_url and "database_server_url" not in config_data:
                print(f"警告: 在 {config_path} 中未找到 'database_server_url'，使用默认地址: {url}")
            return url
    except Exception as e:
        print(f"读取配置文件时发生错误: {e}，使用默认地址: {default_url}")
        return default_url

class DetailStatisticsWidget(QWidget):
    """详情统计组件类，可以嵌入到其他窗口中"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 保存父窗口引用，用于访问主窗口的数据
        self.parent = parent

        # 添加一个标志，用于判断是否已经统计过
        self.has_stats = False

        # 创建店铺详情信息框
        self.shop_detail_tooltip = ShopDetailTooltip(self)

        # 创建图片预览组件
        self.image_preview_tooltip = ImagePreviewTooltip(self)

        # 初始化UI
        self.init_ui()

    def __del__(self):
        """析构方法，确保线程和定时器正确清理"""
        try:
            if hasattr(self, 'image_preview_tooltip') and self.image_preview_tooltip:
                # 使用安全的停止方法
                self.image_preview_tooltip.safe_hide()
        except Exception as e:
            print(f"析构时清理线程出错: {str(e)}")

    def closeEvent(self, event):
        """重写关闭事件，确保线程和定时器正确停止"""
        try:
            # 停止图片预览线程和定时器
            if hasattr(self, 'image_preview_tooltip') and self.image_preview_tooltip:
                self.image_preview_tooltip.safe_hide()
        except Exception as e:
            print(f"关闭时清理线程出错: {str(e)}")
        finally:
            # 调用父类的关闭事件
            super().closeEvent(event)

    def init_ui(self):
        """初始化用户界面"""
        # 设置样式 - 去掉蓝色背景，优化滚动条样式
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
            QPushButton {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                color: #495057;
                padding: 3px 8px;
                font-size: 12px;
                font-family: 'Microsoft YaHei';
                min-height: 24px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #ced4da;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
            QLineEdit {
                border: 1px solid #ced4da;
                border-radius: 3px;
                padding: 3px 5px;
                background: white;
                min-height: 24px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QTableWidget {
                background-color: white;
                gridline-color: #e9ecef;
                border-top: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                selection-background-color: #4e73df;  /* 选中项背景色 */
                selection-color: white;  /* 选中项文字颜色 */
            }
            /* 为第一个表格添加左边框 */
            QTableWidget#table1 {
                border-left: 1px solid #dee2e6;
                border-right: none;
            }
            /* 为中间表格设置边框 */
            QTableWidget#table2 {
                border-left: 1px solid #dee2e6;
                border-right: none;
            }
            QTableWidget#table3 {
                border-left: 1px solid #dee2e6;
                border-right: none;
            }
            /* 为最后一个表格添加右边框 */
            QTableWidget#table4 {
                border-left: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                border-left: none;
                padding: 3px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                font-weight: bold;
            }
            /* 为第一列表头添加左边框 */
            QHeaderView::section:first {
                border-left: 1px solid #dee2e6;
            }

            /* 右键菜单样式 */
            QMenu {
                background-color: white;
                border: 1px solid #d1d3e2;
                border-radius: 4px;
                padding: 5px 0;
                margin: 2px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QMenu::item {
                padding: 6px 25px 6px 20px;
                border: 1px solid transparent;
                color: #3a3b45;
            }
            QMenu::item:selected {
                background-color: #4e73df;
                color: white;
            }
            QMenu::separator {
                height: 1px;
                background-color: #e3e6f0;
                margin: 4px 10px;
            }
            QMenu::indicator {
                width: 13px;
                height: 13px;
            }
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #495057;
            }

            /* 滚动条样式优化 */
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 8px;
                margin: 0px 0px 0px 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                min-height: 30px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }

            /* 水平滚动条样式 */
            QScrollBar:horizontal {
                border: none;
                background: #f0f0f0;
                height: 8px;
                margin: 0px 0px 0px 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:horizontal {
                background: #c0c0c0;
                min-width: 30px;
                border-radius: 4px;
            }
            QScrollBar::handle:horizontal:hover {
                background: #a0a0a0;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
                background: none;
            }
        """)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 0, 5, 5)

        # 顶部控制区域 - 按四个表格分区域
        top_frame = QFrame()
        top_frame.setMaximumHeight(45)
        top_layout = QHBoxLayout(top_frame)
        top_layout.setContentsMargins(5, 0, 5, 0)  # 设置下外边距为10像素
        top_layout.setSpacing(15)  # 与表格区域间距相同

        # 表格1对应的顶部区域
        table1_top_widget = QWidget()
        table1_top_layout = QHBoxLayout(table1_top_widget)
        table1_top_layout.setContentsMargins(0, 0, 0, 0)
        table1_top_layout.setSpacing(5)

        # 统计按钮
        stats_btn = QPushButton("统计")
        stats_btn.setFixedWidth(60)
        stats_btn.clicked.connect(self.on_stats_button_clicked)  # 连接点击事件
        table1_top_layout.addWidget(stats_btn)

        # 导出按钮（原查询按钮）
        export_btn = QPushButton("导出")
        export_btn.setFixedWidth(60)
        table1_top_layout.addWidget(export_btn)

        # 添加弹性空间
        table1_top_layout.addStretch()

        # 设置表格1顶部区域的固定宽度为245像素（与表格1相同）
        table1_top_widget.setFixedWidth(245)

        # 表格2对应的顶部区域
        table2_top_widget = QWidget()
        table2_top_layout = QHBoxLayout(table2_top_widget)
        table2_top_layout.setContentsMargins(0, 0, 0, 0)
        table2_top_layout.setSpacing(5)

        # 这里可以添加表格2对应的控件
        # 暂时为空，添加弹性空间
        table2_top_layout.addStretch()

        # 设置表格2顶部区域的固定宽度为320像素（与表格2相同）
        table2_top_widget.setFixedWidth(320)

        # 表格3对应的顶部区域
        table3_top_widget = QWidget()
        table3_top_layout = QHBoxLayout(table3_top_widget)
        table3_top_layout.setContentsMargins(0, 0, 0, 0)
        table3_top_layout.setSpacing(5)

        # 订单数
        order_count_label = QLabel("订单数:")
        table3_top_layout.addWidget(order_count_label)

        order_count_edit = QLineEdit()
        order_count_edit.setFixedWidth(80)
        table3_top_layout.addWidget(order_count_edit)

        # 关键字
        keyword_label = QLabel("关键字:")
        table3_top_layout.addWidget(keyword_label)

        keyword_edit = QLineEdit()
        keyword_edit.setFixedWidth(100)
        table3_top_layout.addWidget(keyword_edit)

        # 退货率
        return_rate_label = QLabel("退货率:")
        table3_top_layout.addWidget(return_rate_label)

        return_rate_edit = QLineEdit()
        return_rate_edit.setFixedWidth(80)
        return_rate_edit.setPlaceholderText("如: <=5")
        table3_top_layout.addWidget(return_rate_edit)

        # 筛选按钮（移到退货率后面）
        filter_btn = QPushButton("筛选")
        filter_btn.setFixedWidth(60)
        table3_top_layout.addWidget(filter_btn)

        # 生成链接（原生成订单）
        generate_link_btn = QPushButton("生成链接")
        generate_link_btn.setFixedWidth(80)
        table3_top_layout.addWidget(generate_link_btn)

        # 添加弹性空间
        table3_top_layout.addStretch()

        # 设置表格3顶部区域的固定宽度为600像素（与表格3相同）
        table3_top_widget.setFixedWidth(600)

        # 表格4对应的顶部区域
        table4_top_widget = QWidget()
        table4_top_layout = QHBoxLayout(table4_top_widget)
        table4_top_layout.setContentsMargins(0, 0, 0, 0)
        table4_top_layout.setSpacing(5)

        # 这里可以添加表格4对应的控件
        # 暂时为空，添加弹性空间
        table4_top_layout.addStretch()

        # 设置表格4顶部区域的固定宽度为250像素（与表格4相同）
        table4_top_widget.setFixedWidth(250)

        # 将四个表格对应的顶部区域添加到顶部布局
        top_layout.addWidget(table1_top_widget)
        top_layout.addWidget(table2_top_widget)
        top_layout.addWidget(table3_top_widget)
        top_layout.addWidget(table4_top_widget)

        # 添加顶部区域到主布局
        main_layout.addWidget(top_frame)

        # 添加一个空白区域，增加顶部区域和表格区域之间的间距
        spacer = QSpacerItem( QSizePolicy.Minimum, QSizePolicy.Fixed)
        main_layout.addItem(spacer)

        # 创建四个表格区域（每个区域包含表格和底部统计标签），并列排列
        tables_layout = QHBoxLayout()
        tables_layout.setContentsMargins(0, 0, 0, 0)
        tables_layout.setSpacing(15)  # 设置表格区域之间的间距为15像素

        # 表格1
        table1 = QTableWidget()
        table1.setObjectName("table1")  # 设置对象名称，用于CSS选择器
        table1.setColumnCount(4)
        table1.setHorizontalHeaderLabels(["序号", "店铺名称", "订单数", "利润"])
        # 设置列宽模式
        table1.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 序号列固定宽度，不可拖动
        table1.horizontalHeader().setSectionResizeMode(1, QHeaderView.Interactive)  # 店铺名称列可拖动
        table1.horizontalHeader().setSectionResizeMode(2, QHeaderView.Interactive)  # 订单数列可拖动
        table1.horizontalHeader().setSectionResizeMode(3, QHeaderView.Interactive)  # 利润列可拖动
        table1.setColumnWidth(0, 40)  # 序号列固定宽度40像素
        table1.setColumnWidth(1, 80)  # 店铺名称列初始宽度80像素
        table1.setColumnWidth(2, 60)  # 订单数列初始宽度60像素
        table1.setColumnWidth(3, 65)  # 利润列初始宽度65像素
        table1.verticalHeader().setVisible(False)
        table1.setShowGrid(True)
        table1.setAlternatingRowColors(True)
        table1.setFixedHeight(545)  # 设置固定高度为545像素
        table1.setFixedWidth(245)  # 设置固定宽度为245像素（原来165 + 80）
        # 启用排序功能
        table1.setSortingEnabled(True)
        # 禁用序号列的排序
        table1.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 序号列固定宽度，不可拖动
        table1.horizontalHeader().setSortIndicatorShown(True)  # 显示排序指示器
        # 设置默认排序列为订单数列（第3列，索引为2），降序排序
        table1.horizontalHeader().setSortIndicator(2, Qt.DescendingOrder)
        # 连接排序信号，在排序后更新序号
        table1.horizontalHeader().sectionClicked.connect(lambda index: self.handle_table1_sort(index))

        # 表格2
        table2 = QTableWidget()
        table2.setObjectName("table2")  # 设置对象名称，用于CSS选择器
        table2.setColumnCount(6)
        table2.setHorizontalHeaderLabels(["序号", "上家店铺", "利润", "退货率", "订单数", "上家旺旺"])
        # 设置列宽模式
        table2.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 序号列固定宽度，不可拖动
        table2.horizontalHeader().setSectionResizeMode(1, QHeaderView.Interactive)  # 上家店铺列可拖动
        table2.horizontalHeader().setSectionResizeMode(2, QHeaderView.Interactive)  # 利润列可拖动
        table2.horizontalHeader().setSectionResizeMode(3, QHeaderView.Interactive)  # 退货率列可拖动
        table2.horizontalHeader().setSectionResizeMode(4, QHeaderView.Interactive)  # 订单数列可拖动
        table2.horizontalHeader().setSectionResizeMode(5, QHeaderView.Interactive)  # 上家旺旺列可拖动
        table2.setColumnWidth(0, 40)  # 序号列固定宽度40像素
        table2.setColumnWidth(1, 70)  # 上家店铺列初始宽度70像素
        table2.setColumnWidth(2, 50)  # 利润列初始宽度50像素
        table2.setColumnWidth(3, 80)  # 退货率列初始宽度80像素
        table2.setColumnWidth(4, 50)  # 订单数列初始宽度50像素
        table2.setColumnWidth(5, 70)  # 上家旺旺列初始宽度70像素
        table2.verticalHeader().setVisible(False)
        table2.setShowGrid(True)
        table2.setAlternatingRowColors(True)
        table2.setFixedHeight(545)  # 设置固定高度为545像素
        # 设置固定宽度，减少80像素
        table2.setFixedWidth(320)  # 设置一个合适的宽度，这个值可能需要根据实际情况调整
        # 启用排序功能
        table2.setSortingEnabled(True)
        # 禁用序号列的排序
        table2.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 序号列固定宽度，不可拖动
        table2.horizontalHeader().setSortIndicatorShown(True)  # 显示排序指示器
        # 设置默认排序列为利润列（第3列，索引为2），降序排序
        table2.horizontalHeader().setSortIndicator(2, Qt.DescendingOrder)
        # 连接排序信号，在排序后更新序号
        table2.horizontalHeader().sectionClicked.connect(lambda index: self.handle_table2_sort(index))

        # 表格3
        table3 = QTableWidget()
        table3.setObjectName("table3")  # 设置对象名称，用于CSS选择器
        table3.setColumnCount(16)  # 增加负利润列，总共16列
        table3.setHorizontalHeaderLabels(["序号", "店铺名称", "上家旺旺", "商品标题", "图片", "价格", "订单数", "退货率", "实际利润", "负利润", "利润", "运费", "利润率", "商品ID", "上家链接", "操作状态"])
        # 设置列宽可拖动
        table3.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 序号列固定宽度，不可拖动
        for i in range(1, 16):  # 其他列可拖动，现在是16列
            table3.horizontalHeader().setSectionResizeMode(i, QHeaderView.Interactive)

        # 设置每列的宽度
        table3.setColumnWidth(0, 40)  # 序号列宽40像素
        table3.setColumnWidth(1, 70)  # 店铺名称列宽70像素
        table3.setColumnWidth(2, 70)  # 上家旺旺列宽70像素
        table3.setColumnWidth(3, 70)  # 商品标题列宽70像素
        table3.setColumnWidth(4, 70)  # 图片列宽70像素
        table3.setColumnWidth(5, 70)  # 价格列宽70像素
        table3.setColumnWidth(6, 70)  # 订单数列宽70像素
        table3.setColumnWidth(7, 70)  # 退货率列宽70像素
        table3.setColumnWidth(8, 70)  # 实际利润列宽70像素
        table3.setColumnWidth(9, 70)  # 负利润列宽70像素
        table3.setColumnWidth(10, 70)  # 利润列宽70像素
        table3.setColumnWidth(11, 70)  # 运费列宽70像素
        table3.setColumnWidth(12, 70)  # 利润率列宽70像素
        table3.setColumnWidth(13, 70)  # 商品ID列宽70像素
        table3.setColumnWidth(14, 70)  # 上家链接列宽70像素
        table3.setColumnWidth(15, 70)  # 操作状态列宽70像素
        table3.verticalHeader().setVisible(False)
        table3.setShowGrid(True)
        table3.setAlternatingRowColors(True)
        table3.setFixedHeight(545)  # 设置固定高度为545像素
        table3.setFixedWidth(600)  # 设置固定宽度为600像素
        # 启用排序功能
        table3.setSortingEnabled(True)
        # 禁用序号列的排序
        table3.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 序号列固定宽度，不可拖动
        table3.horizontalHeader().setSortIndicatorShown(True)  # 显示排序指示器
        # 设置默认排序列为订单数列（第7列，索引为6），降序排序
        table3.horizontalHeader().setSortIndicator(6, Qt.DescendingOrder)
        # 连接排序信号，在排序后更新序号
        table3.horizontalHeader().sectionClicked.connect(lambda index: self.handle_table3_sort(index))

        # 表格4
        table4 = QTableWidget()
        table4.setObjectName("table4")  # 设置对象名称，用于CSS选择器
        table4.setColumnCount(4)  # 只需要4列：序号、达人ID、达人昵称、订单数
        table4.setHorizontalHeaderLabels(["序号", "达人ID", "达人昵称", "订单数"])
        # 设置列宽可拖动
        table4.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 序号列固定宽度，不可拖动
        for i in range(1, 4):  # 只有4列，所以循环到3
            table4.horizontalHeader().setSectionResizeMode(i, QHeaderView.Interactive)  # 其他列可拖动
        table4.setColumnWidth(0, 40)  # ID列宽40像素
        table4.setColumnWidth(1, 70)  # 达人ID列宽70像素
        table4.setColumnWidth(2, 70)  # 达人昵称列宽70像素
        table4.setColumnWidth(3, 70)  # 订单数列宽70像素
        table4.verticalHeader().setVisible(False)
        table4.setShowGrid(True)
        table4.setAlternatingRowColors(True)
        table4.setFixedHeight(545)  # 设置固定高度为545像素
        table4.setFixedWidth(250)  # 设置固定宽度为250像素
        # 启用排序功能
        table4.setSortingEnabled(True)
        # 禁用序号列的排序
        table4.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # 序号列固定宽度，不可拖动
        table4.horizontalHeader().setSortIndicatorShown(True)  # 显示排序指示器
        # 设置默认排序列为订单数列（第4列，索引为3），降序排序
        table4.horizontalHeader().setSortIndicator(3, Qt.DescendingOrder)
        # 连接排序信号，在排序后更新序号
        table4.horizontalHeader().sectionClicked.connect(lambda index: self.handle_table4_sort(index))

        # 创建四个表格区域，每个区域包含表格和底部统计标签

        # 表格1区域
        table1_area = QVBoxLayout()
        table1_area.setContentsMargins(0, 0, 0, 0)
        table1_area.setSpacing(5)

        # 创建表格1底部统计标签
        table1_stats_label = QLabel("店铺: 0 | 订单: 0 | 利润: 0")
        table1_stats_label.setAlignment(Qt.AlignCenter)
        table1_stats_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ddd; border-top: none;")

        # 将表格1和底部统计标签添加到表格1区域
        table1_area.addWidget(table1)
        table1_area.addWidget(table1_stats_label)

        # 创建一个容器控件来包含表格1区域
        table1_container = QWidget()
        table1_container.setLayout(table1_area)
        table1_container.setFixedWidth(245)  # 设置固定宽度为245像素（原来165 + 80）

        # 表格2区域
        table2_area = QVBoxLayout()
        table2_area.setContentsMargins(0, 0, 0, 0)
        table2_area.setSpacing(5)

        # 创建表格2底部统计标签
        table2_stats_label = QLabel("总店铺: 0 | 订单: 0 | 利润: 0")
        table2_stats_label.setAlignment(Qt.AlignCenter)
        table2_stats_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ddd; border-top: none;")

        # 将表格2和底部统计标签添加到表格2区域
        table2_area.addWidget(table2)
        table2_area.addWidget(table2_stats_label)

        # 创建一个容器控件来包含表格2区域
        table2_container = QWidget()
        table2_container.setLayout(table2_area)
        table2_container.setFixedWidth(320)  # 设置固定宽度为320像素（与表格2相同）

        # 表格3区域
        table3_area = QVBoxLayout()
        table3_area.setContentsMargins(0, 0, 0, 0)
        table3_area.setSpacing(5)

        # 创建表格3底部统计标签
        table3_stats_label = QLabel("商品: 0 | 订单: 0 | 利润: 0 | 实际利润: 0 | 负利润: 0")
        table3_stats_label.setAlignment(Qt.AlignCenter)
        table3_stats_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ddd; border-top: none;")

        # 将表格3和底部统计标签添加到表格3区域
        table3_area.addWidget(table3)
        table3_area.addWidget(table3_stats_label)

        # 创建一个容器控件来包含表格3区域
        table3_container = QWidget()
        table3_container.setLayout(table3_area)
        table3_container.setFixedWidth(600)  # 设置固定宽度为600像素

        # 表格4区域
        table4_area = QVBoxLayout()
        table4_area.setContentsMargins(0, 0, 0, 0)
        table4_area.setSpacing(5)

        # 创建表格4底部统计标签
        table4_stats_label = QLabel("达人: 0 | 订单: 0")
        table4_stats_label.setAlignment(Qt.AlignCenter)
        table4_stats_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ddd; border-top: none;")

        # 将表格4和底部统计标签添加到表格4区域
        table4_area.addWidget(table4)
        table4_area.addWidget(table4_stats_label)

        # 创建一个容器控件来包含表格4区域
        table4_container = QWidget()
        table4_container.setLayout(table4_area)
        table4_container.setFixedWidth(250)  # 设置固定宽度为250像素

        # 将四个表格区域添加到水平布局中
        tables_layout.addWidget(table1_container)

        # 添加表格2区域，不再需要拉伸因子
        tables_layout.addWidget(table2_container)

        tables_layout.addWidget(table3_container)
        tables_layout.addWidget(table4_container)

        # 添加水平布局到主布局
        main_layout.addLayout(tables_layout)

        # 添加弹性空间
        main_layout.addStretch(1)

        # 保存表格引用，以便在方法中访问
        self.table1 = table1
        self.table2 = table2
        self.table3 = table3
        self.table4 = table4

        # 保存统计标签引用，以便在方法中更新
        self.table1_stats_label = table1_stats_label
        self.table2_stats_label = table2_stats_label
        self.table3_stats_label = table3_stats_label
        self.table4_stats_label = table4_stats_label

        # 保存顶部控件引用
        self.stats_btn = stats_btn
        self.export_btn = export_btn
        self.order_count_label = order_count_label
        self.order_count_edit = order_count_edit
        self.filter_btn = filter_btn
        self.keyword_label = keyword_label
        self.keyword_edit = keyword_edit
        self.return_rate_label = return_rate_label
        self.return_rate_edit = return_rate_edit
        self.generate_link_btn = generate_link_btn

        # 保存布局容器引用
        self.table1_top_widget = table1_top_widget
        self.table2_top_widget = table2_top_widget
        self.table3_top_widget = table3_top_widget
        self.table4_top_widget = table4_top_widget

        # 设置表格右键菜单
        table1.setContextMenuPolicy(Qt.CustomContextMenu)
        table1.customContextMenuRequested.connect(self.show_table1_context_menu)

        table2.setContextMenuPolicy(Qt.CustomContextMenu)
        table2.customContextMenuRequested.connect(self.show_table2_context_menu)

        table3.setContextMenuPolicy(Qt.CustomContextMenu)
        table3.customContextMenuRequested.connect(self.show_table3_context_menu)

        table4.setContextMenuPolicy(Qt.CustomContextMenu)
        table4.customContextMenuRequested.connect(self.show_table4_context_menu)

        # 为表格1启用鼠标跟踪，以便处理鼠标悬停事件
        table1.setMouseTracking(True)
        table1.mouseMoveEvent = self.table1_mouse_move_event
        table1.leaveEvent = self.table1_leave_event

        # 为表格3启用行选择和双击事件，以便处理图片预览和复制
        table3.setSelectionBehavior(QAbstractItemView.SelectRows)  # 设置为行选择模式
        table3.itemSelectionChanged.connect(self.table3_selection_changed)  # 连接选择变化信号
        table3.mouseDoubleClickEvent = self.table3_double_click_event

        # 🔍 连接表格3的筛选按钮事件
        self.filter_btn.clicked.connect(self.on_table3_filter_clicked)

    def table1_mouse_move_event(self, event):
        """处理表格1的鼠标移动事件"""
        try:
            # 获取鼠标位置对应的单元格
            item = self.table1.itemAt(event.pos())
            if item:
                row = item.row()
                col = item.column()

                # 只在店铺名称列（第2列，索引为1）显示详情
                if col == 1:
                    shop_name_item = self.table1.item(row, 1)
                    if shop_name_item and shop_name_item.text():
                        shop_name = shop_name_item.text().strip()

                        # 将鼠标位置转换为全局坐标
                        global_pos = self.table1.mapToGlobal(event.pos())

                        # 显示店铺详情信息框
                        self.shop_detail_tooltip.show_shop_detail(shop_name, global_pos)
                        return

            # 如果不在店铺名称列，隐藏信息框
            self.shop_detail_tooltip.hide()

        except Exception as e:
            print(f"处理表格1鼠标移动事件时出错: {str(e)}")
            self.shop_detail_tooltip.hide()

    def table1_leave_event(self, event):
        """处理表格1的鼠标离开事件"""
        try:
            # 鼠标离开表格时隐藏详情信息框
            self.shop_detail_tooltip.hide()
        except Exception as e:
            print(f"处理表格1鼠标离开事件时出错: {str(e)}")

    def table3_selection_changed(self):
        """处理表格3的选择变化事件"""
        try:
            # 立即停止当前的图片显示进程，防止多个线程同时运行
            self.image_preview_tooltip.safe_hide()

            # 获取当前选中的行
            current_row = self.table3.currentRow()
            if current_row >= 0:
                # 获取图片列（第5列，索引为4）的数据
                image_item = self.table3.item(current_row, 4)
                if image_item and image_item.text():
                    image_url = image_item.text().strip()

                    # 获取表格的全局位置，在表格右侧显示图片预览
                    table_geometry = self.table3.geometry()
                    global_pos = self.table3.mapToGlobal(QPoint(table_geometry.width() + 10, 50))

                    # 显示图片预览
                    self.image_preview_tooltip.show_image_preview(image_url, global_pos)
                    return

            # 如果没有选中行或没有图片，确保预览框已隐藏
            # 这里不需要再次调用safe_hide()，因为开头已经调用过了

        except Exception as e:
            print(f"处理表格3选择变化事件时出错: {str(e)}")
            self.image_preview_tooltip.safe_hide()

    def table3_double_click_event(self, event):
        """处理表格3的双击事件"""
        try:
            # 获取双击位置对应的单元格
            item = self.table3.itemAt(event.pos())
            if item:
                row = item.row()
                col = item.column()

                # 只在图片列（第5列，索引为4）处理双击复制
                if col == 4:
                    image_item = self.table3.item(row, 4)
                    if image_item and image_item.text():
                        image_url = image_item.text().strip()

                        # 异步下载并复制图片到剪贴板
                        self.copy_image_to_clipboard(image_url)

        except Exception as e:
            print(f"处理表格3双击事件时出错: {str(e)}")

    def copy_image_to_clipboard(self, image_url):
        """复制图片到剪贴板"""
        try:
            if not image_url or not image_url.strip():
                print("图片URL为空，无法复制")
                return

            # 停止之前的复制线程（如果存在）
            if hasattr(self, 'image_copy_thread') and self.image_copy_thread:
                if self.image_copy_thread.isRunning():
                    self.image_copy_thread.stop()
                    self.image_copy_thread.quit()
                    # 断开信号连接
                    try:
                        self.image_copy_thread.image_downloaded.disconnect()
                        self.image_copy_thread.copy_failed.disconnect()
                    except:
                        pass
                    self.image_copy_thread.deleteLater()

            # 创建图片复制线程
            self.image_copy_thread = ImageCopyThread(image_url)
            self.image_copy_thread.image_downloaded.connect(self.on_image_downloaded)
            self.image_copy_thread.copy_failed.connect(self.on_image_copy_failed)
            # 线程结束后自动清理
            self.image_copy_thread.finished.connect(self.cleanup_copy_thread)
            self.image_copy_thread.start()

        except Exception as e:
            print(f"复制图片时出错: {str(e)}")

    def cleanup_copy_thread(self):
        """清理图片复制线程"""
        try:
            if hasattr(self, 'image_copy_thread') and self.image_copy_thread:
                self.image_copy_thread.deleteLater()
                self.image_copy_thread = None
        except Exception as e:
            print(f"清理图片复制线程时出错: {str(e)}")

    def on_image_downloaded(self, pixmap):
        """图片下载完成，在主线程中复制到剪贴板"""
        try:
            # 在主线程中操作剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setPixmap(pixmap)
            print("图片已成功复制到剪贴板")
        except Exception as e:
            print(f"复制图片到剪贴板时出错: {str(e)}")

    def on_image_copy_failed(self, error_msg):
        """图片复制失败回调"""
        print(f"复制图片失败: {error_msg}")

    def on_stats_button_clicked(self):
        """处理统计按钮点击事件"""
        try:
            print("\n\n===== 开始新的统计操作 =====")
            # 重置统计标志，确保每次点击都重新统计
            self.has_stats = False

            # 清空所有表格
            self.table1.setRowCount(0)
            self.table2.setRowCount(0)
            self.table3.setRowCount(0)
            self.table4.setRowCount(0)
            print("已清空所有表格")

            # 强制更新UI
            QApplication.processEvents()

            # 直接获取应用程序实例中的所有窗口，查找包含所需数据的窗口
            app = QApplication.instance()
            all_windows = app.allWidgets()  # 获取所有窗口和控件

            # 查找包含data_cache属性的窗口
            data_window = None
            for window in all_windows:
                if hasattr(window, 'data_cache') and hasattr(window, 'current_menu'):
                    data_window = window
                    print(f"找到包含data_cache的窗口: {window.__class__.__name__}")
                    break

            if not data_window:
                print("未找到包含data_cache的窗口")
                QMessageBox.warning(self, "提示", "无法获取数据，请确保已加载全部订单数据")
                return

            # 使用找到的窗口作为数据源
            parent_window = data_window
            print(f"使用 {parent_window.__class__.__name__} 作为数据源")

            # 打印窗口的属性，帮助调试
            if hasattr(parent_window, 'current_menu'):
                print(f"当前菜单: {parent_window.current_menu}")
            if hasattr(parent_window, 'data_cache'):
                print(f"数据缓存键: {list(parent_window.data_cache.keys())}")

            # 检查是否有全部订单数据
            if not hasattr(parent_window, 'data_cache') or "全部订单" not in parent_window.data_cache:
                print("数据源中没有全部订单数据")
                QMessageBox.warning(self, "提示", "没有全部订单数据，请先加载全部订单数据")
                return

            # 检查缓存数据是否为空
            if not parent_window.data_cache["全部订单"]:
                print("全部订单缓存数据为空")
                QMessageBox.warning(self, "提示", "全部订单数据为空，请先加载数据")
                return

            # 直接从缓存中获取数据进行统计
            print("直接从缓存中获取数据进行统计")
            # 创建缓存数据的深拷贝，避免修改原始数据
            import copy
            original_cache = parent_window.data_cache["全部订单"]
            print(f"原始缓存数据长度: {len(original_cache)}")

            # 检查原始缓存数据的完整性
            if len(original_cache) > 0:
                print(f"原始缓存第一条数据键: {list(original_cache[0].keys())}")
                print(f"原始缓存第一条数据值示例: {list(original_cache[0].values())[:3]}")

                # 检查关键字段是否存在
                key_fields = ["店铺", "订单备注", "商品标题", "实际利润", "最后状态"]
                for field in key_fields:
                    field_exists = field in original_cache[0]
                    field_value = original_cache[0].get(field, "不存在")
                    print(f"字段 '{field}' 存在: {field_exists}, 值: {field_value}")

                # 特别检查实际利润字段
                profit_field = "实际利润"
                if profit_field in original_cache[0]:
                    profit_value = original_cache[0].get(profit_field, "不存在")
                    print(f"实际利润字段值: {profit_value}, 类型: {type(profit_value)}")

                    # 尝试转换为浮点数
                    try:
                        profit_float = float(str(profit_value).replace(',', ''))
                        print(f"转换后的实际利润值: {profit_float}, 类型: {type(profit_float)}")
                    except Exception as e:
                        print(f"转换实际利润值时出错: {str(e)}")

                # 检查前5条数据的实际利润字段
                print("检查前5条数据的实际利润字段:")
                for i in range(min(5, len(original_cache))):
                    profit_value = original_cache[i].get(profit_field, "不存在")
                    print(f"  第{i+1}条数据的实际利润: {profit_value}, 类型: {type(profit_value)}")

            # 深拷贝缓存数据
            cache_data = copy.deepcopy(original_cache)
            print(f"深拷贝后缓存数据长度: {len(cache_data)}")

            # 设置统计标志
            self.has_stats = True

            # 打印缓存数据的前几条，用于调试
            if len(cache_data) > 0:
                print(f"缓存数据第一条的键: {list(cache_data[0].keys())}")
                print(f"缓存数据第一条的值: {cache_data[0]}")

            # 从缓存数据中提取店铺信息 - 表格1
            print("开始处理表格1数据...")
            shop_stats = {}
            for order in cache_data:
                shop_name = order.get("店铺", "")
                if not shop_name:
                    continue

                # 获取实际利润（保持负数为负数）
                actual_profit = 0
                profit_text = order.get("实际利润", "0")
                try:
                    # 尝试提取数字，保持正负号
                    if isinstance(profit_text, (int, float)):
                        actual_profit = float(profit_text)
                    else:
                        # 直接转换，保持正负号
                        actual_profit = float(str(profit_text).replace(',', ''))
                    print(f"店铺: {shop_name}, 实际利润文本: {profit_text}, 转换后: {actual_profit}")
                except Exception as e:
                    print(f"提取实际利润时出错: {str(e)}, 店铺: {shop_name}, 实际利润文本: {profit_text}")

                # 初始化店铺统计信息
                if shop_name not in shop_stats:
                    shop_stats[shop_name] = {
                        "order_count": 0,
                        "profit": 0
                    }
                    print(f"初始化店铺统计信息: {shop_name}")

                # 更新统计信息
                shop_stats[shop_name]["order_count"] += 1
                shop_stats[shop_name]["profit"] += actual_profit
                print(f"更新店铺统计信息: {shop_name}, 订单数: {shop_stats[shop_name]['order_count']}, 累计利润: {shop_stats[shop_name]['profit']}")

            if shop_stats:
                print(f"从缓存数据中成功提取了 {len(shop_stats)} 个店铺的订单数量和利润")
                # 显示表格1数据
                self.display_shop_stats(shop_stats)
                print("表格1数据处理完成")

                # 处理表格2数据 - 上家店铺信息
                print("开始处理表格2数据...")
                # 从缓存数据中提取上家店铺信息
                self.analyze_supplier_data_from_cache(cache_data)
                print("表格2数据处理完成")

                # 处理表格3数据 - 商品信息
                print("开始处理表格3数据...")
                # 从缓存数据中提取商品信息
                self.analyze_product_data_from_cache(cache_data)
                print("表格3数据处理完成")

                # 处理表格4数据 - 达人信息
                print("开始处理表格4数据...")
                # 从缓存数据中提取达人信息
                self.analyze_talent_data_from_cache(cache_data)
                print("表格4数据处理完成")

            # 设置统计标志为True，表示已经统计过
            self.has_stats = True
            print("统计完成，设置统计标志为True")

            # 强制更新UI
            QApplication.processEvents()

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"统计店铺订单数量时出错: {str(e)}")

    def analyze_supplier_data_from_cache(self, cache_data):
        """从缓存数据中分析上家店铺信息并显示在表格2中"""
        try:
            print("开始分析上家店铺数据...")
            print(f"传入的缓存数据长度: {len(cache_data)}")

            # 检查缓存数据的完整性
            if len(cache_data) > 0:
                print(f"缓存第一条数据键: {list(cache_data[0].keys())}")
                # 检查关键字段是否存在
                key_fields = ["订单备注", "最后状态", "实际利润"]
                for field in key_fields:
                    field_exists = field in cache_data[0]
                    field_value = cache_data[0].get(field, "不存在")
                    print(f"字段 '{field}' 存在: {field_exists}, 值: {field_value}")

            # 创建数据结构来存储上家店铺的统计信息
            supplier_stats = {}

            # 记录处理的订单数和跳过的订单数
            processed_count = 0
            skipped_count = 0
            empty_supplier_count = 0
            empty_remark_count = 0

            # 遍历所有订单数据
            for order_idx, order in enumerate(cache_data):
                try:
                    # 获取订单备注
                    remark_text = order.get("订单备注", "")
                    if not remark_text:
                        empty_remark_count += 1
                        if empty_remark_count <= 5:  # 只打印前5条，避免日志过多
                            print(f"警告: 订单 {order_idx} 没有订单备注")
                        continue

                    # 获取最后状态
                    last_status = order.get("最后状态", "")

                    # 获取实际利润（保持负数为负数）
                    actual_profit = 0
                    profit_text = order.get("实际利润", "0")
                    try:
                        # 尝试提取数字，保持正负号
                        if isinstance(profit_text, (int, float)):
                            actual_profit = float(profit_text)
                        else:
                            # 直接转换，保持正负号
                            actual_profit = float(str(profit_text).replace(',', ''))
                    except Exception as e:
                        print(f"提取实际利润时出错: {str(e)}, 订单 {order_idx}, 实际利润文本: {profit_text}")

                    # 提取上家店铺名称和上家旺旺
                    supplier_name = ""
                    supplier_wangwang = ""

                    if "上家店铺" in remark_text:
                        try:
                            start_idx = remark_text.find("上家店铺") + len("上家店铺")

                            # 方法1：如果有"/"分隔符，提取上家店铺和/之间的文字作为店铺名，/和利润之间作为旺旺
                            if "/" in remark_text[start_idx:] and "利润" in remark_text:
                                slash_idx = remark_text.find("/", start_idx)
                                profit_idx = remark_text.find("利润", start_idx)

                                if slash_idx > start_idx and profit_idx > slash_idx:
                                    # 提取店铺名称（上家店铺到/之间）
                                    supplier_name = remark_text[start_idx:slash_idx].strip()
                                    # 提取旺旺（/到利润之间）
                                    supplier_wangwang = remark_text[slash_idx + 1:profit_idx].strip()

                            # 方法2：如果没有"/"分隔符，直接提取上家店铺和利润之间的文字作为店铺名，旺旺为空
                            elif "利润" in remark_text:
                                profit_idx = remark_text.find("利润", start_idx)
                                if profit_idx > start_idx:
                                    # 提取店铺名称（上家店铺到利润之间）
                                    supplier_name = remark_text[start_idx:profit_idx].strip()
                                    # 旺旺为空
                                    supplier_wangwang = ""

                        except Exception as e:
                            print(f"提取上家店铺名称时出错: {str(e)}, 订单 {order_idx}, 订单备注: {remark_text}")

                    # 如果没有上家店铺名称，尝试使用"上家"关键字提取
                    if not supplier_name and "上家" in remark_text:
                        try:
                            start_idx = remark_text.find("上家") + len("上家")

                            # 方法1：如果有"/"分隔符
                            if "/" in remark_text[start_idx:] and "利润" in remark_text:
                                slash_idx = remark_text.find("/", start_idx)
                                profit_idx = remark_text.find("利润", start_idx)

                                if slash_idx > start_idx and profit_idx > slash_idx:
                                    supplier_name = remark_text[start_idx:slash_idx].strip()
                                    supplier_wangwang = remark_text[slash_idx + 1:profit_idx].strip()

                            # 方法2：如果没有"/"分隔符
                            elif "利润" in remark_text:
                                profit_idx = remark_text.find("利润", start_idx)
                                if profit_idx > start_idx:
                                    supplier_name = remark_text[start_idx:profit_idx].strip()
                                    supplier_wangwang = ""

                        except Exception as e:
                            print(f"提取上家店铺名称(备用方法)时出错: {str(e)}, 订单 {order_idx}, 订单备注: {remark_text}")

                    # 如果没有提取到上家店铺名称，则跳过
                    if not supplier_name:
                        empty_supplier_count += 1
                        if empty_supplier_count <= 5:  # 只打印前5条，避免日志过多
                            print(f"警告: 订单 {order_idx} 没有上家店铺名称, 订单备注: {remark_text}")
                        skipped_count += 1
                        continue

                    # 创建一个键，只使用供应商名称作为键，这样可以合并相同供应商的数据
                    supplier_key = supplier_name

                    # 初始化上家店铺统计信息
                    if supplier_key not in supplier_stats:
                        supplier_stats[supplier_key] = {
                            "supplier_name": supplier_name,
                            "supplier_wangwang": supplier_wangwang,
                            "order_count": 0,
                            "profit": 0,
                            "refund_count": 0,
                            "link": "",  # 链接暂时为空
                            "orders": [],  # 存储订单信息，用于计算退货率
                            "refunded_count": 0,  # 已退款订单数量
                        }

                    # 创建订单信息对象
                    order_info = {
                        "最后状态": last_status,
                        "实际利润": actual_profit
                    }

                    # 更新统计信息
                    supplier_stats[supplier_key]["order_count"] += 1
                    supplier_stats[supplier_key]["orders"].append(order_info)

                    # 累加利润（无论订单状态如何，都累加实际利润，保持负数为负数）
                    supplier_stats[supplier_key]["profit"] += actual_profit

                    # 统计退款数量
                    if last_status == "退款成功":
                        supplier_stats[supplier_key]["refund_count"] += 1

                    # 统计已退款订单数量
                    if last_status == "已退款":
                        supplier_stats[supplier_key]["refunded_count"] += 1

                    # 如果还没有设置旺旺，则设置
                    if not supplier_stats[supplier_key]["supplier_wangwang"] and supplier_wangwang:
                        supplier_stats[supplier_key]["supplier_wangwang"] = supplier_wangwang

                    processed_count += 1

                except Exception as e:
                    print(f"处理订单 {order_idx} 时出错: {str(e)}")
                    skipped_count += 1

            # 打印统计信息
            print(f"处理订单数: {processed_count}, 跳过订单数: {skipped_count}")
            print(f"空订单备注订单数: {empty_remark_count}, 空上家店铺名称订单数: {empty_supplier_count}")

            if len(supplier_stats) > 0:
                # 打印一些上家店铺统计信息的示例
                sample_keys = list(supplier_stats.keys())[:3]  # 取前3个作为示例
                print("上家店铺统计信息示例:")
                for key in sample_keys:
                    print(f"上家店铺: {key}")
                    print(f"  上家旺旺: {supplier_stats[key]['supplier_wangwang']}")
                    print(f"  订单数: {supplier_stats[key]['order_count']}")
                    print(f"  利润: {supplier_stats[key]['profit']}")
                    print(f"  退款数量: {supplier_stats[key]['refund_count']}")
                    print(f"  已退款订单数量: {supplier_stats[key]['refunded_count']}")

            # 显示统计结果到表格2
            print(f"准备显示 {len(supplier_stats)} 个上家店铺的统计结果")
            # 打印一些统计数据的示例，帮助调试
            if len(supplier_stats) > 0:
                sample_key = list(supplier_stats.keys())[0]
                print(f"示例上家店铺数据: {supplier_stats[sample_key]}")
            self.display_supplier_stats(supplier_stats)
            print(f"从缓存数据中成功分析了 {len(supplier_stats)} 个上家店铺的信息")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"从缓存数据分析上家店铺数据时出错: {str(e)}")

    def analyze_supplier_data(self, table_widget, row_count, col_count,
                             shop_col_index, order_remark_col_index,
                             actual_profit_col_index, last_status_col_index):
        """分析上家店铺数据并显示在表格2中"""
        try:
            # 如果找不到必要的列，则返回
            if order_remark_col_index == -1:
                print("未找到订单备注列，无法分析上家店铺数据")
                return

            # 创建数据结构来存储上家店铺的统计信息
            supplier_stats = {}

            # 遍历所有行，提取上家店铺信息
            for row in range(row_count):
                # 获取订单备注
                remark_item = table_widget.item(row, order_remark_col_index)
                if not remark_item:
                    continue

                remark_text = remark_item.text()

                # 获取最后状态（如果有）
                last_status = ""
                if last_status_col_index != -1:
                    status_item = table_widget.item(row, last_status_col_index)
                    if status_item:
                        last_status = status_item.text()

                # 获取实际利润（如果有）
                actual_profit = 0
                if actual_profit_col_index != -1:
                    profit_item = table_widget.item(row, actual_profit_col_index)
                    if profit_item:
                        profit_text = profit_item.text()
                        try:
                            # 尝试提取数字
                            if isinstance(profit_text, (int, float)):
                                actual_profit = float(profit_text)
                            else:
                                actual_profit = float(str(profit_text).replace(',', ''))
                        except Exception as e:
                            print(f"提取实际利润时出错: {str(e)}, 行 {row}, 实际利润文本: {profit_text}")

                # 提取上家店铺名称和上家旺旺
                supplier_name = ""
                supplier_wangwang = ""

                if "上家店铺" in remark_text:
                    try:
                        start_idx = remark_text.find("上家店铺") + len("上家店铺")

                        # 方法1：如果有"/"分隔符，提取上家店铺和/之间的文字作为店铺名，/和利润之间作为旺旺
                        if "/" in remark_text[start_idx:] and "利润" in remark_text:
                            slash_idx = remark_text.find("/", start_idx)
                            profit_idx = remark_text.find("利润", start_idx)

                            if slash_idx > start_idx and profit_idx > slash_idx:
                                # 提取店铺名称（上家店铺到/之间）
                                supplier_name = remark_text[start_idx:slash_idx].strip()
                                # 提取旺旺（/到利润之间）
                                supplier_wangwang = remark_text[slash_idx + 1:profit_idx].strip()

                        # 方法2：如果没有"/"分隔符，直接提取上家店铺和利润之间的文字作为店铺名，旺旺为空
                        elif "利润" in remark_text:
                            profit_idx = remark_text.find("利润", start_idx)
                            if profit_idx > start_idx:
                                # 提取店铺名称（上家店铺到利润之间）
                                supplier_name = remark_text[start_idx:profit_idx].strip()
                                # 旺旺为空
                                supplier_wangwang = ""

                    except Exception as e:
                        print(f"提取上家店铺名称时出错: {str(e)}, 行 {row}, 订单备注: {remark_text}")

                # 如果没有上家店铺名称，尝试使用"上家"关键字提取
                if not supplier_name and "上家" in remark_text:
                    try:
                        start_idx = remark_text.find("上家") + len("上家")

                        # 方法1：如果有"/"分隔符
                        if "/" in remark_text[start_idx:] and "利润" in remark_text:
                            slash_idx = remark_text.find("/", start_idx)
                            profit_idx = remark_text.find("利润", start_idx)

                            if slash_idx > start_idx and profit_idx > slash_idx:
                                supplier_name = remark_text[start_idx:slash_idx].strip()
                                supplier_wangwang = remark_text[slash_idx + 1:profit_idx].strip()

                        # 方法2：如果没有"/"分隔符
                        elif "利润" in remark_text:
                            profit_idx = remark_text.find("利润", start_idx)
                            if profit_idx > start_idx:
                                supplier_name = remark_text[start_idx:profit_idx].strip()
                                supplier_wangwang = ""

                    except Exception as e:
                        print(f"提取上家店铺名称(备用方法)时出错: {str(e)}, 行 {row}, 订单备注: {remark_text}")

                # 如果没有提取到上家店铺名称，则跳过
                if not supplier_name:
                    continue

                # 初始化上家店铺统计信息
                if supplier_name not in supplier_stats:
                    supplier_stats[supplier_name] = {
                        "supplier_name": supplier_name,
                        "supplier_wangwang": supplier_wangwang,
                        "order_count": 0,
                        "profit": 0,
                        "refund_count": 0,
                        "link": "",  # 链接暂时为空
                        "orders": [],  # 存储订单信息，用于计算退货率
                        "refunded_count": 0,  # 已退款订单数量
                    }

                # 创建订单信息对象
                order_info = {
                    "最后状态": last_status,
                    "实际利润": actual_profit
                }

                # 更新统计信息
                supplier_stats[supplier_name]["order_count"] += 1
                supplier_stats[supplier_name]["orders"].append(order_info)

                # 累加利润（无论订单状态如何，都累加实际利润，保持负数为负数）
                supplier_stats[supplier_name]["profit"] += actual_profit

                # 统计退款数量
                if last_status == "退款成功":
                    supplier_stats[supplier_name]["refund_count"] += 1

                # 统计已退款订单数量
                if last_status == "已退款":
                    supplier_stats[supplier_name]["refunded_count"] += 1

                # 如果还没有设置旺旺，则设置
                if not supplier_stats[supplier_name]["supplier_wangwang"] and supplier_wangwang:
                    supplier_stats[supplier_name]["supplier_wangwang"] = supplier_wangwang

            # 显示统计结果到表格2
            self.display_supplier_stats(supplier_stats)

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"分析上家店铺数据时出错: {str(e)}")

    def display_supplier_stats(self, supplier_stats):
        """将上家店铺统计结果显示在表格2中"""
        try:
            print(f"开始显示上家店铺统计结果，数据条数: {len(supplier_stats) if supplier_stats else 0}")

            # 清空表格
            self.table2.setRowCount(0)
            print("表格2已清空")

            # 强制更新UI
            QApplication.processEvents()

            # 如果没有数据，显示提示信息
            if not supplier_stats:
                print("没有上家店铺统计数据，显示提示信息")
                self.table2.setRowCount(1)
                self.table2.setItem(0, 0, QTableWidgetItem("1"))
                self.table2.setItem(0, 1, QTableWidgetItem("无数据"))
                self.table2.setItem(0, 2, QTableWidgetItem("0"))
                self.table2.setItem(0, 3, QTableWidgetItem("0%"))
                self.table2.setItem(0, 4, QTableWidgetItem("0"))
                self.table2.setItem(0, 5, QTableWidgetItem(""))

                # 强制更新UI
                QApplication.processEvents()
                return

            # 准备数据列表，以便一次性添加到表格
            table_data = []
            print(f"开始处理 {len(supplier_stats)} 个上家店铺的数据")

            # 打印所有键，帮助调试
            print(f"所有上家店铺键: {list(supplier_stats.keys())}")

            # 创建一个新的字典，用于合并相同供应商的数据
            merged_supplier_stats = {}

            # 合并相同供应商的数据
            for supplier_key, stats in supplier_stats.items():
                # 只使用上家店铺名称作为汇总字段
                supplier_name = stats["supplier_name"]
                supplier_info = supplier_name
                print(f"处理上家店铺: {supplier_name}, 键: {supplier_key}")

                if supplier_info not in merged_supplier_stats:
                    # 创建新的合并记录
                    merged_supplier_stats[supplier_info] = {
                        "supplier_name": stats["supplier_name"],
                        "supplier_wangwang": stats["supplier_wangwang"],
                        "order_count": 0,
                        "profit": 0,
                        "refund_count": 0,
                        "refunded_count": 0
                    }

                # 累加数据
                merged_supplier_stats[supplier_info]["order_count"] += stats["order_count"]
                merged_supplier_stats[supplier_info]["profit"] += stats["profit"]
                merged_supplier_stats[supplier_info]["refund_count"] += stats["refund_count"]
                merged_supplier_stats[supplier_info]["refunded_count"] += stats["refunded_count"]

            print(f"合并后的上家店铺数量: {len(merged_supplier_stats)}")

            # 处理合并后的数据
            for supplier_info, stats in merged_supplier_stats.items():
                try:
                    print(f"处理上家店铺: {supplier_info}")
                    print(f"  统计数据: {stats}")

                    # 获取已退款订单数量
                    refunded_count = stats.get("refunded_count", 0)
                    print(f"  已退款订单数量: {refunded_count}")

                    # 计算有效订单数（总订单数-已退款）
                    effective_orders = stats["order_count"] - refunded_count
                    print(f"  有效订单数: {effective_orders}")

                    # 计算退货率
                    refund_rate = 0
                    if effective_orders > 0:
                        refund_rate = (stats["refund_count"] / effective_orders) * 100
                    print(f"  退货率: {refund_rate:.2f}%")

                    # 创建一行数据
                    row_data = {
                        "supplier_name": stats["supplier_name"],
                        "profit": round(stats["profit"], 2),
                        "supplier_wangwang": stats["supplier_wangwang"],
                        "order_count": stats["order_count"],
                        "refund_rate": refund_rate
                    }
                    print(f"  行数据: {row_data}")
                    table_data.append(row_data)
                except Exception as e:
                    print(f"准备上家店铺数据时出错: {str(e)}, 上家店铺: {supplier_info}")
                    print(f"  统计数据: {stats}")
                    continue

            # 设置表格行数
            row_count = len(table_data)
            self.table2.setRowCount(row_count)
            print(f"设置表格2行数为: {row_count}")

            # 强制更新UI
            QApplication.processEvents()

            # 暂时禁用排序功能
            self.table2.setSortingEnabled(False)

            # 添加数据到表格
            for row_idx, row_data in enumerate(table_data):
                try:
                    # 序号列
                    num_item = QTableWidgetItem(str(row_idx + 1))
                    num_item.setTextAlignment(Qt.AlignCenter)
                    self.table2.setItem(row_idx, 0, num_item)

                    # 上家店铺列
                    supplier_item = QTableWidgetItem(row_data["supplier_name"])
                    self.table2.setItem(row_idx, 1, supplier_item)

                    # 利润列
                    profit_item = QTableWidgetItem()
                    profit_item.setData(Qt.DisplayRole, row_data["profit"])
                    profit_item.setTextAlignment(Qt.AlignCenter)
                    self.table2.setItem(row_idx, 2, profit_item)

                    # 退货率列
                    refund_item = QTableWidgetItem(f"{row_data['refund_rate']:.2f}%")
                    refund_item.setTextAlignment(Qt.AlignCenter)
                    # 存储原始数值用于排序
                    refund_item.setData(Qt.UserRole, row_data["refund_rate"])
                    self.table2.setItem(row_idx, 3, refund_item)

                    # 订单数列
                    count_item = QTableWidgetItem()
                    count_item.setData(Qt.DisplayRole, row_data["order_count"])
                    count_item.setTextAlignment(Qt.AlignCenter)
                    self.table2.setItem(row_idx, 4, count_item)

                    # 上家旺旺列
                    wangwang_item = QTableWidgetItem(row_data["supplier_wangwang"])
                    self.table2.setItem(row_idx, 5, wangwang_item)

                    # 每处理10行，强制更新一次UI
                    if (row_idx + 1) % 10 == 0:
                        QApplication.processEvents()
                        print(f"已处理 {row_idx + 1} 行数据")

                except Exception as e:
                    print(f"设置表格2行 {row_idx} 时出错: {str(e)}")
                    # 继续处理下一行，不中断

            # 重新启用排序功能
            self.table2.setSortingEnabled(True)

            # 按利润降序排序
            self.table2.sortItems(2, Qt.DescendingOrder)

            # 更新序号列
            self.update_table2_numbers(2)

            # 强制更新UI
            QApplication.processEvents()
            QApplication.processEvents()

            # 计算总店铺数、总订单数和总利润
            total_suppliers = len(table_data)
            total_orders = sum(row_data["order_count"] for row_data in table_data)
            total_profit = sum(row_data["profit"] for row_data in table_data)

            # 更新底部统计标签
            self.table2_stats_label.setText(f"总店铺: {total_suppliers} | 订单: {total_orders} | 利润: {total_profit:.2f}")

            print(f"成功统计 {row_count} 个上家店铺的数据，总订单数: {total_orders}，总利润: {total_profit:.2f}")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"显示上家店铺统计结果时出错: {str(e)}")

    def display_shop_stats(self, shop_stats):
        """将店铺统计结果显示在表格1中"""
        try:
            # 清空表格
            self.table1.setRowCount(0)

            # 强制更新UI
            QApplication.processEvents()

            # 如果没有数据，显示提示信息
            if not shop_stats:
                self.table1.setRowCount(1)
                self.table1.setItem(0, 0, QTableWidgetItem("1"))
                self.table1.setItem(0, 1, QTableWidgetItem("无数据"))
                self.table1.setItem(0, 2, QTableWidgetItem("0"))
                self.table1.setItem(0, 3, QTableWidgetItem("0"))

                # 强制更新UI
                QApplication.processEvents()
                return

            # 准备数据列表，以便一次性添加到表格
            table_data = []
            print(f"开始处理 {len(shop_stats)} 个店铺的数据")

            # 打印所有店铺统计信息，帮助调试
            for shop_name, stats in shop_stats.items():
                print(f"店铺: {shop_name}, 统计信息: {stats}")

            for shop_name, stats in shop_stats.items():
                try:
                    # 获取订单数和利润
                    order_count = stats["order_count"]
                    profit = stats.get("profit", 0)

                    # 确保利润是浮点数
                    if isinstance(profit, str):
                        try:
                            profit = float(profit.replace(',', ''))
                        except:
                            profit = 0

                    print(f"处理店铺: {shop_name}, 订单数: {order_count}, 利润: {profit}")

                    # 创建一行数据
                    row_data = {
                        "shop_name": shop_name,
                        "order_count": order_count,
                        "profit": round(profit, 2)
                    }
                    print(f"创建行数据: {row_data}")
                    table_data.append(row_data)
                except Exception as e:
                    print(f"准备店铺数据时出错: {str(e)}, 店铺: {shop_name}")
                    continue

            # 设置表格行数
            row_count = len(table_data)
            self.table1.setRowCount(row_count)
            print(f"设置表格1行数为: {row_count}")

            # 强制更新UI
            QApplication.processEvents()

            # 暂时禁用排序功能
            self.table1.setSortingEnabled(False)

            # 添加数据到表格
            for row_idx, row_data in enumerate(table_data):
                try:
                    print(f"添加表格1行 {row_idx + 1}: {row_data}")

                    # 序号列
                    num_item = QTableWidgetItem(str(row_idx + 1))
                    num_item.setTextAlignment(Qt.AlignCenter)
                    self.table1.setItem(row_idx, 0, num_item)

                    # 店铺名称列
                    shop_item = QTableWidgetItem(row_data["shop_name"])
                    self.table1.setItem(row_idx, 1, shop_item)

                    # 订单数列 - 设置为数值类型以便正确排序
                    count_item = QTableWidgetItem()
                    count_item.setData(Qt.DisplayRole, row_data["order_count"])  # 使用setData确保按数值排序
                    count_item.setTextAlignment(Qt.AlignCenter)
                    self.table1.setItem(row_idx, 2, count_item)

                    # 利润列 - 设置为数值类型以便正确排序
                    profit_item = QTableWidgetItem()
                    profit_value = row_data["profit"]
                    print(f"  利润值: {profit_value}, 类型: {type(profit_value)}")
                    profit_item.setData(Qt.DisplayRole, profit_value)  # 保留两位小数
                    profit_item.setTextAlignment(Qt.AlignCenter)
                    self.table1.setItem(row_idx, 3, profit_item)

                    # 每处理10行，强制更新一次UI
                    if (row_idx + 1) % 10 == 0:
                        QApplication.processEvents()
                        print(f"已处理 {row_idx + 1} 行数据")

                except Exception as e:
                    print(f"设置表格1行 {row_idx} 时出错: {str(e)}")
                    # 继续处理下一行，不中断

            # 重新启用排序功能
            self.table1.setSortingEnabled(True)

            # 按订单数量降序排序
            self.table1.sortItems(2, Qt.DescendingOrder)

            # 更新序号列
            self.update_table1_numbers(2)

            # 强制更新UI
            QApplication.processEvents()
            QApplication.processEvents()

            # 计算总订单数和总利润
            total_shops = len(table_data)
            total_orders = sum(row_data["order_count"] for row_data in table_data)
            total_profit = sum(row_data["profit"] for row_data in table_data)

            # 更新底部统计标签
            self.table1_stats_label.setText(f"店铺: {total_shops} | 订单: {total_orders} | 利润: {total_profit:.2f}")

            print(f"成功统计 {row_count} 个店铺的订单数量和利润，总订单数: {total_orders}，总利润: {total_profit:.2f}")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"显示店铺统计结果时出错: {str(e)}")

    def show_table1_context_menu(self, position):
        """显示表格1的右键菜单（店铺统计表格）"""
        context_menu = QMenu(self)

        # 添加菜单项表格1右键查看订单
        view_orders_action = QAction("查看订单", self)
        view_orders_action.triggered.connect(self.view_shop_orders_from_table1)

        enter_shop_backend_action = QAction("进入店铺后台", self)
        enter_shop_backend_action.triggered.connect(self.enter_shop_backend_from_table1)

        copy_shop_name_action = QAction("复制店铺名称", self)
        copy_shop_name_action.triggered.connect(self.copy_table1_shop_name)

        # 将菜单项添加到菜单
        context_menu.addAction(view_orders_action)
        context_menu.addAction(enter_shop_backend_action)
        context_menu.addSeparator()
        context_menu.addAction(copy_shop_name_action)

        # 显示右键菜单
        cursor_pos = QCursor.pos()
        context_menu.exec_(cursor_pos)

    def view_shop_orders_from_table1(self):
        """从表格1右键菜单查看店铺订单选中店铺的订单"""
        try:
            from PyQt5.QtWidgets import QApplication

            # 获取当前选中的行
            current_row = self.table1.currentRow()
            if current_row < 0:
                print("没有选中任何行")
                return

            # 获取选中行的店铺名称（第2列，索引为1）
            shop_name_item = self.table1.item(current_row, 1)  # 店铺名称列
            if not shop_name_item:
                print("未找到店铺名称")
                return

            shop_name_full = shop_name_item.text()
            if not shop_name_full:
                print("店铺名称为空")
                return

            # 处理店铺名称，去掉括号和分数（如 "佰俪品质女鞋(4.71)" -> "佰俪品质女鞋"）
            clean_shop_name = shop_name_full
            if '(' in shop_name_full and shop_name_full.endswith(')'):
                clean_shop_name = shop_name_full.split('(')[0].strip()
                print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")
            elif '（' in shop_name_full and shop_name_full.endswith('）'):
                clean_shop_name = shop_name_full.split('（')[0].strip()
                print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")

            print(f"准备查看店铺 '{clean_shop_name}' 的订单")

            # 查找主窗口（详情统计是一键下单的子窗口，所以需要找到主窗口）
            main_window = None

            # 直接通过QApplication查找主窗口
            print("开始查找主窗口...")
            app = QApplication.instance()
            all_windows = app.allWidgets()

            # 查找所有可能的主窗口
            candidates = []
            for window in all_windows:
                if hasattr(window, 'one_click_order_widget') and hasattr(window, 'table_widget'):
                    candidates.append(window)
                    print(f"找到候选主窗口: {window.__class__.__name__}")

            # 选择最合适的主窗口（通常是第一个）
            if candidates:
                main_window = candidates[0]
                print(f"选择主窗口: {main_window.__class__.__name__}")
            else:
                print("未找到主窗口，无法查看订单")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "提示", "无法找到主窗口，请确保在主程序中运行")
                return

            print(f"=== 开始在主窗口中查看店铺 '{clean_shop_name}' 的订单 ===")

            # 检查主窗口是否有一键下单组件
            if not hasattr(main_window, 'one_click_order_widget'):
                print("主窗口没有一键下单组件")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "提示", "主窗口没有一键下单组件")
                return

            one_click_widget = main_window.one_click_order_widget

            # 首先切换到全部订单页面
            print("🔄 步骤1: 切换到全部订单页面...")
            if hasattr(one_click_widget, 'show_all_orders'):
                one_click_widget.show_all_orders()
                print("✅ 已切换到全部订单页面")
            else:
                # 尝试点击全部订单按钮
                button_found = False
                from PyQt5.QtWidgets import QPushButton
                buttons = one_click_widget.findChildren(QPushButton)

                for button in buttons:
                    if button.text() == "全部订单":
                        print("找到全部订单按钮，点击...")
                        button.click()
                        button_found = True
                        break

                if not button_found and hasattr(one_click_widget, 'menu_buttons') and "全部订单" in one_click_widget.menu_buttons:
                    print("在菜单按钮中找到全部订单，点击...")
                    one_click_widget.menu_buttons["全部订单"].click()
                    button_found = True

                if not button_found:
                    print("⚠️ 未找到全部订单按钮")

            # 设置日期范围为最近3个月
            print("🔄 步骤2: 设置日期范围...")
            from PyQt5.QtCore import QDateTime, QTime, QTimer
            current_date = QDateTime.currentDateTime()
            three_months_ago = current_date.addMonths(-3)

            # 设置开始日期为3个月前的0点0分0秒
            start_datetime = QDateTime(three_months_ago.date(), QTime(0, 0, 0))
            if hasattr(one_click_widget, 'start_date'):
                one_click_widget.start_date.setDateTime(start_datetime)
                print(f"✅ 设置开始日期: {start_datetime.toString('yyyy-MM-dd HH:mm:ss')}")

            # 设置结束日期为当前日期的23点59分59秒
            end_datetime = QDateTime(current_date.date(), QTime(23, 59, 59))
            if hasattr(one_click_widget, 'end_date'):
                one_click_widget.end_date.setDateTime(end_datetime)
                print(f"✅ 设置结束日期: {end_datetime.toString('yyyy-MM-dd HH:mm:ss')}")

            # 设置关键字下拉框为"店铺名称"
            print("🔄 步骤3: 设置搜索条件...")
            if hasattr(one_click_widget, 'keyword_combo'):
                keyword_combo = one_click_widget.keyword_combo
                for i in range(keyword_combo.count()):
                    if keyword_combo.itemText(i) == "店铺名称":
                        keyword_combo.setCurrentIndex(i)
                        print("✅ 设置关键字为: 店铺名称")
                        break

            # 填入店铺名称到搜索框
            if hasattr(one_click_widget, 'search_edit'):
                search_edit = one_click_widget.search_edit
                search_edit.setText(clean_shop_name)
                search_edit.setFocus()  # 设置焦点到搜索框
                print(f"✅ 设置搜索内容为: {clean_shop_name}")

            # 延迟执行搜索，确保UI更新完成
            def perform_search():
                try:
                    print("🔄 步骤4: 执行搜索...")
                    if hasattr(one_click_widget, 'on_search_clicked'):
                        one_click_widget.on_search_clicked()
                        print("✅ 搜索已执行")
                    else:
                        print("⚠️ 未找到搜索方法")
                except Exception as e:
                    print(f"❌ 执行搜索时出错: {str(e)}")

            # 使用QTimer延迟执行搜索，确保UI更新完成
            QTimer.singleShot(300, perform_search)

            print(f"=== 完成查看店铺 '{clean_shop_name}' 的订单操作 ===")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"查看店铺订单时出错: {str(e)}")

    def copy_table1_shop_name(self):
        """复制表格1选中的店铺名称（只复制括号前的部分，不包括店铺分）"""
        # 获取当前选中的行
        current_row = self.table1.currentRow()
        if current_row < 0:
            print("没有选中任何行，请先选择要复制店铺名称的行")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "提示", "没有选中任何行，请先选择要复制店铺名称的行")
            return

        # 获取选中行的店铺名称（第2列，索引为1）
        shop_name_item = self.table1.item(current_row, 1)  # 店铺名称列
        if not shop_name_item or not shop_name_item.text():
            print("未找到店铺名称")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "提示", "未找到店铺名称")
            return

        shop_name_full = shop_name_item.text()

        # 处理店铺名称，去掉括号和分数（如 "佰俪品质女鞋(4.71)" -> "佰俪品质女鞋"）
        clean_shop_name = shop_name_full
        if '(' in shop_name_full and shop_name_full.endswith(')'):
            clean_shop_name = shop_name_full.split('(')[0].strip()
            print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")
        elif '（' in shop_name_full and shop_name_full.endswith('）'):
            clean_shop_name = shop_name_full.split('（')[0].strip()
            print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")

        # 复制到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText(clean_shop_name)
        print(f"已复制店铺名称到剪贴板: {clean_shop_name}")

    def enter_shop_backend_from_table1(self):
        """
        从表格1进入店铺后台

        功能:
        - 获取选中行的店铺名称
        - 调用主窗口的 on_table_double_clicked 方法进入店铺后台
        """
        try:
            # 获取当前选中的行
            current_row = self.table1.currentRow()
            if current_row < 0:
                print("没有选中任何行")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "提示", "请先选择要进入后台的店铺")
                return

            # 获取选中行的店铺名称（第2列，索引为1）
            shop_name_item = self.table1.item(current_row, 1)  # 店铺名称列
            if not shop_name_item:
                print("未找到店铺名称")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "未找到店铺名称信息")
                return

            shop_name = shop_name_item.text()
            if not shop_name:
                print("店铺名称为空")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "店铺名称为空")
                return

            # 处理店铺名称，去掉括号和分数（如 "达芙时尚女鞋（4.56）" -> "达芙时尚女鞋"）
            clean_shop_name = shop_name
            if '(' in shop_name and shop_name.endswith(')'):
                clean_shop_name = shop_name.split('(')[0].strip()
                print(f"店铺名称清理：原始='{shop_name}'，清理后='{clean_shop_name}'")
            elif '（' in shop_name and shop_name.endswith('）'):
                clean_shop_name = shop_name.split('（')[0].strip()
                print(f"店铺名称清理：原始='{shop_name}'，清理后='{clean_shop_name}'")

            print(f"正在进入店铺 '{clean_shop_name}' 的后台...")

            # 查找一键下单窗口实例
            one_click_order_window = None
            from PyQt5.QtWidgets import QApplication
            for widget in QApplication.instance().topLevelWidgets():
                if hasattr(widget, 'enter_shop_backend') and widget.__class__.__name__ == 'OneClickOrderWindow':
                    one_click_order_window = widget
                    print(f"找到一键下单窗口: {widget.__class__.__name__}")
                    break

            if not one_click_order_window:
                # 如果没有找到一键下单窗口，尝试查找任何有enter_shop_backend方法的窗口
                for widget in QApplication.instance().topLevelWidgets():
                    if hasattr(widget, 'enter_shop_backend') and hasattr(widget, '_direct_open_shop_backend'):
                        one_click_order_window = widget
                        print(f"找到候选一键下单窗口: {widget.__class__.__name__}")
                        break

                if not one_click_order_window:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "错误", "无法找到一键下单窗口，无法进入店铺后台\n\n请先打开一键下单模块")
                    return

            # 直接调用一键下单窗口的 _direct_open_shop_backend 方法
            print(f"调用一键下单模块的 _direct_open_shop_backend 方法...")
            one_click_order_window._direct_open_shop_backend(clean_shop_name)
            print(f"✅ 成功调用一键下单模块进入店铺 '{clean_shop_name}' 的后台")

        except Exception as e:
            print(f"进入店铺后台时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"进入店铺后台时出错: {str(e)}")

    def show_table2_context_menu(self, position):
        """显示表格2的右键菜单（上家店铺统计表格）"""
        context_menu = QMenu(self)

        # 添加菜单项
        view_orders_action = QAction("查看订单", self)
        view_orders_action.triggered.connect(self.view_supplier_orders_from_table2)

        open_wangwang_chat_action = QAction("打开旺旺聊天", self)
        open_wangwang_chat_action.triggered.connect(self.open_wangwang_chat)

        # 将菜单项添加到菜单
        context_menu.addAction(view_orders_action)
        context_menu.addSeparator()
        context_menu.addAction(open_wangwang_chat_action)

        # 显示右键菜单
        cursor_pos = QCursor.pos()
        context_menu.exec_(cursor_pos)

    def view_supplier_orders_from_table2(self):
        """从表格2右键菜单查看订单选中上家店铺的订单"""
        try:
            from PyQt5.QtWidgets import QApplication

            # 获取当前选中的行
            current_row = self.table2.currentRow()
            if current_row < 0:
                print("没有选中任何行")
                return

            # 获取选中行的上家店铺名称（第2列，索引为1）
            supplier_shop_item = self.table2.item(current_row, 1)  # 上家店铺列
            if not supplier_shop_item:
                print("未找到上家店铺名称")
                return

            supplier_shop_name = supplier_shop_item.text()
            if not supplier_shop_name:
                print("上家店铺名称为空")
                return

            print(f"准备查看上家店铺 '{supplier_shop_name}' 的订单")

            # 查找主窗口
            main_window = None
            app = QApplication.instance()
            all_windows = app.allWidgets()

            # 查找所有可能的主窗口
            candidates = []
            for window in all_windows:
                if hasattr(window, 'view_shop_orders') and hasattr(window, 'table_widget'):
                    candidates.append(window)
                    print(f"找到候选主窗口: {window.__class__.__name__}")

            # 选择最合适的主窗口（通常是第一个）
            if candidates:
                main_window = candidates[0]
                print(f"选择主窗口: {main_window.__class__.__name__}")
            else:
                print("未找到主窗口")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "提示", "无法找到主窗口，请确保在主程序中运行")
                return

            print(f"=== 开始在主窗口中查看上家店铺 '{supplier_shop_name}' 的订单 ===")

            # 设置主窗口的当前选中行（这里不需要选中特定行，因为我们要搜索软件备注）
            # 直接调用一个自定义的查看订单方法
            self.view_supplier_orders_in_main_window(main_window, supplier_shop_name)

            print(f"=== 完成查看上家店铺 '{supplier_shop_name}' 的订单操作 ===")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"查看上家店铺订单时出错: {str(e)}")

    def view_supplier_orders_in_main_window(self, main_window, supplier_shop_name):
        """在主窗口中查看上家店铺的订单"""
        try:
            # 检查主窗口是否有一键下单组件
            if not hasattr(main_window, 'one_click_order_widget'):
                print("主窗口没有一键下单组件")
                return

            one_click_widget = main_window.one_click_order_widget

            # 设置日期范围为最近6个月
            from PyQt5.QtCore import QDateTime, QTime, QTimer
            current_date = QDateTime.currentDateTime()
            six_months_ago = current_date.addMonths(-6)

            # 设置开始日期为6个月前的0点0分0秒
            start_datetime = QDateTime(six_months_ago.date(), QTime(0, 0, 0))
            if hasattr(one_click_widget, 'start_date'):
                one_click_widget.start_date.setDateTime(start_datetime)

            # 设置结束日期为当前日期的23点59分59秒
            end_datetime = QDateTime(current_date.date(), QTime(23, 59, 59))
            if hasattr(one_click_widget, 'end_date'):
                one_click_widget.end_date.setDateTime(end_datetime)

            print(f"设置日期范围: {start_datetime.toString('yyyy-MM-dd HH:mm:ss')} 至 {end_datetime.toString('yyyy-MM-dd HH:mm:ss')}")

            # 设置关键字下拉框为"软件备注"
            if hasattr(one_click_widget, 'keyword_combo'):
                keyword_combo = one_click_widget.keyword_combo
                for i in range(keyword_combo.count()):
                    if keyword_combo.itemText(i) == "软件备注":
                        keyword_combo.setCurrentIndex(i)
                        print("设置关键字为: 软件备注")
                        break

            # 填入上家店铺名称到搜索框
            if hasattr(one_click_widget, 'search_edit'):
                search_edit = one_click_widget.search_edit
                search_edit.setText(supplier_shop_name)
                search_edit.setFocus()  # 设置焦点到搜索框
                print(f"设置搜索内容为: {supplier_shop_name}")

            # 自动触发搜索
            if hasattr(one_click_widget, 'on_search_clicked'):
                # 使用QTimer延迟执行搜索，确保UI更新完成
                QTimer.singleShot(200, one_click_widget.on_search_clicked)
                print("触发搜索")

            # 确保切换到全部订单页面
            def switch_to_all_orders():
                try:
                    if hasattr(one_click_widget, 'show_all_orders'):
                        print("切换到全部订单页面...")
                        one_click_widget.show_all_orders()
                        print("已切换到全部订单页面")
                    else:
                        print("未找到show_all_orders方法，尝试点击全部订单按钮...")
                        # 查找并点击全部订单按钮
                        from PyQt5.QtWidgets import QPushButton
                        buttons = one_click_widget.findChildren(QPushButton)
                        button_found = False

                        for button in buttons:
                            if button.text() == "全部订单":
                                print("找到全部订单按钮，点击...")
                                button.click()
                                print("已点击全部订单按钮")
                                button_found = True
                                break

                        if not button_found:
                            # 尝试查找菜单按钮
                            if hasattr(one_click_widget, 'menu_buttons') and "全部订单" in one_click_widget.menu_buttons:
                                print("在菜单按钮中找到全部订单，点击...")
                                one_click_widget.menu_buttons["全部订单"].click()
                                print("已点击菜单中的全部订单按钮")
                            else:
                                print("未找到全部订单按钮")
                except Exception as e:
                    print(f"切换到全部订单页面时出错: {str(e)}")

            # 延迟执行切换，确保搜索完成
            QTimer.singleShot(500, switch_to_all_orders)

        except Exception as e:
            print(f"在主窗口中查看上家店铺订单时出错: {str(e)}")
        #表格2右键打开旺旺聊天
    def open_wangwang_chat(self):
        """打开旺旺聊天"""
        selected_items = self.table2.selectedItems()
        if not selected_items:
            return

        # 获取选中的行
        selected_rows = set()
        for item in selected_items:
            selected_rows.add(item.row())

        # 从每个选中的行中获取上家旺旺ID或上家店铺名称
        chat_data = []
        for row in selected_rows:
            # 获取上家旺旺列（第6列，索引为5）的内容
            wangwang_item = self.table2.item(row, 5)
            wangwang_id = ""
            if wangwang_item and wangwang_item.text():
                wangwang_id = wangwang_item.text().strip()

            if wangwang_id:
                # 如果上家旺旺不为空，使用旺旺聊天
                chat_data.append({
                    'type': 'wangwang',
                    'value': wangwang_id
                })
            else:
                # 如果上家旺旺为空，获取上家店铺列（第2列，索引为1）的内容
                shop_item = self.table2.item(row, 1)
                if shop_item and shop_item.text():
                    shop_name = shop_item.text().strip()
                    if shop_name:
                        chat_data.append({
                            'type': 'search',
                            'value': shop_name
                        })

        if chat_data:
            print(f"打开旺旺聊天或搜索: {[data['value'] for data in chat_data]}")

            # 为每个数据打开对应的链接
            for data in chat_data:
                try:
                    if data['type'] == 'wangwang':
                        # 使用旺旺聊天链接
                        wangwang_id = data['value']
                        # URL编码旺旺ID
                        encoded_wangwang_id = urllib.parse.quote(wangwang_id)

                        # 构建完整的URL
                        base_url = "https://air.1688.com/app/ocms-fusion-components-1688/def_cbu_web_im/index.html?"
                        params = f"touid=cnalichn{encoded_wangwang_id}&siteid=cnalichn&status=2&portalId=&gid=&offerId=&itemsId=#/"
                        full_url = base_url + params

                        print(f"打开旺旺聊天URL: {full_url}")

                        # 使用默认浏览器打开URL
                        import webbrowser
                        webbrowser.open(full_url)

                    elif data['type'] == 'search':
                        # 使用1688搜索链接
                        shop_name = data['value']
                        print(f"上家旺旺为空，使用上家店铺进行搜索: {shop_name}")

                        # 使用GB2312编码上家店铺名称
                        try:
                            # 先将字符串编码为GB2312字节，然后进行URL编码
                            gb2312_bytes = shop_name.encode('gb2312')
                            encoded_shop_name = urllib.parse.quote(gb2312_bytes)
                            print(f"使用GB2312编码: {shop_name} -> {encoded_shop_name}")
                        except UnicodeEncodeError:
                            # 如果GB2312编码失败，回退到UTF-8编码
                            encoded_shop_name = urllib.parse.quote(shop_name)
                            print(f"GB2312编码失败，使用UTF-8编码: {shop_name} -> {encoded_shop_name}")

                        # 构建1688搜索链接
                        search_url = f"https://s.1688.com/selloffer/offer_search.htm?keywords={encoded_shop_name}&spm=a260k.home2024.searchbox.0"
                        print(f"生成1688搜索链接: {search_url}")

                        # 使用默认浏览器打开搜索链接
                        import webbrowser
                        webbrowser.open(search_url)

                except Exception as e:
                    print(f"打开旺旺聊天或搜索时出错: {str(e)}")

    def show_table3_context_menu(self, position):
        """显示表格3的右键菜单（商品详情表格）"""
        context_menu = QMenu(self)

        # 添加菜单项
        select_all_action = QAction("全选", self)
        select_all_action.triggered.connect(self.select_all_table3_rows)

        deselect_all_action = QAction("取消全选", self)
        deselect_all_action.triggered.connect(self.deselect_all_table3_rows)

        copy_product_id_action = QAction("复制商品ID", self)
        copy_product_id_action.triggered.connect(self.copy_product_id)

        copy_product_title_action = QAction("复制商品标题", self)
        copy_product_title_action.triggered.connect(self.copy_product_title)

        copy_shop_name_action = QAction("复制店铺名称", self)
        copy_shop_name_action.triggered.connect(self.copy_table3_shop_name)

        enter_shop_backend_action = QAction("进入店铺后台", self)
        enter_shop_backend_action.triggered.connect(self.enter_shop_backend_from_table3)

        view_supplier_product_action = QAction("查看上家商品", self)
        view_supplier_product_action.triggered.connect(self.view_supplier_product)

        open_wangwang_chat_action = QAction("打开旺旺聊天", self)
        open_wangwang_chat_action.triggered.connect(self.open_table3_wangwang_chat)

        edit_product_action = QAction("编辑商品", self)
        edit_product_action.triggered.connect(self.edit_product)

        delist_product_action = QAction("下架", self)
        delist_product_action.triggered.connect(self.delist_product)

        list_product_action = QAction("上架", self)
        list_product_action.triggered.connect(self.list_product)

        # 新增：下架垃圾商品
        delist_junk_products_action = QAction("下架垃圾商品", self)
        delist_junk_products_action.triggered.connect(self.delist_junk_products)

        # 将菜单项添加到菜单
        context_menu.addAction(select_all_action)
        context_menu.addAction(deselect_all_action)
        context_menu.addSeparator()
        context_menu.addAction(copy_product_id_action)
        context_menu.addAction(copy_product_title_action)
        context_menu.addAction(copy_shop_name_action)
        context_menu.addAction(enter_shop_backend_action)
        context_menu.addSeparator()
        context_menu.addAction(view_supplier_product_action)
        context_menu.addAction(open_wangwang_chat_action)
        context_menu.addAction(edit_product_action)
        context_menu.addSeparator()
        context_menu.addAction(delist_product_action)
        context_menu.addAction(list_product_action)
        context_menu.addAction(delist_junk_products_action)

        # 显示右键菜单
        cursor_pos = QCursor.pos()
        context_menu.exec_(cursor_pos)

    def copy_product_id(self):
        """复制选中的商品ID"""
        # 获取表格选中的行
        selected_rows = self.get_table3_selected_table_rows()
        if not selected_rows:
            print("没有选中任何行")
            return

        # 获取商品ID列的索引
        product_id_col_index = -1
        for col in range(self.table3.columnCount()):
            header_item = self.table3.horizontalHeaderItem(col)
            if header_item and header_item.text() == "商品ID":
                product_id_col_index = col
                break

        if product_id_col_index == -1:
            print("未找到商品ID列")
            return

        # 从每个选中的行中获取商品ID
        product_ids = []
        for row in selected_rows:
            product_id_item = self.table3.item(row, product_id_col_index)
            if product_id_item and product_id_item.text():
                product_id = product_id_item.text()
                if product_id:
                    product_ids.append(product_id)

        if product_ids:
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(product_ids))
            print(f"已复制 {len(product_ids)} 个商品ID到剪贴板")

    def copy_product_title(self):
        """复制选中的商品标题"""
        # 获取表格选中的行
        selected_rows = self.get_table3_selected_table_rows()
        if not selected_rows:
            print("没有选中任何行")
            return

        # 获取商品标题列的索引
        product_title_col_index = -1
        for col in range(self.table3.columnCount()):
            header_item = self.table3.horizontalHeaderItem(col)
            if header_item and header_item.text() == "商品标题":
                product_title_col_index = col
                break

        if product_title_col_index == -1:
            print("未找到商品标题列")
            return

        # 从每个选中的行中获取商品标题
        product_titles = []
        for row in selected_rows:
            title_item = self.table3.item(row, product_title_col_index)
            if title_item and title_item.text():
                title = title_item.text()
                if title:
                    product_titles.append(title)

        if product_titles:
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(product_titles))
            print(f"已复制 {len(product_titles)} 个商品标题到剪贴板")

    def copy_table3_shop_name(self):
        """复制表格3店铺名称"""
        # 尝试多种方式获取选中的行
        selected_rows = []

        # 方法1：尝试获取表格选中的行
        table_selected_rows = self.get_table3_selected_table_rows()
        if table_selected_rows:
            selected_rows = table_selected_rows
            print(f"通过表格选择获取到 {len(selected_rows)} 行")

        # 方法2：如果表格选择没有结果，尝试获取复选框选中的行
        if not selected_rows:
            checkbox_selected_rows = self.get_table3_selected_rows()
            if checkbox_selected_rows:
                selected_rows = checkbox_selected_rows
                print(f"通过复选框获取到 {len(selected_rows)} 行")

        # 方法3：如果都没有结果，尝试获取当前行
        if not selected_rows:
            current_row = self.table3.currentRow()
            if current_row >= 0:
                selected_rows = [current_row]
                print(f"使用当前行: {current_row}")

        if not selected_rows:
            print("没有选中任何行，请先选择要复制店铺名称的行")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "提示", "没有选中任何行，请先选择要复制店铺名称的行")
            return

        # 获取店铺名称列的索引
        shop_name_col_index = -1
        for col in range(self.table3.columnCount()):
            header_item = self.table3.horizontalHeaderItem(col)
            if header_item and header_item.text() == "店铺名称":
                shop_name_col_index = col
                break

        if shop_name_col_index == -1:
            print("未找到店铺名称列")
            return

        # 从每个选中的行中获取店铺名称
        shop_names = []
        for row in selected_rows:
            shop_item = self.table3.item(row, shop_name_col_index)
            if shop_item and shop_item.text():
                shop_name_full = shop_item.text()
                if shop_name_full:
                    # 处理店铺名称，去掉括号和分数（如 "佰俪品质女鞋(4.71)" -> "佰俪品质女鞋"）
                    clean_shop_name = shop_name_full
                    if '(' in shop_name_full and shop_name_full.endswith(')'):
                        clean_shop_name = shop_name_full.split('(')[0].strip()
                        print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")
                    elif '（' in shop_name_full and shop_name_full.endswith('）'):
                        clean_shop_name = shop_name_full.split('（')[0].strip()
                        print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")

                    shop_names.append(clean_shop_name)

        if shop_names:
            # 去重并保持顺序
            unique_shop_names = []
            for shop_name in shop_names:
                if shop_name not in unique_shop_names:
                    unique_shop_names.append(shop_name)

            # 将店铺名称复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(unique_shop_names))
            print(f"已复制 {len(unique_shop_names)} 个店铺名称到剪贴板")
        else:
            print("选中的行中没有找到店铺名称")

    def enter_shop_backend_from_table3(self):
        """
        从表格3进入店铺后台

        功能:
        - 获取选中行的店铺名称
        - 调用主窗口的 on_table_double_clicked 方法进入店铺后台
        """
        try:
            # 尝试多种方式获取选中的行
            selected_rows = []

            # 方法1：尝试获取表格选中的行
            table_selected_rows = self.get_table3_selected_table_rows()
            if table_selected_rows:
                selected_rows = table_selected_rows
                print(f"通过表格选择获取到 {len(selected_rows)} 行")

            # 方法2：如果表格选择没有结果，尝试获取复选框选中的行
            if not selected_rows:
                checkbox_selected_rows = self.get_table3_selected_rows()
                if checkbox_selected_rows:
                    selected_rows = checkbox_selected_rows
                    print(f"通过复选框获取到 {len(selected_rows)} 行")

            # 方法3：如果都没有结果，尝试获取当前行
            if not selected_rows:
                current_row = self.table3.currentRow()
                if current_row >= 0:
                    selected_rows = [current_row]
                    print(f"使用当前行: {current_row}")

            if not selected_rows:
                print("没有选中任何行，请先选择要进入后台的店铺")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "提示", "请先选择要进入后台的店铺")
                return

            # 获取店铺名称列的索引
            shop_name_col_index = -1
            for col in range(self.table3.columnCount()):
                header_item = self.table3.horizontalHeaderItem(col)
                if header_item and header_item.text() == "店铺名称":
                    shop_name_col_index = col
                    break

            if shop_name_col_index == -1:
                print("未找到店铺名称列")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "未找到店铺名称列")
                return

            # 获取第一个选中行的店铺名称
            first_row = selected_rows[0]
            shop_item = self.table3.item(first_row, shop_name_col_index)
            if not shop_item or not shop_item.text():
                print("未找到店铺名称")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "未找到店铺名称信息")
                return

            shop_name_full = shop_item.text().strip()
            if not shop_name_full:
                print("店铺名称为空")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "店铺名称为空")
                return

            # 处理店铺名称，去掉括号和分数（如 "MDO品质女鞋(4.92)" -> "MDO品质女鞋"）
            clean_shop_name = shop_name_full
            if '(' in shop_name_full and shop_name_full.endswith(')'):
                clean_shop_name = shop_name_full.split('(')[0].strip()
                print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")
            elif '（' in shop_name_full and shop_name_full.endswith('）'):
                clean_shop_name = shop_name_full.split('（')[0].strip()
                print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")

            print(f"正在进入店铺 '{clean_shop_name}' 的后台...")

            # 查找一键下单窗口实例
            one_click_order_window = None
            from PyQt5.QtWidgets import QApplication
            for widget in QApplication.instance().topLevelWidgets():
                if hasattr(widget, 'enter_shop_backend') and widget.__class__.__name__ == 'OneClickOrderWindow':
                    one_click_order_window = widget
                    print(f"找到一键下单窗口: {widget.__class__.__name__}")
                    break

            if not one_click_order_window:
                # 如果没有找到一键下单窗口，尝试查找任何有enter_shop_backend方法的窗口
                for widget in QApplication.instance().topLevelWidgets():
                    if hasattr(widget, 'enter_shop_backend') and hasattr(widget, '_direct_open_shop_backend'):
                        one_click_order_window = widget
                        print(f"找到候选一键下单窗口: {widget.__class__.__name__}")
                        break

                if not one_click_order_window:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "错误", "无法找到一键下单窗口，无法进入店铺后台\n\n请先打开一键下单模块")
                    return

            # 直接调用一键下单窗口的 _direct_open_shop_backend 方法
            print(f"调用一键下单模块的 _direct_open_shop_backend 方法...")
            one_click_order_window._direct_open_shop_backend(clean_shop_name)
            print(f"✅ 成功调用一键下单模块进入店铺 '{clean_shop_name}' 的后台")

        except Exception as e:
            print(f"进入店铺后台时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"进入店铺后台时出错: {str(e)}")

    def view_supplier_product(self):
        """查看上家商品"""
        # 获取表格选中的行
        selected_rows = self.get_table3_selected_table_rows()
        if not selected_rows:
            print("没有选中任何行")
            return

        # 获取上家链接列的索引
        supplier_link_col_index = -1
        for col in range(self.table3.columnCount()):
            header_item = self.table3.horizontalHeaderItem(col)
            if header_item and header_item.text() == "上家链接":
                supplier_link_col_index = col
                break

        if supplier_link_col_index == -1:
            print("未找到上家链接列")
            return

        # 从每个选中的行中获取上家链接
        supplier_links = []
        for row in selected_rows:
            link_item = self.table3.item(row, supplier_link_col_index)
            if link_item and link_item.text():
                link = link_item.text()
                if link:
                    supplier_links.append(link)

        if supplier_links:
            print(f"查看上家商品: {supplier_links}")

            # 使用默认浏览器打开链接
            for link in supplier_links:
                try:
                    import webbrowser
                    webbrowser.open(link)
                    print(f"已打开链接: {link}")
                except Exception as e:
                    print(f"打开链接时出错: {str(e)}")

    def open_table3_wangwang_chat(self):
        """打开表格3中选中行的旺旺聊天"""
        # 获取表格选中的行
        selected_rows = self.get_table3_selected_table_rows()
        if not selected_rows:
            print("没有选中任何行")
            return

        # 获取上家旺旺列的索引
        supplier_wangwang_col_index = -1
        for col in range(self.table3.columnCount()):
            header_item = self.table3.horizontalHeaderItem(col)
            if header_item and header_item.text() == "上家旺旺":
                supplier_wangwang_col_index = col
                break

        if supplier_wangwang_col_index == -1:
            print("未找到上家旺旺列")
            return

        # 从每个选中的行中获取上家旺旺ID和字体信息
        wangwang_data = []
        for row in selected_rows:
            wangwang_item = self.table3.item(row, supplier_wangwang_col_index)
            if wangwang_item and wangwang_item.text():
                wangwang_id = wangwang_item.text()
                if wangwang_id:
                    # 检查字体是否加粗
                    is_bold = wangwang_item.font().bold()
                    wangwang_data.append({
                        'id': wangwang_id,
                        'is_bold': is_bold
                    })

        if wangwang_data:
            print(f"打开旺旺聊天: {[data['id'] for data in wangwang_data]}")

            # 为每个旺旺ID打开对应的链接
            for data in wangwang_data:
                try:
                    wangwang_id = data['id']
                    is_bold = data['is_bold']

                    if is_bold:
                        # 加粗的上家旺旺，使用搜索链接
                        print(f"检测到加粗字体，使用搜索链接: {wangwang_id}")

                        # URL编码上家旺旺 - 使用GB2312编码以兼容1688网站
                        try:
                            # 先将字符串编码为GB2312字节，然后进行URL编码
                            gb2312_bytes = wangwang_id.encode('gb2312')
                            encoded_wangwang_id = urllib.parse.quote(gb2312_bytes)
                            print(f"使用GB2312编码: {wangwang_id} -> {encoded_wangwang_id}")
                        except UnicodeEncodeError:
                            # 如果GB2312编码失败，回退到UTF-8编码
                            encoded_wangwang_id = urllib.parse.quote(wangwang_id)
                            print(f"GB2312编码失败，使用UTF-8编码: {wangwang_id} -> {encoded_wangwang_id}")

                        # 组合搜索链接
                        search_url = f"https://s.1688.com/selloffer/offer_search.htm?keywords={encoded_wangwang_id}&spm=a260k.home2024.searchbox.0"
                        print(f"生成搜索链接: {search_url}")

                        # 用默认浏览器打开
                        import webbrowser
                        webbrowser.open(search_url)
                    else:
                        # 非加粗的上家旺旺，使用原来的旺旺聊天链接
                        print(f"检测到正常字体，使用旺旺聊天链接: {wangwang_id}")

                        # URL编码旺旺ID
                        encoded_wangwang_id = urllib.parse.quote(wangwang_id)

                        # 构建完整的URL
                        base_url = "https://air.1688.com/app/ocms-fusion-components-1688/def_cbu_web_im/index.html?"
                        params = f"touid=cnalichn{encoded_wangwang_id}&siteid=cna"
                        full_url = base_url + params

                        print(f"打开URL: {full_url}")

                        # 使用默认浏览器打开URL
                        import webbrowser
                        webbrowser.open(full_url)

                except Exception as e:
                    print(f"打开旺旺聊天时出错: {str(e)}")

    def edit_product(self):
        """编辑商品 - 使用店铺cookie在内嵌浏览器中打开快手商品编辑页面"""
        try:
            # 尝试多种方式获取选中的行
            selected_rows = []

            # 方法1：尝试获取表格选中的行
            table_selected_rows = self.get_table3_selected_table_rows()
            if table_selected_rows:
                selected_rows = table_selected_rows
                print(f"通过表格选择获取到 {len(selected_rows)} 行")

            # 方法2：如果表格选择没有结果，尝试获取复选框选中的行
            if not selected_rows:
                checkbox_selected_rows = self.get_table3_selected_rows()
                if checkbox_selected_rows:
                    selected_rows = checkbox_selected_rows
                    print(f"通过复选框获取到 {len(selected_rows)} 行")

            # 方法3：如果都没有结果，尝试获取当前行
            if not selected_rows:
                current_row = self.table3.currentRow()
                if current_row >= 0:
                    selected_rows = [current_row]
                    print(f"使用当前行: {current_row}")

            if not selected_rows:
                print("没有选中任何行，请先选择要编辑的商品")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "提示", "请先选择要编辑的商品")
                return

            # 获取商品ID列和店铺名称列的索引
            product_id_col_index = -1
            shop_name_col_index = -1
            
            for col in range(self.table3.columnCount()):
                header_item = self.table3.horizontalHeaderItem(col)
                if header_item:
                    if header_item.text() == "商品ID":
                        product_id_col_index = col
                    elif header_item.text() == "店铺名称":
                        shop_name_col_index = col

            if product_id_col_index == -1:
                print("未找到商品ID列")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "未找到商品ID列")
                return
                
            if shop_name_col_index == -1:
                print("未找到店铺名称列")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "未找到店铺名称列")
                return

            # 获取第一个选中行的商品ID和店铺名称
            first_row = selected_rows[0]
            product_id_item = self.table3.item(first_row, product_id_col_index)
            shop_name_item = self.table3.item(first_row, shop_name_col_index)
            
            if not product_id_item or not product_id_item.text():
                print("未找到商品ID")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "未找到商品ID信息")
                return
                
            if not shop_name_item or not shop_name_item.text():
                print("未找到店铺名称")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "未找到店铺名称信息")
                return

            product_id = product_id_item.text().strip()
            shop_name_full = shop_name_item.text().strip()
            
            # 处理店铺名称，去掉括号和分数（如 "MDO品质女鞋(4.92)" -> "MDO品质女鞋"）
            clean_shop_name = shop_name_full
            if '(' in shop_name_full and shop_name_full.endswith(')'):
                clean_shop_name = shop_name_full.split('(')[0].strip()
                print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")
            elif '（' in shop_name_full and shop_name_full.endswith('）'):
                clean_shop_name = shop_name_full.split('（')[0].strip()
                print(f"店铺名称清理：原始='{shop_name_full}'，清理后='{clean_shop_name}'")

            # 构建快手商品编辑链接
            edit_url = f"https://s.kwaixiaodian.com/zone/goods/config/release/edit/ordinary?itemId={product_id}"
            print(f"正在编辑店铺 '{clean_shop_name}' 的商品 '{product_id}'...")
            print(f"商品编辑页面: {edit_url}")

            # 查找一键下单窗口实例
            one_click_order_window = None
            from PyQt5.QtWidgets import QApplication
            for widget in QApplication.instance().topLevelWidgets():
                if hasattr(widget, 'open_url_with_shop_backend') and widget.__class__.__name__ == 'OneClickOrderWindow':
                    one_click_order_window = widget
                    print(f"找到一键下单窗口: {widget.__class__.__name__}")
                    break

            if not one_click_order_window:
                # 如果没有找到一键下单窗口，尝试查找任何有open_url_with_shop_backend方法的窗口
                for widget in QApplication.instance().topLevelWidgets():
                    if hasattr(widget, 'open_url_with_shop_backend'):
                        one_click_order_window = widget
                        print(f"找到候选一键下单窗口: {widget.__class__.__name__}")
                        break

                if not one_click_order_window:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "错误", "无法找到一键下单窗口，无法使用店铺cookie打开商品编辑页面\n\n请先打开一键下单模块")
                    return

            # 调用一键下单窗口的 open_url_with_shop_backend 方法，使用店铺cookie访问商品编辑页面
            print(f"调用一键下单模块的 open_url_with_shop_backend 方法...")
            one_click_order_window.open_url_with_shop_backend(clean_shop_name, edit_url)
            print(f"✅ 成功调用一键下单模块打开店铺 '{clean_shop_name}' 商品 '{product_id}' 的编辑页面")

        except Exception as e:
            print(f"编辑商品时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"编辑商品时出错: {str(e)}")

    def delist_product(self):
        """下架商品"""
        # 获取表格选中的行（智能选择）
        selected_rows, selection_type = self.get_table3_effective_selected_rows()
        if not selected_rows:
            print("没有选中任何行")
            QMessageBox.warning(self, "提示", "请先选择要下架的商品（可以勾选复选框或选择表格行）")
            return
        
        print(f"使用{selection_type}模式选中了 {len(selected_rows)} 行商品")

        # 获取商品ID列和店铺名称列的索引
        product_id_col_index = -1
        shop_name_col_index = -1
        
        for col in range(self.table3.columnCount()):
            header_item = self.table3.horizontalHeaderItem(col)
            if header_item:
                if header_item.text() == "商品ID":
                    product_id_col_index = col
                elif header_item.text() == "店铺名称":
                    shop_name_col_index = col

        if product_id_col_index == -1:
            print("未找到商品ID列")
            QMessageBox.warning(self, "错误", "未找到商品ID列")
            return
            
        if shop_name_col_index == -1:
            print("未找到店铺名称列")
            QMessageBox.warning(self, "错误", "未找到店铺名称列")
            return

        # 收集选中行的商品信息
        product_operations = []
        for row in selected_rows:
            product_id_item = self.table3.item(row, product_id_col_index)
            shop_name_item = self.table3.item(row, shop_name_col_index)
            
            if product_id_item and product_id_item.text() and shop_name_item and shop_name_item.text():
                product_id = product_id_item.text()
                shop_name_full = shop_name_item.text()
                
                # 处理店铺名称，去掉括号和分数（如 "佰俪品质女鞋(4.71)" -> "佰俪品质女鞋"）
                clean_shop_name = shop_name_full
                if '(' in shop_name_full and shop_name_full.endswith(')'):
                    clean_shop_name = shop_name_full.split('(')[0].strip()
                elif '（' in shop_name_full and shop_name_full.endswith('）'):
                    clean_shop_name = shop_name_full.split('（')[0].strip()
                
                product_operations.append({
                    'product_id': product_id,
                    'shop_name': clean_shop_name,
                    'row': row
                })

        if not product_operations:
            print("没有有效的商品信息")
            QMessageBox.warning(self, "错误", "没有找到有效的商品信息")
            return

        # 确认下架操作
        reply = QMessageBox.question(self, "确认下架", 
                                   f"确定要下架 {len(product_operations)} 个商品吗？\n选择方式：{selection_type}", 
                                   QMessageBox.Yes | QMessageBox.No)
        if reply != QMessageBox.Yes:
            return

        # 清空选中行的操作状态列
        self.clear_operation_status_for_rows([op['row'] for op in product_operations])
        
        # 执行下架操作
        self.perform_delist_operation(product_operations)

    def clear_operation_status_for_rows(self, rows):
        """清空指定行的操作状态列"""
        try:
            # 动态查找操作状态列索引
            status_col_index = -1
            for col in range(self.table3.columnCount()):
                header_item = self.table3.horizontalHeaderItem(col)
                if header_item and header_item.text() == "操作状态":
                    status_col_index = col
                    break

            if status_col_index != -1:
                for row in rows:
                    status_item = QTableWidgetItem("")
                    self.table3.setItem(row, status_col_index, status_item)
        except Exception as e:
            print(f"清空操作状态时出错: {str(e)}")

    def update_operation_status(self, row, status):
        """更新指定行的操作状态"""
        try:
            # 动态查找操作状态列索引
            status_col_index = -1
            for col in range(self.table3.columnCount()):
                header_item = self.table3.horizontalHeaderItem(col)
                if header_item and header_item.text() == "操作状态":
                    status_col_index = col
                    break

            if status_col_index != -1:
                status_item = QTableWidgetItem(status)
                self.table3.setItem(row, status_col_index, status_item)
        except Exception as e:
            print(f"更新操作状态时出错: {str(e)}")

    def perform_delist_operation(self, product_operations):
        """执行商品下架操作"""
        try:
            # 读取账号管理配置
            config_path = get_config_path('账号管理.json')
            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                QMessageBox.warning(self, "错误", "账号管理配置文件不存在")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 按店铺分组商品
            shop_products = {}
            for operation in product_operations:
                shop_name = operation['shop_name']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(operation)

            # 执行下架操作
            results = []
            for shop_name, products in shop_products.items():
                print(f"开始下架店铺 {shop_name} 的商品...")
                
                # 查找店铺的accesstoken
                shop_info = self.find_shop_info(config_data, shop_name)
                if not shop_info:
                    print(f"未找到店铺 {shop_name} 的配置信息")
                    for product in products:
                        # 确保product是字典类型且包含product_id键
                        if isinstance(product, dict) and 'product_id' in product:
                            # 更新状态列
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 未找到店铺配置")
                        else:
                            print(f"警告：产品数据格式不正确: {product}")
                    continue

                access_token = shop_info.get('accesstoken')
                if not access_token:
                    print(f"店铺 {shop_name} 没有accesstoken")
                    for product in products:
                        # 确保product是字典类型且包含product_id键
                        if isinstance(product, dict) and 'product_id' in product:
                            # 更新状态列
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 缺少accesstoken")
                        else:
                            print(f"警告：产品数据格式不正确: {product}")
                    continue

                # 创建快手API实例
                try:
                    # 从配置文件或环境变量获取API密钥
                    app_key = shop_info.get('app_key') or self.get_kuaishou_app_key()
                    app_secret = shop_info.get('app_secret') or self.get_kuaishou_app_secret()
                    sign_secret = shop_info.get('signSecret') or self.get_kuaishou_sign_secret()
                    
                    api = KuaishouAPI(
                        app_key=app_key,
                        app_secret=app_secret,
                        sign_secret=sign_secret,
                        access_token=access_token
                    )
                    
                    # 为每个商品执行下架操作
                    for product in products:
                        # 确保product是字典类型且包含product_id键
                        if not isinstance(product, dict) or 'product_id' not in product:
                            print(f"警告：产品数据格式不正确: {product}")
                            continue
                            
                        product_id = product['product_id']
                        row = product.get('row', -1)
                        print(f"正在下架商品: {product_id}")
                        
                        # 更新状态为处理中
                        if row >= 0:
                            self.update_operation_status(row, "下架中...")
                        
                        try:
                            # 调用快手API下架商品
                            result = api.unshelf_item(int(product_id))
                            
                            if result.get('success'):
                                print(f"商品 {product_id} 下架成功")
                                # 更新状态为成功
                                if row >= 0:
                                    self.update_operation_status(row, "下架成功")
                            else:
                                error_msg = result.get('message', '下架失败')
                                print(f"商品 {product_id} 下架失败: {error_msg}")
                                # 更新状态为失败
                                if row >= 0:
                                    self.update_operation_status(row, f"下架失败: {error_msg}")
                        except Exception as e:
                            error_msg = f"下架异常: {str(e)}"
                            print(f"商品 {product_id} 下架异常: {error_msg}")
                            # 更新状态为异常
                            if row >= 0:
                                self.update_operation_status(row, f"下架异常: {error_msg}")
                
                except Exception as e:
                    print(f"创建快手API实例失败: {str(e)}")
                    for product in products:
                        # 确保product是字典类型且包含product_id键
                        if isinstance(product, dict) and 'product_id' in product:
                            # 更新状态列
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"API初始化失败: {str(e)}")
                        else:
                            print(f"警告：产品数据格式不正确: {product}")

            # 操作完成，在控制台输出摘要
            print("下架操作完成，状态已更新到表格中")

        except Exception as e:
            print(f"执行下架操作时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行下架操作时出错: {str(e)}")

    def find_shop_info(self, config_data, shop_name):
        """从配置数据中查找店铺信息"""
        try:
            if 'data' in config_data:
                for shop_data in config_data['data']:
                    if shop_data.get('店铺名称') == shop_name:
                        return shop_data
            return None
        except Exception as e:
            print(f"查找店铺信息时出错: {str(e)}")
            return None

    def show_delist_results(self, results):
        """显示下架结果"""
        try:
            success_count = sum(1 for r in results if r['success'])
            fail_count = len(results) - success_count
            
            # 构建结果消息
            result_message = f"下架操作完成\n\n成功: {success_count} 个\n失败: {fail_count} 个"
            
            if fail_count > 0:
                result_message += "\n\n失败详情:"
                for result in results:
                    if not result['success']:
                        result_message += f"\n商品ID: {result['product_id']} - {result['message']}"
            
            # 显示结果对话框
            if fail_count == 0:
                QMessageBox.information(self, "下架成功", result_message)
            else:
                QMessageBox.warning(self, "下架完成", result_message)
                
            print(f"下架操作完成 - 成功: {success_count}, 失败: {fail_count}")
            
        except Exception as e:
            print(f"显示下架结果时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"显示结果时出错: {str(e)}")

    def get_kuaishou_app_key(self):
        """获取快手API的App Key"""
        try:
            # 首先尝试从配置文件读取
            config_path = get_config_path('config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    kuaishou_config = config_data.get('kuaishou_api_config', {})
                    app_key = kuaishou_config.get('app_key')
                    if app_key:
                        return app_key
            
            # 如果配置文件中没有，返回默认值或环境变量
            return os.environ.get('KUAISHOU_APP_KEY', 'your_app_key_here')
            
        except Exception as e:
            print(f"获取快手App Key失败: {str(e)}")
            return 'your_app_key_here'

    def get_kuaishou_app_secret(self):
        """获取快手API的App Secret"""
        try:
            # 首先尝试从配置文件读取
            config_path = get_config_path('config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    kuaishou_config = config_data.get('kuaishou_api_config', {})
                    app_secret = kuaishou_config.get('app_secret')
                    if app_secret:
                        return app_secret
            
            # 如果配置文件中没有，返回默认值或环境变量
            return os.environ.get('KUAISHOU_APP_SECRET', 'your_app_secret_here')
            
        except Exception as e:
            print(f"获取快手App Secret失败: {str(e)}")
            return 'your_app_secret_here'

    def get_kuaishou_sign_secret(self):
        """获取快手API的Sign Secret"""
        try:
            # 首先尝试从配置文件读取
            config_path = get_config_path('config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    kuaishou_config = config_data.get('kuaishou_api_config', {})
                    sign_secret = kuaishou_config.get('signSecret')
                    if sign_secret:
                        return sign_secret
            
            # 如果配置文件中没有，返回默认值或环境变量
            return os.environ.get('KUAISHOU_SIGN_SECRET', 'your_sign_secret_here')
            
        except Exception as e:
            print(f"获取快手Sign Secret失败: {str(e)}")
            return 'your_sign_secret_here'

    def list_product(self):
        """上架商品"""
        try:
            # 获取表格选中的行（智能选择）
            selected_rows, selection_type = self.get_table3_effective_selected_rows()
            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要上架的商品（可以勾选复选框或选择表格行）")
                return
            
            print(f"使用{selection_type}模式选中了 {len(selected_rows)} 行商品")

            # 获取商品ID列和店铺名称列的索引
            product_id_col_index = -1
            shop_name_col_index = -1
            
            for col in range(self.table3.columnCount()):
                header_item = self.table3.horizontalHeaderItem(col)
                if header_item:
                    header_text = header_item.text()
                    if header_text == "商品ID":
                        product_id_col_index = col
                    elif header_text == "店铺名称":
                        shop_name_col_index = col

            if product_id_col_index == -1:
                QMessageBox.warning(self, "错误", "未找到商品ID列")
                return
                
            if shop_name_col_index == -1:
                QMessageBox.warning(self, "错误", "未找到店铺名称列")
                return

            # 构建商品操作列表
            product_operations = []
            
            for row in selected_rows:
                # 获取商品ID
                product_id_item = self.table3.item(row, product_id_col_index)
                if not product_id_item or not product_id_item.text():
                    continue
                    
                product_id = product_id_item.text().strip()
                
                # 获取店铺名称
                shop_name_item = self.table3.item(row, shop_name_col_index)
                if not shop_name_item or not shop_name_item.text():
                    continue
                    
                shop_name = shop_name_item.text().strip()
                
                # 清理店铺名称（去除评分括号）
                if '(' in shop_name and shop_name.endswith(')'):
                    shop_name = shop_name.split('(')[0].strip()
                
                # 添加商品操作信息
                product_operations.append({
                    'product_id': product_id,
                    'shop_name': shop_name,
                    'row': row
                })

            if not product_operations:
                QMessageBox.warning(self, "错误", "没有找到有效的商品信息")
                return

            # 确认操作
            reply = QMessageBox.question(
                self, 
                "确认上架", 
                f"将要上架 {len(product_operations)} 个商品\n选择方式：{selection_type}\n\n确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return

            # 清空选中行的操作状态列
            self.clear_operation_status_for_rows(selected_rows)

            # 执行上架操作
            self.perform_list_operation(product_operations)

        except Exception as e:
            print(f"上架商品时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"上架操作失败: {str(e)}")

    def perform_list_operation(self, product_operations):
        """执行上架操作"""
        try:
            # 读取账号配置
            config_path = get_config_path("账号管理.json")
            if not os.path.exists(config_path):
                QMessageBox.critical(self, "错误", "未找到账号配置文件")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 按店铺分组商品
            shop_products = {}
            for operation in product_operations:
                shop_name = operation['shop_name']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(operation)

            # 执行上架操作
            for shop_name, products in shop_products.items():
                print(f"开始上架店铺 {shop_name} 的商品...")
                
                # 查找店铺的accesstoken
                shop_info = self.find_shop_info(config_data, shop_name)
                if not shop_info:
                    print(f"未找到店铺 {shop_name} 的配置信息")
                    for product in products:
                        # 确保product是字典类型且包含product_id键
                        if isinstance(product, dict) and 'product_id' in product:
                            # 更新状态列
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 未找到店铺配置")
                        else:
                            print(f"警告：产品数据格式不正确: {product}")
                    continue

                access_token = shop_info.get('accesstoken')
                if not access_token:
                    print(f"店铺 {shop_name} 没有accesstoken")
                    for product in products:
                        # 确保product是字典类型且包含product_id键
                        if isinstance(product, dict) and 'product_id' in product:
                            # 更新状态列
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 缺少accesstoken")
                        else:
                            print(f"警告：产品数据格式不正确: {product}")
                    continue

                # 创建快手API实例
                try:
                    # 从配置文件或环境变量获取API密钥
                    app_key = shop_info.get('app_key') or self.get_kuaishou_app_key()
                    app_secret = shop_info.get('app_secret') or self.get_kuaishou_app_secret()
                    sign_secret = shop_info.get('signSecret') or self.get_kuaishou_sign_secret()
                    
                    api = KuaishouAPI(
                        app_key=app_key,
                        app_secret=app_secret,
                        sign_secret=sign_secret,
                        access_token=access_token
                    )
                    
                    # 为每个商品执行上架操作
                    for product in products:
                        # 确保product是字典类型且包含product_id键
                        if not isinstance(product, dict) or 'product_id' not in product:
                            print(f"警告：产品数据格式不正确: {product}")
                            continue
                            
                        product_id = product['product_id']
                        row = product.get('row', -1)
                        print(f"正在上架商品: {product_id}")
                        
                        # 更新状态为处理中
                        if row >= 0:
                            self.update_operation_status(row, "上架中...")
                        
                        try:
                            # 调用快手API上架商品
                            result = api.shelf_item(int(product_id))
                            
                            if result.get('success'):
                                print(f"商品 {product_id} 上架成功")
                                # 更新状态为成功
                                if row >= 0:
                                    self.update_operation_status(row, "上架成功")
                            else:
                                error_msg = result.get('message', '上架失败')
                                print(f"商品 {product_id} 上架失败: {error_msg}")
                                # 更新状态为失败
                                if row >= 0:
                                    self.update_operation_status(row, f"上架失败: {error_msg}")
                        except Exception as e:
                            error_msg = f"上架异常: {str(e)}"
                            print(f"商品 {product_id} 上架异常: {error_msg}")
                            # 更新状态为异常
                            if row >= 0:
                                self.update_operation_status(row, f"上架异常: {error_msg}")
                
                except Exception as e:
                    print(f"创建快手API实例失败: {str(e)}")
                    for product in products:
                        # 确保product是字典类型且包含product_id键
                        if isinstance(product, dict) and 'product_id' in product:
                            # 更新状态列
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"API初始化失败: {str(e)}")
                        else:
                            print(f"警告：产品数据格式不正确: {product}")

            # 操作完成，在控制台输出摘要
            print("上架操作完成，状态已更新到表格中")

        except Exception as e:
            print(f"执行上架操作时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"上架操作异常: {str(e)}")

    def show_list_results(self, results, success_count, failed_count):
        """显示上架结果"""
        try:
            total = success_count + failed_count
            
            # 创建结果对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("上架操作结果")
            dialog.setModal(True)
            dialog.resize(600, 400)
            
            layout = QVBoxLayout(dialog)
            
            # 结果统计
            summary_label = QLabel(f"上架操作完成 - 成功: {success_count}, 失败: {failed_count}")
            summary_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #2e7d32;
                    padding: 10px;
                    background-color: #e8f5e8;
                    border: 1px solid #4caf50;
                    border-radius: 5px;
                    margin: 5px;
                }
            """)
            layout.addWidget(summary_label)
            
            # 详细结果表格
            if results:
                results_table = QTableWidget()
                results_table.setColumnCount(4)
                results_table.setHorizontalHeaderLabels(["店铺名称", "商品ID", "状态", "说明"])
                results_table.setRowCount(len(results))
                
                for i, result in enumerate(results):
                    # 店铺名称
                    shop_item = QTableWidgetItem(result['shop_name'])
                    results_table.setItem(i, 0, shop_item)
                    
                    # 商品ID
                    product_item = QTableWidgetItem(result['product_id'])
                    results_table.setItem(i, 1, product_item)
                    
                    # 状态
                    status_text = "成功" if result['success'] else "失败"
                    status_item = QTableWidgetItem(status_text)
                    if result['success']:
                        status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
                    else:
                        status_item.setBackground(QColor(255, 200, 200))  # 浅红色
                    results_table.setItem(i, 2, status_item)
                    
                    # 说明
                    message_item = QTableWidgetItem(result['message'])
                    results_table.setItem(i, 3, message_item)
                
                # 调整列宽
                results_table.resizeColumnsToContents()
                results_table.horizontalHeader().setStretchLastSection(True)
                
                layout.addWidget(results_table)
            
            # 确定按钮
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            
            ok_button = QPushButton("确定")
            ok_button.clicked.connect(dialog.accept)
            button_layout.addWidget(ok_button)
            
            layout.addLayout(button_layout)
            
            # 显示对话框
            dialog.exec_()
            
            # 控制台输出结果摘要
            print(f"上架操作完成 - 成功: {success_count}, 失败: {failed_count}")
            
        except Exception as e:
            print(f"显示上架结果时出错: {str(e)}")
            QMessageBox.information(self, "提示", f"上架操作完成 - 成功: {success_count}, 失败: {failed_count}")

    def delist_junk_products(self):
        """下架垃圾商品（实际利润为负数且订单数大于等于设定值的商品）"""
        try:
            print("开始下架垃圾商品...")
            
            # 获取订单数输入框的值
            order_count_text = self.order_count_edit.text().strip()
            if not order_count_text:
                QMessageBox.warning(self, "提示", "请先在订单数输入框中输入最小订单数量")
                return
                
            try:
                min_order_count = int(order_count_text)
            except ValueError:
                QMessageBox.warning(self, "提示", "订单数输入框中请输入有效的数字")
                return
                
            if min_order_count <= 0:
                QMessageBox.warning(self, "提示", "订单数必须大于0")
                return
            
            # 获取列索引（以表头名索引）
            product_id_col_index = -1
            shop_name_col_index = -1
            actual_profit_col_index = -1
            order_count_col_index = -1
            
            for col in range(self.table3.columnCount()):
                header_item = self.table3.horizontalHeaderItem(col)
                if header_item:
                    header_text = header_item.text()
                    if header_text == "商品ID":
                        product_id_col_index = col
                    elif header_text == "店铺名称":
                        shop_name_col_index = col
                    elif header_text == "实际利润":
                        actual_profit_col_index = col
                    elif header_text == "订单数":
                        order_count_col_index = col
            
            print(f"列索引：商品ID={product_id_col_index}, 店铺名称={shop_name_col_index}, 实际利润={actual_profit_col_index}, 订单数={order_count_col_index}")
            
            # 检查是否找到必要的列
            if product_id_col_index == -1:
                QMessageBox.warning(self, "错误", "未找到商品ID列")
                return
            if shop_name_col_index == -1:
                QMessageBox.warning(self, "错误", "未找到店铺名称列")
                return
            if actual_profit_col_index == -1:
                QMessageBox.warning(self, "错误", "未找到实际利润列")
                return
            if order_count_col_index == -1:
                QMessageBox.warning(self, "错误", "未找到订单数列")
                return
            
            # 获取所有符合条件的商品行
            junk_products = []
            
            for row in range(self.table3.rowCount()):
                try:
                    # 获取实际利润
                    actual_profit_item = self.table3.item(row, actual_profit_col_index)
                    if not actual_profit_item or not actual_profit_item.text().strip():
                        continue
                        
                    # 获取订单数
                    order_count_item = self.table3.item(row, order_count_col_index)
                    if not order_count_item or not order_count_item.text().strip():
                        continue
                        
                    # 解析实际利润
                    actual_profit_text = actual_profit_item.text().strip()
                    try:
                        actual_profit = float(actual_profit_text)
                    except ValueError:
                        print(f"跳过行 {row+1}：无法解析实际利润 '{actual_profit_text}'")
                        continue
                        
                    # 解析订单数
                    order_count_text = order_count_item.text().strip()
                    try:
                        order_count = int(order_count_text)
                    except ValueError:
                        print(f"跳过行 {row+1}：无法解析订单数 '{order_count_text}'")
                        continue
                    
                    # 判断是否符合垃圾商品条件：实际利润为负数且订单数大于等于设定值
                    if actual_profit < 0 and order_count >= min_order_count:
                        # 获取商品ID和店铺名称
                        product_id_item = self.table3.item(row, product_id_col_index)
                        shop_name_item = self.table3.item(row, shop_name_col_index)
                        
                        if product_id_item and shop_name_item:
                            product_id = product_id_item.text().strip()
                            shop_name = shop_name_item.text().strip()
                            
                            if product_id and shop_name:
                                # 清理店铺名称，去掉括号里的评分
                                clean_shop_name = shop_name
                                if '(' in shop_name and shop_name.endswith(')'):
                                    clean_shop_name = shop_name.split('(')[0].strip()
                                elif '（' in shop_name and shop_name.endswith('）'):
                                    clean_shop_name = shop_name.split('（')[0].strip()
                                
                                junk_products.append({
                                    'row': row,
                                    'product_id': product_id,
                                    'shop_name': clean_shop_name,
                                    'original_shop_name': shop_name,
                                    'actual_profit': actual_profit,
                                    'order_count': order_count
                                })
                                print(f"找到垃圾商品：商品ID={product_id}, 店铺={clean_shop_name}, 实际利润={actual_profit}, 订单数={order_count}")
                                
                except Exception as e:
                    print(f"处理第 {row+1} 行时出错: {str(e)}")
                    continue
            
            if not junk_products:
                QMessageBox.information(self, "提示", 
                    f"没有找到符合条件的垃圾商品\n"
                    f"条件：实际利润<0 且 订单数≥{min_order_count}")
                return
            
            # 先清空整个操作状态列的所有状态
            all_rows = list(range(self.table3.rowCount()))
            self.clear_operation_status_for_rows(all_rows)
            print(f"已清空整个操作状态列（共 {len(all_rows)} 行）")
            
            # 显示确认对话框
            reply = QMessageBox.question(self, "确认下架", 
                f"找到 {len(junk_products)} 个垃圾商品符合条件：\n"
                f"• 实际利润 < 0\n"
                f"• 订单数 ≥ {min_order_count}\n\n"
                f"是否确认下架这些商品？",
                QMessageBox.Yes | QMessageBox.No)
            
            if reply != QMessageBox.Yes:
                return
            
            # 按店铺分组商品
            shop_groups = {}
            for product in junk_products:
                shop_name = product['shop_name']
                # 清理店铺名称，去掉括号里的评分
                clean_shop_name = shop_name
                if '(' in shop_name and shop_name.endswith(')'):
                    clean_shop_name = shop_name.split('(')[0].strip()
                elif '（' in shop_name and shop_name.endswith('）'):
                    clean_shop_name = shop_name.split('（')[0].strip()
                
                if clean_shop_name not in shop_groups:
                    shop_groups[clean_shop_name] = []
                shop_groups[clean_shop_name].append(product['product_id'])
            
            print(f"垃圾商品分组结果：{len(shop_groups)} 个店铺，共 {len(junk_products)} 个商品")
            
            # 构建包含行号的商品操作列表
            product_operations = []
            
            for product in junk_products:
                product_operations.append({
                    'shop_name': product['shop_name'],
                    'product_id': product['product_id'],
                    'row': product['row']
                })
            
            # 调用现有的下架方法
            self.perform_delist_operation(product_operations)
            
        except Exception as e:
            print(f"下架垃圾商品时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"下架垃圾商品时出错: {str(e)}")

    def show_table4_context_menu(self, position):
        """显示表格4的右键菜单（达人信息表格）"""
        context_menu = QMenu(self)

        # 添加菜单项
        copy_wechat_action = QAction("复制微信", self)
        copy_wechat_action.triggered.connect(self.copy_wechat)

        copy_phone_action = QAction("复制手机号", self)
        copy_phone_action.triggered.connect(self.copy_phone)

        copy_talent_id_action = QAction("复制达人ID", self)
        copy_talent_id_action.triggered.connect(self.copy_talent_id)

        copy_talent_nickname_action = QAction("复制达人昵称", self)
        copy_talent_nickname_action.triggered.connect(self.copy_talent_nickname)

        view_talent_info_action = QAction("查看达人信息", self)
        view_talent_info_action.triggered.connect(self.view_talent_info)

        # 将菜单项添加到菜单
        context_menu.addAction(copy_wechat_action)
        context_menu.addAction(copy_phone_action)
        context_menu.addSeparator()
        context_menu.addAction(copy_talent_id_action)
        context_menu.addAction(copy_talent_nickname_action)
        context_menu.addSeparator()
        context_menu.addAction(view_talent_info_action)

        # 显示右键菜单
        cursor_pos = QCursor.pos()
        context_menu.exec_(cursor_pos)

    def copy_wechat(self):
        """复制微信"""
        # 这里暂不实现具体功能，因为表格4中没有微信列
        print("复制微信功能待实现")

    def copy_phone(self):
        """复制手机号"""
        # 这里暂不实现具体功能，因为表格4中没有手机号列
        print("复制手机号功能待实现")

    def copy_talent_id(self):
        """复制达人ID"""
        selected_items = self.table4.selectedItems()
        if not selected_items:
            return

        # 查找达人ID列（第2列，索引为1）
        talent_ids = []
        for item in selected_items:
            if item.column() == 1:  # 达人ID列
                talent_id = item.text()
                if talent_id:
                    talent_ids.append(talent_id)

        if talent_ids:
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(talent_ids))
            print(f"已复制 {len(talent_ids)} 个达人ID到剪贴板")

    def copy_talent_nickname(self):
        """复制达人昵称"""
        selected_items = self.table4.selectedItems()
        if not selected_items:
            return

        # 查找达人昵称列（第3列，索引为2）
        talent_nicknames = []
        for item in selected_items:
            if item.column() == 2:  # 达人昵称列
                nickname = item.text()
                if nickname:
                    talent_nicknames.append(nickname)

        if talent_nicknames:
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(talent_nicknames))
            print(f"已复制 {len(talent_nicknames)} 个达人昵称到剪贴板")

    def view_talent_info(self):
        """查看达人信息"""
        selected_items = self.table4.selectedItems()
        if not selected_items:
            return

        # 获取选中行的达人ID
        selected_rows = set()
        for item in selected_items:
            selected_rows.add(item.row())

        talent_ids = []
        for row in selected_rows:
            talent_id_item = self.table4.item(row, 1)  # 达人ID列
            if talent_id_item and talent_id_item.text():
                talent_ids.append(talent_id_item.text())

        if talent_ids:
            print(f"查看达人信息: {talent_ids}")
            # 这里暂不实现具体功能

    def update_table1_numbers(self, column_index):
        """更新表格1的序号列"""
        try:
            # 暂时禁用排序，避免在更新序号时触发排序
            self.table1.setSortingEnabled(False)

            # 更新序号列
            for row in range(self.table1.rowCount()):
                # 创建序号单元格
                num_item = QTableWidgetItem(str(row + 1))
                num_item.setTextAlignment(Qt.AlignCenter)
                # 设置序号单元格
                self.table1.setItem(row, 0, num_item)

            # 重新启用排序
            self.table1.setSortingEnabled(True)
            print("表格1序号更新完成")
        except Exception as e:
            print(f"更新表格1序号时出错: {str(e)}")

    def update_table2_numbers(self, column_index):
        """更新表格2的序号列"""
        try:
            # 暂时禁用排序，避免在更新序号时触发排序
            self.table2.setSortingEnabled(False)

            # 更新序号列
            for row in range(self.table2.rowCount()):
                # 创建序号单元格
                num_item = QTableWidgetItem(str(row + 1))
                num_item.setTextAlignment(Qt.AlignCenter)
                # 设置序号单元格
                self.table2.setItem(row, 0, num_item)

            # 重新启用排序
            self.table2.setSortingEnabled(True)
            print("表格2序号更新完成")
        except Exception as e:
            print(f"更新表格2序号时出错: {str(e)}")

    def update_table3_numbers(self, column_index):
        """更新表格3的序号列"""
        try:
            # 暂时禁用排序，避免在更新序号时触发排序
            self.table3.setSortingEnabled(False)

            # 更新序号列 - 更新自定义Widget中的序号
            for row in range(self.table3.rowCount()):
                # 获取自定义Widget
                checkbox_widget = self.table3.cellWidget(row, 0)
                if checkbox_widget and isinstance(checkbox_widget, CheckBoxNumberWidget):
                    # 更新序号
                    checkbox_widget.set_number(row + 1)

            # 重新启用排序
            self.table3.setSortingEnabled(True)
            print("表格3序号更新完成")
        except Exception as e:
            print(f"更新表格3序号时出错: {str(e)}")

    def get_table3_selected_rows(self):
        """获取表格3中复选框选中的行"""
        selected_rows = []
        for row in range(self.table3.rowCount()):
            checkbox_widget = self.table3.cellWidget(row, 0)
            if checkbox_widget and isinstance(checkbox_widget, CheckBoxNumberWidget):
                if checkbox_widget.is_checked():
                    selected_rows.append(row)
        return selected_rows

    def get_table3_selected_table_rows(self):
        """获取表格3中表格选中的行"""
        selected_rows = []
        selected_items = self.table3.selectedItems()

        # 获取所有选中行的行号
        selected_row_set = set()
        for item in selected_items:
            selected_row_set.add(item.row())

        # 转换为列表并排序
        selected_rows = sorted(list(selected_row_set))
        return selected_rows

    def get_table3_effective_selected_rows(self):
        """获取表格3中有效选中的行（智能选择）
        
        逻辑：
        1. 优先使用复选框选中的行
        2. 如果没有复选框选中，则使用表格选中的行
        """
        # 先检查复选框选中的行
        checkbox_selected_rows = self.get_table3_selected_rows()
        
        if checkbox_selected_rows:
            # 如果有复选框选中，就使用复选框选择的行
            return checkbox_selected_rows, "复选框"
        else:
            # 如果没有复选框选中，就使用表格选中的行
            table_selected_rows = self.get_table3_selected_table_rows()
            return table_selected_rows, "表格选择"

    def select_all_table3_rows(self):
        """全选表格3的所有行"""
        for row in range(self.table3.rowCount()):
            checkbox_widget = self.table3.cellWidget(row, 0)
            if checkbox_widget and isinstance(checkbox_widget, CheckBoxNumberWidget):
                checkbox_widget.set_checked(True)

    def deselect_all_table3_rows(self):
        """取消全选表格3的所有行"""
        for row in range(self.table3.rowCount()):
            checkbox_widget = self.table3.cellWidget(row, 0)
            if checkbox_widget and isinstance(checkbox_widget, CheckBoxNumberWidget):
                checkbox_widget.set_checked(False)

    def analyze_talent_data_from_cache(self, cache_data):
        """从缓存数据中分析达人信息并显示在表格4中"""
        try:
            print("开始分析达人数据...")
            print(f"传入的缓存数据长度: {len(cache_data)}")

            # 检查缓存数据的完整性
            if len(cache_data) > 0:
                print(f"缓存第一条数据键: {list(cache_data[0].keys())}")
                # 检查关键字段是否存在
                key_fields = ["达人ID", "达人昵称"]
                for field in key_fields:
                    field_exists = field in cache_data[0]
                    field_value = cache_data[0].get(field, "不存在")
                    print(f"字段 '{field}' 存在: {field_exists}, 值: {field_value}")

            # 创建数据结构来存储达人的统计信息
            talent_stats = {}

            # 记录处理的订单数和跳过的订单数
            processed_count = 0
            skipped_count = 0
            empty_talent_id_count = 0

            # 遍历所有订单数据
            for order_idx, order in enumerate(cache_data):
                try:
                    # 获取达人ID
                    talent_id = order.get("达人ID", "")
                    if not talent_id:
                        empty_talent_id_count += 1
                        if empty_talent_id_count <= 5:  # 只打印前5条，避免日志过多
                            print(f"警告: 订单 {order_idx} 没有达人ID")
                        continue

                    # 获取达人昵称
                    talent_nickname = order.get("达人昵称", "")

                    # 创建键，只使用达人ID作为键，这样可以合并相同达人的数据
                    talent_key = talent_id

                    # 初始化达人统计信息
                    if talent_key not in talent_stats:
                        talent_stats[talent_key] = {
                            "talent_id": talent_id,
                            "talent_nickname": talent_nickname,
                            "order_count": 0
                        }

                    # 更新统计信息
                    talent_stats[talent_key]["order_count"] += 1

                    processed_count += 1

                except Exception as e:
                    print(f"处理订单 {order_idx} 时出错: {str(e)}")
                    skipped_count += 1

            # 打印统计信息
            print(f"处理订单数: {processed_count}, 跳过订单数: {skipped_count}")
            print(f"空达人ID订单数: {empty_talent_id_count}")

            if len(talent_stats) > 0:
                # 打印一些达人统计信息的示例
                sample_keys = list(talent_stats.keys())[:3]  # 取前3个作为示例
                print("达人统计信息示例:")
                for key in sample_keys:
                    print(f"达人ID: {key}")
                    print(f"  达人昵称: {talent_stats[key]['talent_nickname']}")
                    print(f"  订单数: {talent_stats[key]['order_count']}")

            # 显示统计结果到表格4
            self.display_talent_stats(talent_stats)
            print(f"从缓存数据中成功分析了 {len(talent_stats)} 个达人的信息")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"从缓存数据分析达人数据时出错: {str(e)}")

    def display_talent_stats(self, talent_stats):
        """将达人统计结果显示在表格4中"""
        try:
            print(f"开始显示达人统计结果，数据条数: {len(talent_stats) if talent_stats else 0}")

            # 清空表格
            self.table4.setRowCount(0)
            print("表格4已清空")

            # 强制更新UI
            QApplication.processEvents()

            # 如果没有数据，显示提示信息
            if not talent_stats:
                print("没有达人统计数据，显示提示信息")
                self.table4.setRowCount(1)
                self.table4.setItem(0, 0, QTableWidgetItem("1"))
                self.table4.setItem(0, 1, QTableWidgetItem("无数据"))
                self.table4.setItem(0, 2, QTableWidgetItem(""))
                self.table4.setItem(0, 3, QTableWidgetItem("0"))

                # 强制更新UI
                QApplication.processEvents()
                return

            # 准备数据列表，以便一次性添加到表格
            table_data = []
            print(f"开始处理 {len(talent_stats)} 个达人的数据")

            # 处理数据
            for talent_key, stats in talent_stats.items():
                try:
                    # 创建一行数据
                    row_data = {
                        "talent_id": stats["talent_id"],
                        "talent_nickname": stats["talent_nickname"],
                        "order_count": stats["order_count"]
                    }
                    table_data.append(row_data)
                except Exception as e:
                    print(f"准备达人数据时出错: {str(e)}, 达人ID: {talent_key}")
                    continue

            # 设置表格行数
            row_count = len(table_data)
            self.table4.setRowCount(row_count)
            print(f"设置表格4行数为: {row_count}")

            # 强制更新UI
            QApplication.processEvents()

            # 暂时禁用排序功能
            self.table4.setSortingEnabled(False)

            # 添加数据到表格
            for row_idx, row_data in enumerate(table_data):
                try:
                    # 序号列
                    num_item = QTableWidgetItem(str(row_idx + 1))
                    num_item.setTextAlignment(Qt.AlignCenter)
                    self.table4.setItem(row_idx, 0, num_item)

                    # 达人ID列
                    talent_id_item = QTableWidgetItem(row_data["talent_id"])
                    self.table4.setItem(row_idx, 1, talent_id_item)

                    # 达人昵称列
                    nickname_item = QTableWidgetItem(row_data["talent_nickname"])
                    self.table4.setItem(row_idx, 2, nickname_item)

                    # 订单数列
                    count_item = QTableWidgetItem()
                    count_item.setData(Qt.DisplayRole, row_data["order_count"])
                    count_item.setTextAlignment(Qt.AlignCenter)
                    self.table4.setItem(row_idx, 3, count_item)

                    # 每处理10行，强制更新一次UI
                    if (row_idx + 1) % 10 == 0:
                        QApplication.processEvents()
                        print(f"已处理 {row_idx + 1} 行数据")

                except Exception as e:
                    print(f"设置表格4行 {row_idx} 时出错: {str(e)}")
                    # 继续处理下一行，不中断

            # 重新启用排序功能
            self.table4.setSortingEnabled(True)

            # 按订单数量降序排序
            self.table4.sortItems(3, Qt.DescendingOrder)

            # 更新序号列
            self.update_table4_numbers(3)

            # 强制更新UI
            QApplication.processEvents()

            # 计算总达人数和总订单数
            total_talents = len(table_data)
            total_orders = sum(row_data["order_count"] for row_data in table_data)

            # 更新底部统计标签
            self.table4_stats_label.setText(f"达人: {total_talents} | 订单: {total_orders}")

            print(f"成功显示 {row_count} 个达人的统计结果，总订单数: {total_orders}")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"显示达人统计结果时出错: {str(e)}")

    def update_table4_numbers(self, column_index):
        """更新表格4的序号列"""
        try:
            # 暂时禁用排序，避免在更新序号时触发排序
            self.table4.setSortingEnabled(False)

            # 更新序号列
            for row in range(self.table4.rowCount()):
                # 创建序号单元格
                num_item = QTableWidgetItem(str(row + 1))
                num_item.setTextAlignment(Qt.AlignCenter)
                # 设置序号单元格
                self.table4.setItem(row, 0, num_item)

            # 重新启用排序
            self.table4.setSortingEnabled(True)
            print("表格4序号更新完成")
        except Exception as e:
            print(f"更新表格4序号时出错: {str(e)}")

    def handle_table1_sort(self, column_index):
        """处理表格1的排序事件"""
        try:
            # 如果点击的是序号列，则不进行排序
            if column_index == 0:
                return

            # 禁用排序功能，避免在更新序号时触发排序
            self.table1.setSortingEnabled(False)

            # 执行排序
            self.table1.sortItems(column_index, self.table1.horizontalHeader().sortIndicatorOrder())

            # 更新序号列
            self.update_table1_numbers(column_index)

            # 重新启用排序
            self.table1.setSortingEnabled(True)

            # 强制更新UI
            QApplication.processEvents()
        except Exception as e:
            print(f"处理表格1排序时出错: {str(e)}")

    def handle_table2_sort(self, column_index):
        """处理表格2的排序事件"""
        try:
            # 如果点击的是序号列，则不进行排序
            if column_index == 0:
                return

            # 禁用排序功能，避免在更新序号时触发排序
            self.table2.setSortingEnabled(False)

            # 执行排序
            self.table2.sortItems(column_index, self.table2.horizontalHeader().sortIndicatorOrder())

            # 更新序号列
            self.update_table2_numbers(column_index)

            # 重新启用排序
            self.table2.setSortingEnabled(True)

            # 强制更新UI
            QApplication.processEvents()
        except Exception as e:
            print(f"处理表格2排序时出错: {str(e)}")

    def handle_table3_sort(self, column_index):
        """处理表格3的排序事件"""
        try:
            # 如果点击的是序号列，则不进行排序
            if column_index == 0:
                return

            # 禁用排序功能，避免在更新序号时触发排序
            self.table3.setSortingEnabled(False)

            # 执行排序
            self.table3.sortItems(column_index, self.table3.horizontalHeader().sortIndicatorOrder())

            # 更新序号列
            self.update_table3_numbers(column_index)

            # 重新启用排序
            self.table3.setSortingEnabled(True)

            # 强制更新UI
            QApplication.processEvents()
        except Exception as e:
            print(f"处理表格3排序时出错: {str(e)}")

    def handle_table4_sort(self, column_index):
        """处理表格4的排序事件"""
        try:
            # 如果点击的是序号列，则不进行排序
            if column_index == 0:
                return

            # 禁用排序功能，避免在更新序号时触发排序
            self.table4.setSortingEnabled(False)

            # 执行排序
            self.table4.sortItems(column_index, self.table4.horizontalHeader().sortIndicatorOrder())

            # 更新序号列
            self.update_table4_numbers(column_index)

            # 重新启用排序
            self.table4.setSortingEnabled(True)

            # 强制更新UI
            QApplication.processEvents()
        except Exception as e:
            print(f"处理表格4排序时出错: {str(e)}")

    def analyze_product_data(self, table_widget, row_count, col_count):
        """分析商品数据并显示在表格3中"""
        try:
            # 查找各列的索引
            shop_col_index = -1
            order_remark_col_index = -1
            product_title_col_index = -1
            payment_col_index = -1
            actual_profit_col_index = -1
            last_status_col_index = -1
            product_id_col_index = -1
            merchant_code_col_index = -1
            shipping_fee_col_index = -1
            image_col_index = -1  # 图片列索引

            # 只查找"图片"列名进行精确匹配
            target_image_column_name = "图片"

            # 查找列索引
            for col in range(col_count):
                header_item = table_widget.horizontalHeaderItem(col)
                if not header_item:
                    continue

                header_text = header_item.text()
                if header_text == "店铺":
                    shop_col_index = col
                    print(f"找到店铺列，索引: {col}")
                elif header_text == "订单备注":
                    order_remark_col_index = col
                    print(f"找到订单备注列，索引: {col}")
                elif header_text == "商品标题":
                    product_title_col_index = col
                    print(f"找到商品标题列，索引: {col}")
                elif header_text == "付款":
                    payment_col_index = col
                    print(f"找到付款列，索引: {col}")
                elif header_text == "实际利润":
                    actual_profit_col_index = col
                    print(f"找到实际利润列，索引: {col}")
                elif header_text == "最后状态":
                    last_status_col_index = col
                    print(f"找到最后状态列，索引: {col}")
                elif header_text == "商品ID":
                    product_id_col_index = col
                    print(f"找到商品ID列，索引: {col}")
                elif header_text == "商家编码":
                    merchant_code_col_index = col
                    print(f"找到商家编码列，索引: {col}")
                elif header_text == "运费":
                    shipping_fee_col_index = col
                    print(f"找到运费列，索引: {col}")
                elif header_text == target_image_column_name:
                    image_col_index = col
                    print(f"找到图片列，索引: {col}, 列名: {header_text}")

            # 如果找不到必要的列，则返回
            if shop_col_index == -1 or product_title_col_index == -1:
                print("未找到必要的列，无法分析商品数据")
                return

            # 创建数据结构来存储商品的统计信息
            product_stats = {}

            # 遍历所有行，提取商品信息
            for row in range(row_count):
                # 获取店铺名称
                shop_name = ""
                if shop_col_index != -1:
                    shop_item = table_widget.item(row, shop_col_index)
                    if shop_item:
                        shop_name = shop_item.text()

                # 获取上家旺旺
                supplier_wangwang = ""
                if order_remark_col_index != -1:
                    remark_item = table_widget.item(row, order_remark_col_index)
                    if remark_item:
                        remark_text = remark_item.text()
                        if "/" in remark_text and "利润" in remark_text:
                            try:
                                start_idx = remark_text.find("/") + 1
                                end_idx = remark_text.find("利润", start_idx)
                                if end_idx > start_idx:
                                    supplier_wangwang = remark_text[start_idx:end_idx].strip()
                            except:
                                pass

                # 获取商品标题
                product_title = ""
                if product_title_col_index != -1:
                    title_item = table_widget.item(row, product_title_col_index)
                    if title_item:
                        product_title = title_item.text()

                # 获取价格
                price = 0
                if payment_col_index != -1:
                    payment_item = table_widget.item(row, payment_col_index)
                    if payment_item:
                        payment_text = payment_item.text()
                        try:
                            # 提取(前面的数字
                            if "(" in payment_text:
                                price_text = payment_text.split("(")[0].strip()
                                price = float(price_text.replace(',', ''))
                            else:
                                price = float(payment_text.replace(',', ''))
                        except:
                            pass

                # 获取最后状态
                last_status = ""
                if last_status_col_index != -1:
                    status_item = table_widget.item(row, last_status_col_index)
                    if status_item:
                        last_status = status_item.text()

                # 获取实际利润
                actual_profit = 0
                if actual_profit_col_index != -1:
                    profit_item = table_widget.item(row, actual_profit_col_index)
                    if profit_item:
                        profit_text = profit_item.text()
                        try:
                            actual_profit = float(profit_text.replace(',', ''))
                        except:
                            pass

                # 获取利润（从订单备注中提取）
                profit = 0
                if order_remark_col_index != -1:
                    remark_item = table_widget.item(row, order_remark_col_index)
                    if remark_item:
                        remark_text = remark_item.text()
                        if "利润" in remark_text and "元" in remark_text:
                            try:
                                start_idx = remark_text.find("利润") + len("利润")
                                end_idx = remark_text.find("元", start_idx)
                                if end_idx > start_idx:
                                    profit_text = remark_text[start_idx:end_idx].strip()
                                    profit = float(profit_text.replace(',', ''))
                            except:
                                pass

                # 获取运费
                shipping_fee = 0
                if shipping_fee_col_index != -1:
                    fee_item = table_widget.item(row, shipping_fee_col_index)
                    if fee_item:
                        fee_text = fee_item.text()
                        try:
                            shipping_fee = float(fee_text.replace(',', ''))
                        except:
                            pass

                # 获取商品ID
                product_id = ""
                if product_id_col_index != -1:
                    id_item = table_widget.item(row, product_id_col_index)
                    if id_item:
                        product_id = id_item.text()

                # 获取图片链接
                image_url = ""
                if image_col_index != -1:
                    image_item = table_widget.item(row, image_col_index)
                    if image_item:
                        image_url = image_item.text()
                        if row < 3:  # 调试信息
                            print(f"行 {row} 从图片列获取到图片链接: {image_url}")

                # 获取上家链接
                supplier_link = ""
                if merchant_code_col_index != -1:
                    code_item = table_widget.item(row, merchant_code_col_index)
                    if code_item:
                        merchant_code = code_item.text()
                        # 提取-后面的数字
                        if "-" in merchant_code:
                            try:
                                supplier_id = merchant_code.split("-")[-1].strip()
                                # 检查是否为纯数字
                                if supplier_id.isdigit():
                                    supplier_link = f"https://detail.1688.com/offer/{supplier_id}.html"
                            except:
                                pass
                        # 如果是纯数字，直接组合
                        elif merchant_code.isdigit():
                            supplier_link = f"https://detail.1688.com/offer/{merchant_code}.html"

                # 创建键，只使用商品标题作为键，这样可以合并相同商品的数据
                product_key = product_title

                # 初始化商品统计信息
                if product_key not in product_stats:
                    product_stats[product_key] = {
                        "shop_name": shop_name,
                        "supplier_wangwang": supplier_wangwang,
                        "product_title": product_title,
                        "image_url": image_url,  # 添加图片链接字段
                        "price": price,
                        "order_count": 0,
                        "refund_count": 0,
                        "refunded_count": 0,
                        "actual_profit": 0,
                        "negative_profit": 0,  # 添加负利润字段
                        "profit": 0,
                        "shipping_fee": 0,
                        "product_id": product_id,
                        "supplier_link": supplier_link,
                        "status": last_status
                    }

                # 更新统计信息
                product_stats[product_key]["order_count"] += 1
                product_stats[product_key]["actual_profit"] += actual_profit
                # 如果实际利润为负数，累加到负利润字段
                if actual_profit < 0:
                    product_stats[product_key]["negative_profit"] += actual_profit
                product_stats[product_key]["shipping_fee"] += shipping_fee

                # 统计退款数量
                if last_status == "退款成功":
                    product_stats[product_key]["refund_count"] += 1

                # 统计已退款订单数量
                if last_status == "已退款":
                    product_stats[product_key]["refunded_count"] += 1

                # 如果不是退款状态，则累加利润
                if last_status != "已退款" and last_status != "退款成功":
                    product_stats[product_key]["profit"] += profit

            # 显示统计结果到表格3
            self.display_product_stats(product_stats)
            print(f"成功分析了 {len(product_stats)} 个商品的数据")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"分析商品数据时出错: {str(e)}")

    def analyze_product_data_from_cache(self, cache_data):
        """从缓存数据中分析商品数据并显示在表格3中"""
        try:
            print("开始分析商品数据...")
            print(f"传入的缓存数据长度: {len(cache_data)}")

            # 检查缓存数据的完整性
            if len(cache_data) > 0:
                print(f"缓存第一条数据键: {list(cache_data[0].keys())}")

                # 检查"图片"字段是否存在
                all_keys = list(cache_data[0].keys())
                has_image_field = "图片" in all_keys
                print(f"数据中是否包含'图片'字段: {has_image_field}")
                if has_image_field:
                    print(f"'图片'字段示例值: {cache_data[0].get('图片', '')}")

                # 检查关键字段是否存在
                key_fields = ["店铺", "商品标题", "订单备注", "付款", "最后状态", "实际利润", "运费", "商品ID", "商家编码", "图片"]
                for field in key_fields:
                    field_exists = field in cache_data[0]
                    field_value = cache_data[0].get(field, "不存在")
                    print(f"字段 '{field}' 存在: {field_exists}, 值: {field_value}")

            # 创建数据结构来存储商品的统计信息
            product_stats = {}

            # 记录处理的订单数和跳过的订单数
            processed_count = 0
            skipped_count = 0
            empty_shop_count = 0
            empty_title_count = 0
            empty_remark_count = 0

            # 打印一条消息，表示开始处理商品数据
            print(f"开始处理 {len(cache_data)} 条订单数据，提取商品信息...")

            # 遍历所有订单数据
            for order_idx, order in enumerate(cache_data):
                try:
                    # 获取店铺名称
                    shop_name = order.get("店铺", "")
                    if not shop_name:
                        empty_shop_count += 1
                        if empty_shop_count <= 5:  # 只打印前5条，避免日志过多
                            print(f"警告: 订单 {order_idx} 没有店铺名称")
                        continue

                    # 获取订单备注
                    remark_text = order.get("订单备注", "")
                    if not remark_text:
                        empty_remark_count += 1
                        if empty_remark_count <= 5:  # 只打印前5条，避免日志过多
                            print(f"警告: 订单 {order_idx} 没有订单备注")

                    # 获取上家旺旺 - 从订单备注中提取
                    supplier_wangwang = ""
                    is_new_method = False  # 标记是否使用新方法提取
                    if remark_text and "利润" in remark_text:
                        try:
                            # 方法1：原来的提取方式，如果有"/"分隔符
                            if "/" in remark_text:
                                start_idx = remark_text.find("/") + 1
                                end_idx = remark_text.find("利润", start_idx)
                                if end_idx > start_idx:
                                    supplier_wangwang = remark_text[start_idx:end_idx].strip()
                                    is_new_method = False

                            # 方法2：新增判断，如果没有"/"但有"上家店铺"和"利润"
                            elif "上家店铺" in remark_text:
                                # 提取上家店铺和利润之间的文字作为上家旺旺
                                start_idx = remark_text.find("上家店铺") + len("上家店铺")
                                end_idx = remark_text.find("利润", start_idx)
                                if end_idx > start_idx:
                                    supplier_wangwang = remark_text[start_idx:end_idx].strip()
                                    is_new_method = True
                                    print(f"订单 {order_idx} 使用新方法提取上家旺旺: '{supplier_wangwang}'")
                        except Exception as e:
                            print(f"提取上家旺旺时出错: {str(e)}, 订单 {order_idx}, 订单备注: {remark_text}")

                    # 获取商品标题
                    product_title = order.get("商品标题", "")
                    if not product_title:
                        empty_title_count += 1
                        if empty_title_count <= 5:  # 只打印前5条，避免日志过多
                            print(f"警告: 订单 {order_idx} 没有商品标题")
                        continue

                    # 获取价格
                    price = 0
                    payment_text = order.get("付款", "0")
                    try:
                        # 提取(前面的数字
                        if "(" in payment_text:
                            price_text = payment_text.split("(")[0].strip()
                            price = float(price_text.replace(',', ''))
                        else:
                            price = float(str(payment_text).replace(',', ''))
                    except Exception as e:
                        print(f"提取价格时出错: {str(e)}, 订单 {order_idx}, 付款文本: {payment_text}")

                    # 获取最后状态
                    last_status = order.get("最后状态", "")

                    # 获取实际利润
                    actual_profit = 0
                    profit_text = order.get("实际利润", "0")
                    try:
                        actual_profit = float(str(profit_text).replace(',', ''))
                    except Exception as e:
                        print(f"提取实际利润时出错: {str(e)}, 订单 {order_idx}, 实际利润文本: {profit_text}")

                    # 获取利润（从订单备注中提取）
                    profit = 0
                    if remark_text and "利润" in remark_text and "元" in remark_text:
                        try:
                            start_idx = remark_text.find("利润") + len("利润")
                            end_idx = remark_text.find("元", start_idx)
                            if end_idx > start_idx:
                                profit_text = remark_text[start_idx:end_idx].strip()
                                profit = float(profit_text.replace(',', ''))
                        except Exception as e:
                            print(f"提取利润时出错: {str(e)}, 订单 {order_idx}, 订单备注: {remark_text}")

                    # 获取运费
                    shipping_fee = 0
                    fee_text = order.get("运费", "0")
                    try:
                        shipping_fee = float(str(fee_text).replace(',', ''))
                    except Exception as e:
                        print(f"提取运费时出错: {str(e)}, 订单 {order_idx}, 运费文本: {fee_text}")

                    # 获取商品ID
                    product_id = order.get("商品ID", "")

                    # 获取图片链接 - 只从"图片"字段精确匹配
                    image_url = ""

                    # 只使用"图片"字段进行精确匹配
                    if "图片" in order and order["图片"]:
                        image_url = order["图片"]
                        if order_idx < 3:  # 调试信息
                            print(f"订单 {order_idx} 从字段 '图片' 获取到图片链接: {image_url}")

                    # 调试信息：打印图片链接提取情况
                    if order_idx < 3:  # 只打印前3条，避免日志过多
                        print(f"订单 {order_idx} 图片链接提取:")
                        print(f"  图片字段值: {order.get('图片', '无此字段')}")
                        print(f"  最终提取的图片链接: '{image_url}'")
                        print(f"  商品标题: {product_title}")

                    # 获取上家链接 - 从商家编码中提取
                    supplier_link = ""
                    merchant_code = order.get("商家编码", "")
                    if merchant_code:
                        # 提取-后面的数字
                        if "-" in merchant_code:
                            try:
                                supplier_id = merchant_code.split("-")[-1].strip()
                                # 检查是否为纯数字
                                if supplier_id.isdigit():
                                    supplier_link = f"https://detail.1688.com/offer/{supplier_id}.html"
                            except Exception as e:
                                print(f"提取上家链接时出错: {str(e)}, 订单 {order_idx}, 商家编码: {merchant_code}")
                        # 如果是纯数字，直接组合
                        elif merchant_code.isdigit():
                            supplier_link = f"https://detail.1688.com/offer/{merchant_code}.html"

                    # 创建键，只使用商品标题作为键，这样可以合并相同商品的数据
                    product_key = product_title

                    # 初始化商品统计信息
                    if product_key not in product_stats:
                        product_stats[product_key] = {
                            "shop_name": shop_name,
                            "supplier_wangwang": supplier_wangwang,
                            "product_title": product_title,
                            "image_url": image_url,  # 添加图片链接字段
                            "price": price,
                            "order_count": 0,
                            "refund_count": 0,
                            "refunded_count": 0,
                            "actual_profit": 0,
                            "negative_profit": 0,  # 添加负利润字段
                            "profit": 0,
                            "shipping_fee": 0,
                            "product_id": product_id,
                            "supplier_link": supplier_link,
                            "status": last_status,
                            "is_new_method": is_new_method  # 添加新方法标记
                        }

                    # 更新统计信息
                    product_stats[product_key]["order_count"] += 1
                    product_stats[product_key]["actual_profit"] += actual_profit
                    # 如果实际利润为负数，累加到负利润字段
                    if actual_profit < 0:
                        product_stats[product_key]["negative_profit"] += actual_profit
                    product_stats[product_key]["shipping_fee"] += shipping_fee

                    # 统计退款数量
                    if last_status == "退款成功":
                        product_stats[product_key]["refund_count"] += 1

                    # 统计已退款订单数量
                    if last_status == "已退款":
                        product_stats[product_key]["refunded_count"] += 1

                    # 如果不是退款状态，则累加利润
                    if last_status != "已退款" and last_status != "退款成功":
                        product_stats[product_key]["profit"] += profit

                    processed_count += 1

                except Exception as e:
                    print(f"处理订单 {order_idx} 时出错: {str(e)}")
                    skipped_count += 1

            # 打印统计信息
            print(f"处理订单数: {processed_count}, 跳过订单数: {skipped_count}")
            print(f"空店铺名称订单数: {empty_shop_count}, 空商品标题订单数: {empty_title_count}, 空订单备注订单数: {empty_remark_count}")

            if len(product_stats) > 0:
                # 打印一些商品统计信息的示例
                sample_keys = list(product_stats.keys())[:3]  # 取前3个作为示例
                print("商品统计信息示例:")
                for key in sample_keys:
                    print(f"商品: {key}")
                    print(f"  店铺名称: {product_stats[key]['shop_name']}")
                    print(f"  上家旺旺: {product_stats[key]['supplier_wangwang']}")
                    print(f"  商品标题: {product_stats[key]['product_title']}")
                    print(f"  图片链接: {product_stats[key]['image_url']}")
                    print(f"  价格: {product_stats[key]['price']}")
                    print(f"  订单数: {product_stats[key]['order_count']}")
                    print(f"  实际利润: {product_stats[key]['actual_profit']}")
                    print(f"  利润: {product_stats[key]['profit']}")
                    print(f"  运费: {product_stats[key]['shipping_fee']}")
                    print(f"  商品ID: {product_stats[key]['product_id']}")
                    print(f"  上家链接: {product_stats[key]['supplier_link']}")

            # 显示统计结果到表格3
            self.display_product_stats(product_stats)
            print(f"从缓存数据中成功分析了 {len(product_stats)} 个商品的数据")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"从缓存数据分析商品数据时出错: {str(e)}")

    def display_product_stats(self, product_stats):
        """将商品统计结果显示在表格3中"""
        try:
            # 清空表格
            self.table3.setRowCount(0)

            # 强制更新UI
            QApplication.processEvents()

            # 如果没有数据，显示提示信息
            if not product_stats:
                self.table3.setRowCount(1)
                self.table3.setItem(0, 0, QTableWidgetItem("1"))  # 序号列
                self.table3.setItem(0, 1, QTableWidgetItem("无数据"))
                self.table3.setItem(0, 2, QTableWidgetItem(""))
                self.table3.setItem(0, 3, QTableWidgetItem(""))
                self.table3.setItem(0, 4, QTableWidgetItem(""))  # 图片列
                self.table3.setItem(0, 5, QTableWidgetItem("0"))
                self.table3.setItem(0, 6, QTableWidgetItem("0"))
                self.table3.setItem(0, 7, QTableWidgetItem("0%"))
                self.table3.setItem(0, 8, QTableWidgetItem("0"))
                self.table3.setItem(0, 9, QTableWidgetItem("0"))  # 负利润列
                self.table3.setItem(0, 10, QTableWidgetItem("0"))
                self.table3.setItem(0, 11, QTableWidgetItem("0"))
                self.table3.setItem(0, 12, QTableWidgetItem("0%"))
                self.table3.setItem(0, 13, QTableWidgetItem(""))
                self.table3.setItem(0, 14, QTableWidgetItem(""))
                self.table3.setItem(0, 15, QTableWidgetItem(""))  # 操作状态列

                # 强制更新UI
                QApplication.processEvents()
                return

            # 准备数据列表，以便一次性添加到表格
            table_data = []

            # 创建一个新的字典，用于合并相同商品的数据
            merged_product_stats = {}

            # 合并相同商品的数据
            for product_key, stats in product_stats.items():
                # 只使用商品标题作为汇总字段
                product_title = stats["product_title"]
                product_info = product_title

                if product_info not in merged_product_stats:
                    # 创建新的合并记录
                    merged_product_stats[product_info] = {
                        "shop_name": stats["shop_name"],
                        "supplier_wangwang": stats["supplier_wangwang"],
                        "product_title": stats["product_title"],
                        "image_url": stats["image_url"],  # 添加图片链接字段
                        "price": stats["price"],
                        "order_count": 0,
                        "refund_count": 0,
                        "refunded_count": 0,
                        "actual_profit": 0,
                        "negative_profit": 0,  # 添加负利润字段
                        "profit": 0,
                        "shipping_fee": 0,
                        "product_id": stats["product_id"],
                        "supplier_link": stats["supplier_link"],
                        "status": stats["status"],
                        "is_new_method": stats.get("is_new_method", False)  # 添加新方法标记
                    }

                # 累加数据
                merged_product_stats[product_info]["order_count"] += stats["order_count"]
                merged_product_stats[product_info]["refund_count"] += stats["refund_count"]
                merged_product_stats[product_info]["refunded_count"] += stats["refunded_count"]
                merged_product_stats[product_info]["actual_profit"] += stats["actual_profit"]
                merged_product_stats[product_info]["negative_profit"] += stats.get("negative_profit", 0)  # 累加负利润
                merged_product_stats[product_info]["profit"] += stats["profit"]
                merged_product_stats[product_info]["shipping_fee"] += stats["shipping_fee"]

            print(f"合并后的商品数量: {len(merged_product_stats)}")

            # 处理合并后的数据
            for product_info, stats in merged_product_stats.items():
                try:
                    # 计算退货率
                    refund_rate = 0
                    effective_orders = stats["order_count"] - stats["refunded_count"]
                    if effective_orders > 0:
                        refund_rate = (stats["refund_count"] / effective_orders) * 100

                    # 计算利润率
                    profit_rate = 0
                    if stats["price"] > 0:
                        profit_rate = (stats["profit"] / stats["price"]) * 100

                    # 创建一行数据
                    row_data = {
                        "shop_name": stats["shop_name"],
                        "supplier_wangwang": stats["supplier_wangwang"],
                        "product_title": stats["product_title"],
                        "image_url": stats["image_url"],  # 添加图片链接字段
                        "price": round(stats["price"], 2),
                        "order_count": stats["order_count"],
                        "refund_rate": refund_rate,
                        "actual_profit": round(stats["actual_profit"], 2),
                        "negative_profit": round(stats.get("negative_profit", 0), 2),  # 添加负利润字段
                        "profit": round(stats["profit"], 2),
                        "shipping_fee": round(stats["shipping_fee"], 2),
                        "profit_rate": profit_rate,
                        "product_id": stats["product_id"],
                        "supplier_link": stats["supplier_link"],
                        "status": stats["status"],
                        "is_new_method": stats.get("is_new_method", False)  # 添加新方法标记
                    }
                    table_data.append(row_data)
                except Exception as e:
                    print(f"准备商品数据时出错: {str(e)}, 商品: {product_info}")
                    continue

            # 设置表格行数
            row_count = len(table_data)
            self.table3.setRowCount(row_count)
            print(f"设置表格3行数为: {row_count}")

            # 强制更新UI
            QApplication.processEvents()

            # 暂时禁用排序功能
            self.table3.setSortingEnabled(False)

            # 添加数据到表格
            for row_idx, row_data in enumerate(table_data):
                try:
                    # 序号列 - 使用自定义Widget包含复选框和序号
                    checkbox_widget = CheckBoxNumberWidget(row_idx + 1)
                    self.table3.setCellWidget(row_idx, 0, checkbox_widget)

                    # 店铺名称列
                    self.table3.setItem(row_idx, 1, QTableWidgetItem(row_data["shop_name"]))

                    # 上家旺旺列
                    wangwang_item = QTableWidgetItem(row_data["supplier_wangwang"])
                    # 如果是用新方法提取的，设置字体加粗
                    if row_data.get("is_new_method", False):
                        font = wangwang_item.font()
                        font.setBold(True)
                        wangwang_item.setFont(font)
                    self.table3.setItem(row_idx, 2, wangwang_item)

                    # 商品标题列
                    self.table3.setItem(row_idx, 3, QTableWidgetItem(row_data["product_title"]))

                    # 图片列 - 显示图片链接
                    image_url = row_data["image_url"]
                    image_item = QTableWidgetItem(image_url)
                    image_item.setTextAlignment(Qt.AlignCenter)
                    self.table3.setItem(row_idx, 4, image_item)

                    # 调试信息：打印图片列显示情况
                    if row_idx < 3:  # 只打印前3行，避免日志过多
                        print(f"表格3第{row_idx}行图片列显示:")
                        print(f"  商品标题: {row_data['product_title']}")
                        print(f"  图片链接: '{image_url}'")
                        print(f"  图片链接长度: {len(image_url) if image_url else 0}")
                        print(f"  是否为空: {not bool(image_url)}")

                    # 价格列
                    price_item = QTableWidgetItem()
                    price_item.setData(Qt.DisplayRole, row_data["price"])
                    price_item.setTextAlignment(Qt.AlignCenter)
                    self.table3.setItem(row_idx, 5, price_item)

                    # 订单数列
                    count_item = QTableWidgetItem()
                    count_item.setData(Qt.DisplayRole, row_data["order_count"])
                    count_item.setTextAlignment(Qt.AlignCenter)
                    self.table3.setItem(row_idx, 6, count_item)

                    # 退货率列
                    refund_item = QTableWidgetItem(f"{row_data['refund_rate']:.2f}%")
                    refund_item.setTextAlignment(Qt.AlignCenter)
                    refund_item.setData(Qt.UserRole, row_data["refund_rate"])
                    self.table3.setItem(row_idx, 7, refund_item)

                    # 实际利润列
                    actual_profit_item = QTableWidgetItem()
                    actual_profit_item.setData(Qt.DisplayRole, row_data["actual_profit"])
                    actual_profit_item.setTextAlignment(Qt.AlignCenter)
                    self.table3.setItem(row_idx, 8, actual_profit_item)

                    # 负利润列 - 动态查找"负利润"列
                    negative_profit_col_index = -1
                    for col in range(self.table3.columnCount()):
                        header_item = self.table3.horizontalHeaderItem(col)
                        if header_item and header_item.text() == "负利润":
                            negative_profit_col_index = col
                            break

                    if negative_profit_col_index != -1:
                        negative_profit_item = QTableWidgetItem()
                        negative_profit_item.setData(Qt.DisplayRole, row_data.get("negative_profit", 0))
                        negative_profit_item.setTextAlignment(Qt.AlignCenter)
                        self.table3.setItem(row_idx, negative_profit_col_index, negative_profit_item)

                    # 利润列 - 动态查找"利润"列
                    profit_col_index = -1
                    for col in range(self.table3.columnCount()):
                        header_item = self.table3.horizontalHeaderItem(col)
                        if header_item and header_item.text() == "利润":
                            profit_col_index = col
                            break

                    if profit_col_index != -1:
                        profit_item = QTableWidgetItem()
                        profit_item.setData(Qt.DisplayRole, row_data["profit"])
                        profit_item.setTextAlignment(Qt.AlignCenter)
                        self.table3.setItem(row_idx, profit_col_index, profit_item)

                    # 运费列 - 动态查找"运费"列
                    shipping_col_index = -1
                    for col in range(self.table3.columnCount()):
                        header_item = self.table3.horizontalHeaderItem(col)
                        if header_item and header_item.text() == "运费":
                            shipping_col_index = col
                            break

                    if shipping_col_index != -1:
                        shipping_item = QTableWidgetItem()
                        shipping_item.setData(Qt.DisplayRole, row_data["shipping_fee"])
                        shipping_item.setTextAlignment(Qt.AlignCenter)
                        self.table3.setItem(row_idx, shipping_col_index, shipping_item)

                    # 利润率列 - 动态查找"利润率"列
                    rate_col_index = -1
                    for col in range(self.table3.columnCount()):
                        header_item = self.table3.horizontalHeaderItem(col)
                        if header_item and header_item.text() == "利润率":
                            rate_col_index = col
                            break

                    if rate_col_index != -1:
                        rate_item = QTableWidgetItem(f"{row_data['profit_rate']:.2f}%")
                        rate_item.setTextAlignment(Qt.AlignCenter)
                        rate_item.setData(Qt.UserRole, row_data["profit_rate"])
                        self.table3.setItem(row_idx, rate_col_index, rate_item)

                    # 商品ID列 - 动态查找"商品ID"列
                    id_col_index = -1
                    for col in range(self.table3.columnCount()):
                        header_item = self.table3.horizontalHeaderItem(col)
                        if header_item and header_item.text() == "商品ID":
                            id_col_index = col
                            break

                    if id_col_index != -1:
                        self.table3.setItem(row_idx, id_col_index, QTableWidgetItem(row_data["product_id"]))

                    # 上家链接列 - 动态查找"上家链接"列
                    link_col_index = -1
                    for col in range(self.table3.columnCount()):
                        header_item = self.table3.horizontalHeaderItem(col)
                        if header_item and header_item.text() == "上家链接":
                            link_col_index = col
                            break

                    if link_col_index != -1:
                        self.table3.setItem(row_idx, link_col_index, QTableWidgetItem(row_data["supplier_link"]))

                    # 操作状态列 - 动态查找"操作状态"列
                    status_col_index = -1
                    for col in range(self.table3.columnCount()):
                        header_item = self.table3.horizontalHeaderItem(col)
                        if header_item and header_item.text() == "操作状态":
                            status_col_index = col
                            break

                    if status_col_index != -1:
                        self.table3.setItem(row_idx, status_col_index, QTableWidgetItem(row_data["status"]))

                    # 检查标红条件
                    order_count = row_data.get("order_count", 0)
                    negative_profit = abs(row_data.get("negative_profit", 0))  # 负利润的绝对值
                    actual_profit = row_data.get("actual_profit", 0)
                    red_color = QColor(255, 0, 0)  # 红色

                    # 标红逻辑1：负利润大于实际利润时标红整行
                    if negative_profit > actual_profit:
                        for col in range(self.table3.columnCount()):
                            item = self.table3.item(row_idx, col)
                            if item:
                                item.setForeground(red_color)

                    # 标红逻辑2：订单数大于3且负利润大于实际利润时额外标红订单数列
                    if order_count > 3 and negative_profit > actual_profit:
                        # 查找订单数列
                        order_count_col_index = -1
                        for col in range(self.table3.columnCount()):
                            header_item = self.table3.horizontalHeaderItem(col)
                            if header_item and header_item.text() == "订单数":
                                order_count_col_index = col
                                break

                        if order_count_col_index != -1:
                            order_count_item = self.table3.item(row_idx, order_count_col_index)
                            if order_count_item:
                                # 订单数列已经在整行标红中被标红了，这里可以添加其他特殊标记
                                # 比如设置粗体字体
                                font = order_count_item.font()
                                font.setBold(True)
                                order_count_item.setFont(font)

                    # 每处理10行，强制更新一次UI
                    if (row_idx + 1) % 10 == 0:
                        QApplication.processEvents()
                        print(f"已处理 {row_idx + 1} 行数据")

                except Exception as e:
                    print(f"设置表格3行 {row_idx} 时出错: {str(e)}")
                    # 继续处理下一行，不中断

            # 重新启用排序功能
            self.table3.setSortingEnabled(True)

            # 按订单数量降序排序
            self.table3.sortItems(6, Qt.DescendingOrder)

            # 更新序号列
            self.update_table3_numbers(6)

            # 强制更新UI
            QApplication.processEvents()
            QApplication.processEvents()

            # 计算总商品数、总订单数、总利润、总实际利润和总负利润
            total_products = len(table_data)
            total_orders = sum(row_data["order_count"] for row_data in table_data)
            total_profit = sum(row_data["profit"] for row_data in table_data)
            total_actual_profit = sum(row_data["actual_profit"] for row_data in table_data)
            total_negative_profit = sum(row_data.get("negative_profit", 0) for row_data in table_data)

            # 更新底部统计标签
            self.table3_stats_label.setText(f"商品: {total_products} | 订单: {total_orders} | 利润: {total_profit:.2f} | 实际利润: {total_actual_profit:.2f} | 负利润: {total_negative_profit:.2f}")



            print(f"成功显示 {row_count} 个商品的统计结果，总订单数: {total_orders}，总利润: {total_profit:.2f}，总实际利润: {total_actual_profit:.2f}，总负利润: {total_negative_profit:.2f}")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"显示商品统计结果时出错: {str(e)}")

    def on_image_copy_failed(self, error_msg):
        """图片复制失败回调"""
        print(f"复制图片失败: {error_msg}")

    def on_table3_filter_clicked(self):
        """处理表格3智能筛选按钮点击事件"""
        try:
            # 获取三个输入框的值
            order_count_text = self.order_count_edit.text().strip()
            keyword = self.keyword_edit.text().strip()
            return_rate_text = self.return_rate_edit.text().strip()

            # 如果三个输入框都为空，取消所有选中
            if not order_count_text and not keyword and not return_rate_text:
                self.deselect_all_table3_rows()
                print("📋 已取消所有复选框选中状态")
                return

            # 验证订单数输入（如果有输入的话）
            min_order_count = None
            if order_count_text:
                try:
                    min_order_count = int(order_count_text)
                except ValueError:
                    QMessageBox.warning(self, "输入错误", "请输入有效的订单数（整数）")
                    return

            # 解析退货率条件（如果有输入的话）
            return_rate_condition = None
            if return_rate_text:
                return_rate_condition = self.parse_return_rate_condition(return_rate_text)
                if return_rate_condition is None:
                    QMessageBox.warning(self, "输入错误", "退货率格式错误，请输入如：<=5、>=10、<3、>8、=0 等格式")
                    return

            # 根据输入情况决定筛选方式
            conditions = []
            if order_count_text:
                conditions.append("订单数")
            if keyword:
                conditions.append("关键字")
            if return_rate_text:
                conditions.append("退货率")

            if len(conditions) > 1:
                # 多条件组合筛选
                self.filter_table3_multi_conditions(min_order_count, keyword, return_rate_condition)
            elif order_count_text:
                # 只输入订单数：按订单数筛选
                self.filter_table3_by_order_count(min_order_count)
            elif keyword:
                # 只输入关键字：按关键字筛选
                self.search_table3_by_keyword(keyword)
            elif return_rate_text:
                # 只输入退货率：按退货率筛选
                self.filter_table3_by_return_rate(return_rate_condition)

        except Exception as e:
            print(f"处理表格3智能筛选时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"筛选失败: {str(e)}")

    def filter_table3_by_order_count(self, min_order_count):
        """根据订单数筛选表格3数据 - 勾选符合条件的复选框"""
        try:
            # 先取消所有复选框的选中状态
            self.deselect_all_table3_rows()

            # 遍历所有行，勾选符合条件的复选框
            row_count = self.table3.rowCount()
            filtered_count = 0

            for row in range(row_count):
                # 获取订单数（第6列，索引为6）
                order_item = self.table3.item(row, 6)
                if order_item and order_item.text():
                    try:
                        order_count = int(order_item.text())
                        if order_count >= min_order_count:
                            # 勾选符合条件的复选框
                            checkbox_widget = self.table3.cellWidget(row, 0)
                            if checkbox_widget and hasattr(checkbox_widget, 'set_checked'):
                                checkbox_widget.set_checked(True)
                                filtered_count += 1
                    except (ValueError, TypeError):
                        # 如果订单数无法转换为整数，跳过该行
                        continue

            print(f"📊 订单数筛选完成：勾选了 {filtered_count} 条记录（订单数 >= {min_order_count}）")

        except Exception as e:
            print(f"筛选表格3订单数时出错: {str(e)}")
            raise

    def search_table3_by_keyword(self, keyword):
        """根据关键字搜索表格3商品标题 - 勾选符合条件的复选框"""
        try:
            # 先取消所有复选框的选中状态
            self.deselect_all_table3_rows()

            # 遍历所有行，勾选符合条件的复选框
            row_count = self.table3.rowCount()
            found_count = 0
            keyword_lower = keyword.lower()  # 转换为小写进行不区分大小写的搜索

            for row in range(row_count):
                # 获取商品标题（第3列，索引为3）
                title_item = self.table3.item(row, 3)
                if title_item and title_item.text():
                    try:
                        product_title = title_item.text()
                        if keyword_lower in product_title.lower():
                            # 勾选符合条件的复选框
                            checkbox_widget = self.table3.cellWidget(row, 0)
                            if checkbox_widget and hasattr(checkbox_widget, 'set_checked'):
                                checkbox_widget.set_checked(True)
                                found_count += 1
                    except (AttributeError, TypeError):
                        # 如果获取标题失败，跳过该行
                        continue

            print(f"🔍 关键字搜索完成：勾选了 {found_count} 条记录（包含关键字：{keyword}）")

        except Exception as e:
            print(f"搜索表格3关键字时出错: {str(e)}")
            raise

    def parse_return_rate_condition(self, return_rate_text):
        """解析退货率条件字符串"""
        try:
            import re

            # 支持的格式：<=5, >=10, <3, >8, =0, 5 (默认为<=)
            if re.match(r'^<=\s*\d+(\.\d+)?$', return_rate_text):
                # <=5
                value = float(return_rate_text.replace('<=', '').strip())
                return ('<=', value)
            elif re.match(r'^>=\s*\d+(\.\d+)?$', return_rate_text):
                # >=10
                value = float(return_rate_text.replace('>=', '').strip())
                return ('>=', value)
            elif re.match(r'^<\s*\d+(\.\d+)?$', return_rate_text):
                # <3
                value = float(return_rate_text.replace('<', '').strip())
                return ('<', value)
            elif re.match(r'^>\s*\d+(\.\d+)?$', return_rate_text):
                # >8
                value = float(return_rate_text.replace('>', '').strip())
                return ('>', value)
            elif re.match(r'^=\s*\d+(\.\d+)?$', return_rate_text):
                # =0
                value = float(return_rate_text.replace('=', '').strip())
                return ('=', value)
            elif re.match(r'^\d+(\.\d+)?$', return_rate_text):
                # 5 (默认为<=5)
                value = float(return_rate_text.strip())
                return ('<=', value)
            else:
                return None

        except Exception as e:
            print(f"解析退货率条件时出错: {str(e)}")
            return None

    def filter_table3_by_return_rate(self, return_rate_condition):
        """根据退货率筛选表格3数据 - 勾选符合条件的复选框"""
        try:
            # 先取消所有复选框的选中状态
            self.deselect_all_table3_rows()

            # 遍历所有行，勾选符合条件的复选框
            row_count = self.table3.rowCount()
            filtered_count = 0
            operator, target_value = return_rate_condition

            for row in range(row_count):
                # 获取退货率（第7列，索引为7）
                return_rate_item = self.table3.item(row, 7)
                if return_rate_item and return_rate_item.text():
                    try:
                        # 移除百分号并转换为数字
                        return_rate_text = return_rate_item.text().replace('%', '').strip()
                        return_rate = float(return_rate_text)

                        # 根据操作符判断是否符合条件
                        match = False
                        if operator == '<=':
                            match = return_rate <= target_value
                        elif operator == '>=':
                            match = return_rate >= target_value
                        elif operator == '<':
                            match = return_rate < target_value
                        elif operator == '>':
                            match = return_rate > target_value
                        elif operator == '=':
                            match = abs(return_rate - target_value) < 0.01  # 浮点数比较

                        if match:
                            # 勾选符合条件的复选框
                            checkbox_widget = self.table3.cellWidget(row, 0)
                            if checkbox_widget and hasattr(checkbox_widget, 'set_checked'):
                                checkbox_widget.set_checked(True)
                                filtered_count += 1
                    except (ValueError, TypeError):
                        # 如果退货率无法转换为数字，跳过该行
                        continue

            print(f"📊 退货率筛选完成：勾选了 {filtered_count} 条记录（退货率 {operator} {target_value}）")

        except Exception as e:
            print(f"筛选表格3退货率时出错: {str(e)}")
            raise

    def filter_table3_multi_conditions(self, min_order_count, keyword, return_rate_condition):
        """多条件组合筛选：同时满足多个条件 - 勾选符合条件的复选框"""
        try:
            # 先取消所有复选框的选中状态
            self.deselect_all_table3_rows()

            # 遍历所有行，勾选同时满足所有条件的复选框
            row_count = self.table3.rowCount()
            found_count = 0
            keyword_lower = keyword.lower() if keyword else ""

            for row in range(row_count):
                all_match = True

                # 检查订单数条件（第6列，索引为6）
                if min_order_count is not None:
                    order_item = self.table3.item(row, 6)
                    order_match = False
                    if order_item and order_item.text():
                        try:
                            order_count = int(order_item.text())
                            if order_count >= min_order_count:
                                order_match = True
                        except (ValueError, TypeError):
                            pass
                    if not order_match:
                        all_match = False

                # 检查关键字条件（第3列，索引为3）
                if keyword and all_match:
                    title_item = self.table3.item(row, 3)
                    keyword_match = False
                    if title_item and title_item.text():
                        try:
                            product_title = title_item.text()
                            if keyword_lower in product_title.lower():
                                keyword_match = True
                        except (AttributeError, TypeError):
                            pass
                    if not keyword_match:
                        all_match = False

                # 检查退货率条件（第7列，索引为7）
                if return_rate_condition and all_match:
                    return_rate_item = self.table3.item(row, 7)
                    return_rate_match = False
                    if return_rate_item and return_rate_item.text():
                        try:
                            return_rate_text = return_rate_item.text().replace('%', '').strip()
                            return_rate = float(return_rate_text)

                            operator, target_value = return_rate_condition
                            if operator == '<=':
                                return_rate_match = return_rate <= target_value
                            elif operator == '>=':
                                return_rate_match = return_rate >= target_value
                            elif operator == '<':
                                return_rate_match = return_rate < target_value
                            elif operator == '>':
                                return_rate_match = return_rate > target_value
                            elif operator == '=':
                                return_rate_match = abs(return_rate - target_value) < 0.01
                        except (ValueError, TypeError):
                            pass
                    if not return_rate_match:
                        all_match = False

                # 只有同时满足所有条件才勾选
                if all_match:
                    checkbox_widget = self.table3.cellWidget(row, 0)
                    if checkbox_widget and hasattr(checkbox_widget, 'set_checked'):
                        checkbox_widget.set_checked(True)
                        found_count += 1

            # 构建条件描述
            conditions_desc = []
            if min_order_count is not None:
                conditions_desc.append(f"订单数 >= {min_order_count}")
            if keyword:
                conditions_desc.append(f"包含关键字：{keyword}")
            if return_rate_condition:
                operator, value = return_rate_condition
                conditions_desc.append(f"退货率 {operator} {value}")

            print(f"🎯 多条件筛选完成：勾选了 {found_count} 条记录（{' 且 '.join(conditions_desc)}）")

        except Exception as e:
            print(f"多条件筛选表格3时出错: {str(e)}")
            raise

    def filter_table3_combined(self, min_order_count, keyword):
        """组合筛选：同时满足订单数和关键字条件 - 勾选符合条件的复选框（保留兼容性）"""
        return self.filter_table3_multi_conditions(min_order_count, keyword, None)




class DetailStatisticsWindow(QMainWindow):
    """详情统计独立窗口类，用于单独运行时使用"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("详情统计")
        self.resize(1200, 800)

        # 初始化UI
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 添加详情统计组件
        self.stats_widget = DetailStatisticsWidget()
        main_layout.addWidget(self.stats_widget)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DetailStatisticsWindow()
    window.setWindowTitle("详情统计")
    window.resize(1500, 800)
    window.show()
    sys.exit(app.exec_())


