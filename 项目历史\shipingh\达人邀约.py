import sys
import os
import random
import json
import time
import traceback
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QRadioButton, QCheckBox, QLabel, QLineEdit, QTableWidget,
                            QTableWidgetItem, QPushButton, QGroupBox, QScrollArea,
                            QGridLayout, QFormLayout, QComboBox, QTextEdit, QSpinBox,
                            QButtonGroup, QHeaderView, QFrame, QMessageBox, QSizePolicy, QSpacerItem, QMenu,
                            QDialog)
from PyQt5.QtCore import Qt, QSize, QUrl, QTimer, pyqtSignal, pyqtSlot, QMetaObject, Q_ARG, QDateTime, QStringListModel
from PyQt5.QtGui import QFont, QIntValidator
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage, QWebEngineProfile
from PyQt5.QtNetwork import QNetworkCookie
import requests
import threading
from concurrent.futures import ThreadPoolExecutor
import concurrent

# 为独立运行模式添加依赖处理
try:
    from 微信api import WechatAPI, send_request_with_cookie
    HAS_WECHAT_API = True
    print("[达人邀约] 成功导入微信API模块")
except ImportError:
    HAS_WECHAT_API = False
    print("[达人邀约] 未找到微信API模块，使用模拟模式")
    
    class WechatAPI:
        """模拟微信API类，用于独立运行"""
        def __init__(self, *args, **kwargs):
            self.current_url = "https://shop.channels.weixin.qq.com/"
            
        def get_current_shop_info(self):
            return {"name": "测试店铺", "id": "test_shop_id"}
            
        def get_shop_list(self):
            return [
                {"name": "测试店铺1", "id": "shop1"},
                {"name": "测试店铺2", "id": "shop2"},
                {"name": "测试店铺3", "id": "shop3"}
            ]
            
        def get_goods_list(self, *args, **kwargs):
            return {
                "goods": [
                    {"title": "测试商品1", "id": "goods1"},
                    {"title": "测试商品2", "id": "goods2"}
                ]
            }
            
        def execute_js(self, *args, **kwargs):
            return {"status": "success"}
            
    def send_request_with_cookie(*args, **kwargs):
        """模拟请求函数"""
        return {"status": "success", "data": []}

try:
    from tool.config_utils import get_server_url
    HAS_CONFIG_UTILS = True
    print("[达人邀约] 成功导入配置工具模块")
except ImportError:
    HAS_CONFIG_UTILS = False
    print("[达人邀约] 未找到配置工具模块，使用默认配置")
    
    def get_server_url():
        """返回默认服务器URL"""
        return "http://localhost:8000"


def get_config_file_path(relative_path):
    """获取配置文件的绝对路径，始终使用程序运行目录

    Args:
        relative_path (str): 相对路径

    Returns:
        str: 配置文件的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 打包后的应用程序，使用可执行文件所在目录
        base_path = os.path.dirname(sys.executable)
        print(f"[达人邀约-配置路径] 使用打包环境程序目录: {base_path}")
    else:
        # 开发环境，使用脚本所在目录
        base_path = os.path.dirname(os.path.abspath(__file__))
        print(f"[达人邀约-配置路径] 使用开发环境目录: {base_path}")

    full_path = os.path.join(base_path, relative_path)
    print(f"[达人邀约-配置路径] 完整路径: {full_path}")
    return full_path


class ShopManager:
    """独立的店铺管理类，从config\账号列表.json文件读取店铺信息"""

    @staticmethod
    def get_shop_list():
        """从配置文件获取店铺列表

        Returns:
            list: 店铺信息列表，每个元素包含店铺的完整信息
        """
        try:
            # 使用配置文件路径函数获取正确的配置文件路径
            config_path = get_config_file_path(os.path.join("config", "账号列表.json"))
            print(f"[ShopManager] 尝试读取配置文件: {config_path}")

            if not os.path.exists(config_path):
                print(f"[ShopManager] 配置文件不存在: {config_path}")
                return []

            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if not isinstance(data, list):
                print(f"[ShopManager] 配置文件格式错误，应该是列表格式")
                return []

            # 处理每个店铺的cookie字段，转换为cookies_list格式
            for shop in data:
                if "cookie" in shop and "cookies_list" not in shop:
                    try:
                        # cookie字段是JSON字符串，需要解析
                        cookie_str = shop["cookie"]
                        if isinstance(cookie_str, str):
                            cookies_list = json.loads(cookie_str)
                            shop["cookies_list"] = cookies_list
                            print(f"[ShopManager] 为店铺 '{shop.get('店铺名称', 'Unknown')}' 转换cookie格式")
                    except Exception as e:
                        print(f"[ShopManager] 解析店铺 '{shop.get('店铺名称', 'Unknown')}' 的cookie失败: {e}")
                        shop["cookies_list"] = []

            print(f"[ShopManager] 成功加载 {len(data)} 个店铺信息")
            return data

        except Exception as e:
            print(f"[ShopManager] 读取店铺列表失败: {e}")
            return []

    @staticmethod
    def get_shop_names():
        """获取所有店铺名称列表

        Returns:
            list: 店铺名称列表
        """
        shops = ShopManager.get_shop_list()
        shop_names = []

        for shop in shops:
            shop_name = shop.get("店铺名称", "")
            if shop_name and shop_name not in shop_names:
                shop_names.append(shop_name)

        print(f"[ShopManager] 获取到 {len(shop_names)} 个店铺名称: {shop_names}")
        return shop_names

    @staticmethod
    def get_shop_by_name(shop_name):
        """根据店铺名称获取店铺信息

        Args:
            shop_name (str): 店铺名称

        Returns:
            dict: 店铺信息，如果未找到返回None
        """
        print(f"[ShopManager] 开始重新读取店铺 '{shop_name}' 的最新信息...")
        shops = ShopManager.get_shop_list()

        for shop in shops:
            if shop.get("店铺名称") == shop_name:
                print(f"[ShopManager] ✅ 找到店铺 '{shop_name}' 的信息")

                # 详细打印cookie信息
                cookies_list = shop.get('cookies_list', [])
                if cookies_list:
                    print(f"[ShopManager] 📋 店铺 '{shop_name}' 的Cookie信息:")
                    print(f"[ShopManager]   - Cookie数量: {len(cookies_list)}")

                    # 检查关键cookie字段
                    cookie_dict = {c['name']: c['value'] for c in cookies_list}
                    biz_magic = cookie_dict.get('biz_magic', '')
                    biz_token = cookie_dict.get('biz_token', '')

                    print(f"[ShopManager]   - biz_magic: {'✅ 存在' if biz_magic else '❌ 缺失'}")
                    print(f"[ShopManager]   - biz_token: {'✅ 存在' if biz_token else '❌ 缺失'}")

                    if biz_magic:
                        print(f"[ShopManager]   - biz_magic前10位: {biz_magic[:10]}...")
                    if biz_token:
                        print(f"[ShopManager]   - biz_token前10位: {biz_token[:10]}...")
                else:
                    print(f"[ShopManager] ⚠️ 店铺 '{shop_name}' 没有Cookie信息")

                return shop

        print(f"[ShopManager] ❌ 未找到店铺 '{shop_name}' 的信息")
        return None

    @staticmethod
    def get_next_shop_name(current_shop_name):
        """获取下一个店铺名称（跳过已在日志中的店铺）

        Args:
            current_shop_name (str): 当前店铺名称

        Returns:
            str: 下一个店铺名称，如果没有则返回None
        """
        shop_names = ShopManager.get_shop_names()
        if not shop_names:
            print(f"[ShopManager] 没有可用的店铺")
            return None

        print(f"[ShopManager] 查找店铺 '{current_shop_name}' 的下一个店铺")
        print(f"[ShopManager] 所有店铺: {shop_names}")

        # 查找当前店铺在列表中的索引
        try:
            current_index = shop_names.index(current_shop_name)
            print(f"[ShopManager] 当前店铺 '{current_shop_name}' 在列表中的索引: {current_index}")
        except ValueError:
            print(f"[ShopManager] 当前店铺 '{current_shop_name}' 不在列表中，从第一个店铺开始")
            current_index = -1

        # 从当前索引开始，循环查找下一个未在日志中的店铺
        for i in range(1, len(shop_names) + 1):
            next_index = (current_index + i) % len(shop_names)
            next_shop = shop_names[next_index]
            print(f"[ShopManager] 检查第 {i} 个店铺: '{next_shop}' (索引: {next_index})")

            # 检查该店铺是否已在日志中
            if not ShopManager.check_shop_in_log(next_shop):
                print(f"[ShopManager] ✅ 找到下一个未在日志中的店铺: '{next_shop}'")
                return next_shop
            else:
                print(f"[ShopManager] ❌ 店铺 '{next_shop}' 已在日志中，继续查找")

        print(f"[ShopManager] ⚠️ 所有店铺都已在日志中，无法找到下一个店铺")

        # 再次检查所有店铺的状态，用于调试
        print(f"[ShopManager] 调试：重新检查所有店铺状态")
        for shop in shop_names:
            in_log = ShopManager.check_shop_in_log(shop)
            print(f"[ShopManager] 调试：店铺 '{shop}' 在日志中: {in_log}")

        return None

    @staticmethod
    def check_shop_in_log(shop_name):
        """检查店铺是否在当天的邀约日志中

        Args:
            shop_name (str): 店铺名称

        Returns:
            bool: 如果在日志中返回True，否则返回False
        """
        try:
            # 使用配置文件路径函数获取正确的日志文件路径
            log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))
            print(f"[ShopManager] 尝试读取日志文件: {log_file_path}")

            if not os.path.exists(log_file_path):
                print(f"[ShopManager] 日志文件不存在，店铺 '{shop_name}' 未在日志中")
                return False

            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # 获取当前日期
            current_date = time.strftime("%Y-%m-%d")
            print(f"[ShopManager] 检查店铺 '{shop_name}' 是否在 {current_date} 的日志中")

            # 检查店铺是否在当天的日志中
            for entry in log_data:
                entry_date = entry.get("date", "")
                entry_shop = entry.get("shop_name", "")

                if entry_shop == shop_name and entry_date.startswith(current_date):
                    print(f"[ShopManager] 店铺 '{shop_name}' 在日志中有记录 (日期: {entry_date})")
                    return True

            print(f"[ShopManager] 店铺 '{shop_name}' 未在当天日志中")
            return False

        except Exception as e:
            print(f"[ShopManager] 检查店铺是否在日志中失败: {e}")
            return False

    @staticmethod
    def check_all_shops_in_log():
        """检查所有店铺是否都在当天的日志中

        Returns:
            bool: 如果所有店铺都在日志中返回True，否则返回False
        """
        shop_names = ShopManager.get_shop_names()
        if not shop_names:
            print(f"[ShopManager] 没有店铺，认为所有店铺都已完成")
            return True

        print(f"[ShopManager] 检查所有 {len(shop_names)} 个店铺是否都在日志中")

        for shop_name in shop_names:
            if not ShopManager.check_shop_in_log(shop_name):
                print(f"[ShopManager] 店铺 '{shop_name}' 未在日志中，还有店铺需要邀约")
                return False

        print(f"[ShopManager] 所有店铺都已在日志中")
        return True


class InviteLogDialog(QDialog):
    """邀约日志对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("邀约日志")
        self.setFixedSize(520, 620)  # 设置固定大小为520×620
        # 使用配置文件路径函数获取正确的日志文件路径
        self.log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))
        print(f"[邀约日志] 日志文件路径: {self.log_file_path}")

        # 设置窗口标志，使其成为非模态对话框
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint | Qt.Window)

        # 设置样式表，移除边框
        self.setStyleSheet("""
            QDialog {
                border: none;
                background-color: white;
            }
        """)

        # 创建布局
        layout = QVBoxLayout(self)

        # 创建表格
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(5)  # 5列（增加序号列）
        self.log_table.setHorizontalHeaderLabels(["序号", "店铺名称", "状态", "次数", "邀约记录"])

        # 设置表格样式
        self.log_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 4px;
                border: 1px solid #d0d0d0;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                font-weight: bold;
            }
        """)

        # 设置列宽
        self.log_table.setColumnWidth(0, 50)  # 序号
        self.log_table.setColumnWidth(1, 120)  # 店铺名称
        self.log_table.setColumnWidth(2, 80)  # 状态
        self.log_table.setColumnWidth(3, 50)  # 次数
        self.log_table.setColumnWidth(4, 200)  # 邀约记录

        # 设置表格的选择行为
        self.log_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.log_table.setSelectionMode(QTableWidget.SingleSelection)

        # 设置表头
        self.log_table.horizontalHeader().setStretchLastSection(True)
        self.log_table.verticalHeader().setVisible(False)

        # 启用右键菜单
        self.log_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.log_table.customContextMenuRequested.connect(self.show_context_menu)

        # 添加表格到布局
        layout.addWidget(self.log_table)

        # 创建统计信息区域
        stats_frame = QFrame()
        stats_frame.setStyleSheet("background-color: #f5f5f5; border-top: 1px solid #e0e0e0;")
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setContentsMargins(10, 5, 10, 5)

        # 统计标签
        self.shop_count_label = QLabel("店铺数: 0")
        self.shop_count_label.setStyleSheet("font-size: 12px; color: #333333;")

        self.invitation_count_label = QLabel("邀约人数: 0")
        self.invitation_count_label.setStyleSheet("font-size: 12px; color: #333333;")

        # 添加标签到统计布局
        stats_layout.addWidget(self.shop_count_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.invitation_count_label)

        # 添加统计区域到主布局
        layout.addWidget(stats_frame)

        # 加载日志数据
        self.load_log_data()

    def show_context_menu(self, position):
        """显示右键菜单"""
        # 创建菜单
        menu = QMenu(self)

        # 设置菜单样式，移除系统级阴影效果，修复右下角圆角背景显示异常问题
        menu.setWindowFlags(menu.windowFlags() | Qt.NoDropShadowWindowHint)

        # 获取当前选中的行
        selected_rows = self.log_table.selectionModel().selectedRows()

        # 只有在有选中行的情况下才显示这些菜单项
        if selected_rows:
            # 添加进入达人后台操作
            enter_backend_action = menu.addAction("进入达人后台")
            enter_backend_action.triggered.connect(self.enter_daren_backend)

            # 添加分隔线
            menu.addSeparator()

            # 添加删除操作
            delete_action = menu.addAction("删除选中项")
            delete_action.triggered.connect(self.delete_selected_log)

            # 添加分隔线
            menu.addSeparator()

        # 无论是否有选中行，都显示全选删除选项
        if self.log_table.rowCount() > 0:
            delete_all_action = menu.addAction("全选删除")
            delete_all_action.triggered.connect(self.delete_all_logs)

        # 只有菜单有内容时才显示
        if not menu.isEmpty():
            # 在鼠标位置显示菜单
            menu.exec_(self.log_table.viewport().mapToGlobal(position))



    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)

        # 设置窗口标题栏颜色为白色
        if sys.platform == 'win32':
            try:
                import ctypes
                DWMWA_CAPTION_COLOR = 35  # Windows 11 22000以上版本适用

                # 将颜色值从 #FFFFFF 转换为 COLORREF 值 (BGR格式)
                # 白色的 COLORREF 值为 0x00FFFFFF
                caption_color = ctypes.c_int(0x00FFFFFF)

                # 设置窗口标题栏颜色
                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    int(self.winId()),
                    DWMWA_CAPTION_COLOR,
                    ctypes.byref(caption_color),
                    ctypes.sizeof(caption_color)
                )
                print("邀约日志窗口标题栏颜色已设置为白色")
            except Exception as e:
                print(f"设置邀约日志窗口标题栏颜色失败: {str(e)}")

    def update_statistics(self, log_data):
        """更新统计信息"""
        if not log_data:
            self.shop_count_label.setText("店铺数: 0")
            self.invitation_count_label.setText("邀约人数: 0")
            return

        # 计算店铺数（去重）
        unique_shops = set()
        total_invitations = 0

        for entry in log_data:
            shop_name = entry.get("shop_name", "")
            if shop_name:
                unique_shops.add(shop_name)

            # 累加邀约次数
            count = entry.get("count", 0)
            if isinstance(count, (int, float)):
                total_invitations += count

        # 更新标签
        self.shop_count_label.setText(f"店铺数: {len(unique_shops)}")
        self.invitation_count_label.setText(f"邀约人数: {total_invitations}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 接受关闭事件，允许窗口被销毁
        event.accept()

    def delete_selected_log(self):
        """删除选中的日志条目"""
        # 获取当前选中的行
        selected_rows = self.log_table.selectionModel().selectedRows()

        if not selected_rows:
            return

        # 确认删除
        reply = QMessageBox.question(self, "确认删除", "确定要删除选中的日志条目吗？",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # 获取日志文件路径
        log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))

        # 如果日志文件不存在，直接返回
        if not os.path.exists(log_file_path):
            QMessageBox.warning(self, "错误", "日志文件不存在")
            return

        try:
            # 读取日志文件
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # 获取选中行的店铺名称和时间
            row_index = selected_rows[0].row()
            shop_name = self.log_table.item(row_index, 1).text()
            time_value = self.log_table.item(row_index, 4).text()

            # 查找并删除匹配的日志条目
            for i, entry in enumerate(log_data):
                if entry.get("shop_name") == shop_name and entry.get("time") == time_value:
                    del log_data[i]
                    break

            # 保存更新后的日志
            with open(log_file_path, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            # 重新加载日志数据
            self.load_log_data()

            print(f"[邀约日志] 已删除日志条目: {shop_name} - {time_value}")

        except Exception as e:
            print(f"[错误] 删除邀约日志条目失败: {e}")
            QMessageBox.warning(self, "错误", f"删除日志条目失败: {e}")

    def delete_all_logs(self):
        """删除所有日志条目"""
        # 检查表格是否为空
        if self.log_table.rowCount() == 0:
            return

        # 确认删除
        reply = QMessageBox.question(self, "确认全部删除",
                                    "确定要删除所有日志条目吗？此操作不可恢复！",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # 再次确认
        reply = QMessageBox.warning(self, "二次确认",
                                   "删除所有日志将导致所有店铺状态被重置，可能会重复邀约。\n\n确定要继续吗？",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # 获取日志文件路径
        log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))

        # 如果日志文件不存在，直接返回
        if not os.path.exists(log_file_path):
            QMessageBox.warning(self, "错误", "日志文件不存在")
            return

        try:
            # 创建空的日志文件
            with open(log_file_path, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)

            # 重新加载日志数据
            self.load_log_data()

            print("[邀约日志] 已删除所有日志条目")
            QMessageBox.information(self, "删除成功", "已成功删除所有日志条目")

        except Exception as e:
            print(f"[错误] 删除所有邀约日志条目失败: {e}")
            QMessageBox.warning(self, "错误", f"删除所有日志条目失败: {e}")

    def load_log_data(self):
        """从文件加载日志数据"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.log_file_path), exist_ok=True)

            # 如果日志文件不存在，创建一个空的日志文件
            if not os.path.exists(self.log_file_path):
                with open(self.log_file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False)
                return

            # 读取日志文件
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # 填充表格
            self.log_table.setRowCount(len(log_data))

            for row, entry in enumerate(log_data):
                # 设置序号
                seq_item = QTableWidgetItem(str(row + 1))
                seq_item.setTextAlignment(Qt.AlignCenter)
                self.log_table.setItem(row, 0, seq_item)

                # 设置店铺名称
                shop_name_item = QTableWidgetItem(entry.get("shop_name", ""))
                shop_name_item.setTextAlignment(Qt.AlignCenter)
                self.log_table.setItem(row, 1, shop_name_item)

                # 设置状态
                status_item = QTableWidgetItem(entry.get("status", ""))
                status_item.setTextAlignment(Qt.AlignCenter)
                self.log_table.setItem(row, 2, status_item)

                # 设置次数
                count_item = QTableWidgetItem(str(entry.get("count", 0)))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.log_table.setItem(row, 3, count_item)

                # 设置邀约记录时间
                time_item = QTableWidgetItem(entry.get("time", ""))
                time_item.setTextAlignment(Qt.AlignCenter)
                self.log_table.setItem(row, 4, time_item)

            # 按时间倒序排序（最新的记录在最上面）
            self.log_table.sortItems(4, Qt.DescendingOrder)

            # 更新序号（排序后重新编号）
            for row in range(self.log_table.rowCount()):
                seq_item = QTableWidgetItem(str(row + 1))
                seq_item.setTextAlignment(Qt.AlignCenter)
                self.log_table.setItem(row, 0, seq_item)

            # 更新统计信息
            self.update_statistics(log_data)

        except Exception as e:
            print(f"[错误] 加载邀约日志失败: {e}")
            # 如果加载失败，显示一个空表格
            self.log_table.setRowCount(0)
            # 重置统计信息
            self.shop_count_label.setText("店铺数: 0")
            self.invitation_count_label.setText("邀约人数: 0")

    @staticmethod
    def has_log_entry(shop_name):
        """检查店铺是否在当天的邀约日志中有记录

        Args:
            shop_name (str): 店铺名称

        Returns:
            bool: 如果店铺在日志中有记录返回True，否则返回False
        """
        try:
            # 使用配置文件路径函数获取正确的日志文件路径
            log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))
            print(f"[邀约日志] 尝试读取日志文件: {log_file_path}")

            # 如果日志文件不存在，直接返回False
            if not os.path.exists(log_file_path):
                print(f"[邀约日志] 日志文件不存在，店铺 '{shop_name}' 未在日志中")
                return False

            # 读取日志文件
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # 获取当前日期
            current_date = time.strftime("%Y-%m-%d")

            # 检查店铺是否在当天的日志中
            for entry in log_data:
                if (entry.get("shop_name") == shop_name and
                    entry.get("date", "").startswith(current_date)):
                    print(f"[邀约日志] 店铺 '{shop_name}' 在日志中有记录")
                    return True

            print(f"[邀约日志] 店铺 '{shop_name}' 未在日志中")
            return False
        except Exception as e:
            print(f"[错误] 检查邀约日志失败: {e}")
            return False

    @staticmethod
    def update_log_entry(shop_name, status, count=0):
        """更新日志中的店铺状态

        Args:
            shop_name (str): 店铺名称
            status (str): 新的状态信息
            count (int): 邀约次数

        Returns:
            bool: 如果更新成功返回True，否则返回False
        """
        try:
            # 使用配置文件路径函数获取正确的日志文件路径
            log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))
            print(f"[邀约日志] 尝试读取日志文件: {log_file_path}")

            # 如果日志文件不存在，直接返回False
            if not os.path.exists(log_file_path):
                print(f"[邀约日志] 日志文件不存在，无法更新店铺 '{shop_name}' 的状态")
                return False

            # 读取日志文件
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # 获取当前日期
            current_date = time.strftime("%Y-%m-%d")
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")

            # 查找并更新店铺状态
            updated = False
            for entry in log_data:
                if (entry.get("shop_name") == shop_name and
                    entry.get("date", "").startswith(current_date)):
                    # 更新状态和次数
                    entry["status"] = status
                    if "已达上限" in status:
                        entry["count"] = 200
                    else:
                        entry["count"] = count
                    entry["time"] = current_time  # 更新时间
                    updated = True
                    break

            # 如果没有找到匹配的记录，返回False
            if not updated:
                print(f"[邀约日志] 未找到店铺 '{shop_name}' 的记录，无法更新状态")
                return False

            # 保存更新后的日志
            with open(log_file_path, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            print(f"[邀约日志] 已更新店铺 '{shop_name}' 的状态为: {status}")
            return True

        except Exception as e:
            print(f"[错误] 更新邀约日志失败: {e}")
            return False

    @staticmethod
    def add_log_entry(shop_name, status, count=0):
        """添加一条日志记录

        Args:
            shop_name (str): 店铺名称
            status (str): 状态信息
            count (int): 邀约次数，如果状态为"已达上限"则为200，其他为0

        Returns:
            bool: 如果是重复记录返回True，否则返回False
        """
        print(f"[DEBUG 邀约日志] ===== add_log_entry 被调用 =====")
        print(f"[DEBUG 邀约日志] 参数: shop_name='{shop_name}', status='{status}', count={count}")
        try:
            # 使用配置文件路径函数获取正确的日志文件路径
            log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))
            print(f"[邀约日志] 尝试读取日志文件: {log_file_path}")

            # 确保配置目录存在
            os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

            # 读取现有日志
            log_data = []
            if os.path.exists(log_file_path):
                try:
                    with open(log_file_path, 'r', encoding='utf-8') as f:
                        log_data = json.load(f)
                except:
                    log_data = []

            # 获取当前日期
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            current_date = current_time.split()[0]  # 提取日期部分

            # 检查店铺是否已经存在于当天的日志中，如果存在则更新而不是跳过
            existing_entry_index = -1
            for i, entry in enumerate(log_data):
                if entry.get("shop_name") == shop_name and entry.get("date") == current_date:
                    existing_entry_index = i
                    print(f"[邀约日志] 店铺 '{shop_name}' 已存在于今日日志中，将更新记录")
                    break

            # 如果状态包含"已达上限"，则次数为200
            if "已达上限" in status:
                count = 200

            # 创建新的日志条目
            new_entry = {
                "shop_name": shop_name,
                "status": status,
                "count": count,
                "time": current_time,
                "date": current_date  # 添加日期字段，用于按日期筛选
            }

            # 如果找到现有记录，更新它；否则添加新记录
            if existing_entry_index >= 0:
                # 更新现有记录
                old_entry = log_data[existing_entry_index]
                log_data[existing_entry_index] = new_entry
                print(f"[邀约日志] 已更新记录: {shop_name} - {old_entry.get('status', '未知')} -> {status} - {count}")
                is_duplicate = True
            else:
                # 添加新记录
                log_data.append(new_entry)
                print(f"[邀约日志] 已添加记录: {shop_name} - {status} - {count}")
                is_duplicate = False

            # 保存日志
            with open(log_file_path, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            return is_duplicate  # 返回是否是重复记录（更新现有记录）

        except Exception as e:
            print(f"[错误] 添加邀约日志失败: {e}")
            return False  # 出错时也返回False

    @staticmethod
    def clear_previous_day_logs():
        """清除前一天的日志记录"""
        try:
            # 使用配置文件路径函数获取正确的日志文件路径
            log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))
            print(f"[邀约日志] 尝试读取日志文件: {log_file_path}")

            # 如果日志文件不存在，直接返回
            if not os.path.exists(log_file_path):
                print("[邀约日志] 日志文件不存在，无需清除")
                return

            # 读取现有日志
            try:
                with open(log_file_path, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
            except:
                print("[邀约日志] 读取日志文件失败，创建新的空日志")
                log_data = []
                with open(log_file_path, 'w', encoding='utf-8') as f:
                    json.dump(log_data, f, ensure_ascii=False, indent=2)
                return

            # 获取当前日期
            current_date = time.strftime("%Y-%m-%d")

            # 筛选出当天的日志
            today_logs = []
            for entry in log_data:
                # 如果日志条目没有日期字段，或者日期与当前日期相同，则保留
                if "date" not in entry or entry["date"] == current_date:
                    today_logs.append(entry)

            # 如果有日志被清除，打印信息
            if len(today_logs) < len(log_data):
                print(f"[邀约日志] 已清除 {len(log_data) - len(today_logs)} 条前一天的日志记录")

            # 保存当天的日志
            with open(log_file_path, 'w', encoding='utf-8') as f:
                json.dump(today_logs, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"[错误] 清除前一天日志失败: {e}")


# --- 新增: 递归查找函数 --- #
def find_first_valid_value(data, target_key):
    """递归遍历字典或列表，查找第一个非空/非空白的目标键值。"""
    if isinstance(data, dict):
        # 1. 先检查当前字典层级是否有目标键且值有效
        value = data.get(target_key)
        if value is not None:
            str_value = str(value).strip()
            if str_value:
                # print(f"[Recursive Search] Found valid '{target_key}': '{str_value}' directly in dict.")
                return str_value

        # 2. 如果当前层级没有，递归搜索子字典或子列表
        for key, val in data.items():
            if isinstance(val, (dict, list)):
                found_value = find_first_valid_value(val, target_key)
                if found_value is not None: # 注意：递归函数返回 None 表示没找到
                    # print(f"[Recursive Search] Found valid '{target_key}' in nested structure under key '{key}'.")
                    return found_value

    elif isinstance(data, list):
        # 遍历列表中的每个元素
        for item in data:
            if isinstance(item, (dict, list)):
                found_value = find_first_valid_value(item, target_key)
                if found_value is not None:
                    # print(f"[Recursive Search] Found valid '{target_key}' in nested list item.")
                    return found_value

    # 如果遍历完所有路径都没找到有效值
    return None
# --- 结束新增 --- #

class InfluencerInvitationUI(QWidget):
    # --- 修改: 信号传递页码 ---
    products_loaded = pyqtSignal(list, int) # list: products_data, int: page_number
    # --- 结束修改 ---
    scraping_error = pyqtSignal(str)
    # --- 修改: 并发获取完成信号 (传递最终结果字典) ---
    batch_fetch_complete = pyqtSignal(dict)
    # --- 结束修改 ---
    # --- 新增: 达人加载完成信号 ---
    # influencers_loaded = pyqtSignal(list, int, int, bool) # list: influencer_data, int: page_num, int: total_count, bool: success -> 保留用于单页，但主要用批量
    batch_influencers_loaded = pyqtSignal(list, int, int, bool) # list: combined_list, int: last_page_num, int: total_count, bool: overall_success
    # --- 结束新增 ---
    # +++ 新增: 自动邀约信号 +++
    invite_influencer_signal = pyqtSignal(int) # 发送邀约请求 (传递行号)
    auto_invite_finished_signal = pyqtSignal(bool) # 自动邀约完成 (传递是否被中断)
    # +++ 新增: 单店邀约信号 +++
    single_shop_invite_finished_signal = pyqtSignal(bool) # 单店邀约完成 (传递是否被中断)
    # +++ 新增: 请求下一页信号 +++
    request_next_page_signal = pyqtSignal()
    # +++ 新增: 请求店铺切换信号 +++
    request_shop_switch_signal = pyqtSignal(str) # 发送要切换到的店铺名称
    # +++ 结束新增 +++
    # +++ 新增: 邀约结果信号 +++
    invitation_result_signal = pyqtSignal(int, bool, str, int) # row, success, message, error_code
    # +++ 结束新增 +++
    # +++ 新增: 加载数据库状态信号 +++
    apply_db_state_signal = pyqtSignal(dict)
    db_state_load_failed_signal = pyqtSignal()
    # +++ 结束新增 +++

    def __init__(self, account_manager, main_window_ref, parent=None):
        super(InfluencerInvitationUI, self).__init__(parent)
        self.account_manager = account_manager
        self.main_window_ref = main_window_ref
        # --- 修改: 定义配置文件路径为 config/config.json，使用配置文件路径函数 ---
        self.settings_file = get_config_file_path(os.path.join("config", "config.json")) # 使用配置文件路径函数确保路径正确
        print(f"[达人邀约] 配置文件路径: {self.settings_file}")
        # --- 结束修改 ---
        self.current_shop_name = None
        # --- 新增: 用于存储跳过的店铺信息 ---
        self.skipped_shops_info = {}
        # --- 结束新增 ---
        # --- 新增: 用于防止重复清空表格 ---
        self._is_clearing_table = False
        # --- 结束新增 ---

        # --- 新增: 用于跟踪当前店铺的邀约状态 ---
        self.current_shop_invite_status = {
            "status": "",  # 状态：开始邀约、邀约成功、已达上限、完成邀约、停止邀约等
            "count": 0,    # 邀约次数
            "recorded": False  # 是否已记录到日志
        }

        # --- 新增: 定时邀约相关变量 ---
        self.timer_invitation = QTimer()
        self.timer_invitation.timeout.connect(self._check_timer_invitation)
        self.timer_invitation.start(10000)  # 每10秒检查一次（便于测试）
        self.timer_invitation_triggered = False  # 防止重复触发
        # --- 结束新增 ---
        # --- 结束新增 ---
        self.scraper_view = None
        self.scraper_page = None
        self.scraper_profile = None
        self.current_page = 1
        self.total_pages = -1
        self.consecutive_empty_pages = 0

        # --- 新增: 用于轮询检查的变量 ---
        self.check_timer = None
        self.check_start_time = 0
        self.check_interval_ms = 250 # 检查间隔 (毫秒)
        self.check_timeout_ms = 15000 # 超时时间 (15 秒)
        self._current_check_js = "" # 存储当前检查的 JS
        self._current_success_callback = None # 存储检查成功后的回调
        # --- 结束新增 --- #

        self.results_lock = threading.Lock() # 保护计数器
        self.batch_fetch_complete.connect(self._handle_batch_fetch_complete) # 连接新信号
        self.page_results_cache = {} # 用于缓存并发结果
        self.expected_pages = 0 # 期望收到的页面数量
        self.received_pages = 0 # 实际收到的页面数量

        # --- 新增: 达人加载相关变量 ---
        self.loading_influencers = False  # 是否正在加载达人数据
        self.current_influencer_page = 0  # 当前达人数据页码，0表示未加载
        self.total_influencers = 0  # 达人总数

        # 类目ID映射
        self.category_id_map = {
            "服饰内衣": 6033,
            "鞋靴": 7378,
            "箱包皮具": 6831,
            "运动户外": 6932,
            "食品饮料": 7419,
            "生鲜": 6625,
            "酒类": 7339,
            "家庭清洁/纸品": 1453,
            "家居日用": 1421,
            "家纺": 1653,
            "厨具": 1142,
            "个人护理": 1001,
            "美妆护肤": 6870,
            "图书": 135835,
            "家装建材": 1701,
            "家用电器": 1495,
            "宠物生活": 1208,
            "农资园艺": 1069,
            "母婴": 6153,
            "数码": 1972
            # 后续可以添加其他类目
        }
        # --- 结束新增 --- #
        self.is_auto_inviting = False # 添加自动邀约状态标志
        self.auto_invitation_thread = None # 用于存储后台线程
        # +++ 新增: 用于页面加载同步的事件 +++
        self.page_load_event = threading.Event()
        # +++ 结束新增 +++
        # +++ 新增: 自动邀约准备阶段标志 +++
        self.preparing_for_auto_invite = False
        self.products_loaded_for_auto_invite = False
        self.influencers_loaded_for_auto_invite = False
        # +++ 结束新增 +++
        # +++ 新增: 店铺切换相关标志 +++
        self.switch_shop_requested = False
        self.original_shop_hit_limit = None # 记录第一个达到上限的店铺
        # +++ 结束新增 +++
        # +++ 新增: 自动邀约断点续传索引 +++
        # self.auto_invite_resume_index = 0 <-- 移除此行
        # +++ 结束新增 --- #

        # --- 新增: 等待达人加载以启动邀约标志 +++
        self.waiting_for_influencers_to_start_invite = False
        # +++ 新增: 链式加载标志 (先商品后达人) +++
        self.waiting_for_products_then_influencers = False
        # +++ 新增: 自动调整页码后的加载标志 +++
        self.auto_adjust_page_for_invitation = False
        # +++ 新增: 已显示所有店铺邀约完成提示的标志 +++
        self.all_shops_completed_notified = False
        # +++ 新增: 店铺掉线标志 +++
        # 不再使用店铺掉线标志
        # +++ 新增: 自动翻页相关标志 +++
        self.auto_inviting_before_next_page = False
        # +++ 结束新增 +++

        # +++ 新增: 记录自动邀约的当前行号 +++
        self.current_auto_invite_row = 0 # 下次邀约从第0行开始
        # +++ 新增: 记录单店邀约的当前行号 +++
        self.current_single_shop_invite_row = 0 # 单店邀约的行号，独立于自动邀约
        # +++ 结束新增 +++

        # +++ 新增: 单店邀约独立标志系统 +++
        self.single_shop_loading_products = False  # 单店邀约正在加载商品
        self.single_shop_loading_influencers = False  # 单店邀约正在加载达人
        self.single_shop_products_loaded = False  # 单店邀约商品已加载
        self.single_shop_influencers_loaded = False  # 单店邀约达人已加载
        self.single_shop_waiting_for_products_then_influencers = False  # 单店邀约等待商品后加载达人
        self.single_shop_waiting_for_influencers_to_start_invite = False  # 单店邀约等待达人后开始邀约
        # +++ 结束新增 +++

        # +++ 新增: 单店邀约相关变量 +++
        self.is_single_shop_inviting = False  # 是否正在进行单店邀约
        self.loading_products_for_single_shop_invite = False  # 是否正在为单店邀约加载商品
        self.loading_influencers_for_single_shop_invite = False  # 是否正在为单店邀约加载达人
        self.single_shop_product_loading_shop = None  # 单店邀约商品加载的店铺名称
        self.single_shop_influencer_loading_shop = None  # 单店邀约达人加载的店铺名称
        # +++ 结束新增 +++

        # --- 新增: 连接自动邀约信号 ---
        self.invite_influencer_signal.connect(self._handle_invite_request)
        self.auto_invite_finished_signal.connect(self._handle_auto_invite_finished)
        # +++ 新增: 连接单店邀约信号 +++
        self.single_shop_invite_finished_signal.connect(self._handle_single_shop_invite_finished)
        # +++ 新增: 连接请求下一页信号 +++
        self.request_next_page_signal.connect(self._handle_request_next_page)
        # +++ 新增: 连接店铺切换信号 +++
        # 不再直接连接到主窗口的方法，而是先经过我们的辅助方法
        self.request_shop_switch_signal.connect(self._clear_table_and_switch_shop)
        # +++ 结束新增 +++
        # +++ 新增: 连接邀约结果信号 +++
        self.invitation_result_signal.connect(self._handle_invitation_result)
        # +++ 结束新增 +++
        # +++ 新增: 连接数据库状态加载信号 +++
        self.apply_db_state_signal.connect(self._on_apply_db_state)
        self.db_state_load_failed_signal.connect(self._on_db_state_load_failed)
        # +++ 结束新增 +++

        # --- 新增: 加载状态标志 ---
        self._loading_settings = False
        # --- 结束新增 ---

        # --- 修改: 先 initUI 再加载设置 ---
        self.initUI()
        self._ensure_config_dir_exists() # 确保目录存在
        self._load_settings() # 在UI初始化完成后加载设置
        # --- 结束修改 ---
        self.products_loaded.connect(self.populate_product_table)
        self.scraping_error.connect(self.show_scraping_error)
        # --- 新增: 连接达人加载信号 ---
        # self.influencers_loaded.connect(self._handle_influencer_load_result) # 暂时注释掉旧的单页处理槽
        self.batch_influencers_loaded.connect(self._handle_batch_influencer_load_result) # 连接新的批量处理槽
        # --- 结束新增 ---

        # --- 新增: 连接筛选控件信号的辅助函数 ---
        self._connect_filter_signals()
        # --- 结束新增 ---

    # --- 新增: 确保 config 目录存在 ---
    def _ensure_config_dir_exists(self):
        """确保配置文件所在的目录存在"""
        try:
            config_dir = os.path.dirname(self.settings_file)
            if config_dir and not os.path.exists(config_dir): # 检查目录是否非空且不存在
                os.makedirs(config_dir)
                print(f"已创建配置目录: {config_dir}")
        except Exception as e:
            print(f"创建配置目录时出错: {e}")
    # --- 结束新增 ---

    # --- 新增: 连接筛选控件信号的辅助函数 ---
    def _connect_filter_signals(self):
        """连接所有筛选控件的信号到 _save_settings"""
        print("[DEBUG Inv] Connecting filter signals...")
        # 1. 类目复选框
        if hasattr(self, 'category_checkboxes'):
            for checkbox in self.category_checkboxes:
                checkbox.stateChanged.connect(self._save_settings)

        # 2. 单选按钮组 (连接每个按钮的 toggled 信号)
        radio_buttons_to_connect = [
            # Fans
            self.fans_unlimited, self.fans_less_1w, self.fans_1w_10w,
            self.fans_10w_50w, self.fans_more_50w,
            # Sales
            self.sales_unlimited, self.sales_less_10w, self.sales_10w_50w,
            self.sales_50w_100w, self.sales_more_100w,
            # Price
            self.price_unlimited, self.price_less_100, self.price_100_200, self.price_more_200,
            # Avg Sales
            self.avg_sales_unlimited, self.avg_sales_less_1w, self.avg_sales_1w_10w, self.avg_sales_more_10w,
            # Age
            self.age_unlimited, self.age_less_18, self.age_18_24,
            self.age_25_39, self.age_40_49, self.age_more_50,
        ]
        for radio_button in radio_buttons_to_connect:
            if radio_button: # 确保控件存在
                 # 只在按钮被选中时触发保存，避免取消选中时也触发
                 radio_button.toggled.connect(lambda checked, rb=radio_button: self._save_settings() if checked else None)


        # 3. 下拉框
        self.gender_combo.currentIndexChanged.connect(self._save_settings)

        # 4. 其他复选框
        self.has_recommendation.stateChanged.connect(self._save_settings)
        self.has_certification.stateChanged.connect(self._save_settings)

        print("[DEBUG Inv] Filter signals connected.")
    # --- 结束新增 ---

    def initUI(self):
        # 主布局
        main_layout = QVBoxLayout()

        # --- 修改: 将顶部筛选条件放入 GroupBox --- #
        # 创建 GroupBox
        filter_groupbox = QGroupBox("达人属性")
        # 为 GroupBox 创建内部布局
        filter_grid_layout = QGridLayout()

        # 第一行筛选条件 (添加到 filter_grid_layout)
        # category_label 不再需要
        # top_filter_layout.addWidget(category_label, 0, 0)

        # 粉丝数筛选条件
        fans_label = QLabel("粉丝数:")
        self.fans_unlimited = QRadioButton("不限")
        self.fans_unlimited.setChecked(True)
        self.fans_less_1w = QRadioButton("1w以下")
        self.fans_1w_10w = QRadioButton("1w-10w")
        self.fans_10w_50w = QRadioButton("10w-50w")
        self.fans_more_50w = QRadioButton("50w以上")

        fans_group = QButtonGroup(self)
        fans_group.addButton(self.fans_unlimited)
        fans_group.addButton(self.fans_less_1w)
        fans_group.addButton(self.fans_1w_10w)
        fans_group.addButton(self.fans_10w_50w)
        fans_group.addButton(self.fans_more_50w)

        # 注意：添加到 filter_grid_layout 时，列索引从 0 开始
        filter_grid_layout.addWidget(fans_label, 0, 0)
        filter_grid_layout.addWidget(self.fans_unlimited, 0, 1)
        filter_grid_layout.addWidget(self.fans_less_1w, 0, 2)
        filter_grid_layout.addWidget(self.fans_1w_10w, 0, 3)
        filter_grid_layout.addWidget(self.fans_10w_50w, 0, 4)
        filter_grid_layout.addWidget(self.fans_more_50w, 0, 5)

        # 销售额筛选条件
        sales_label = QLabel("销售额:")
        self.sales_unlimited = QRadioButton("不限")
        self.sales_unlimited.setChecked(True)
        self.sales_less_10w = QRadioButton("10w以下")
        self.sales_10w_50w = QRadioButton("10w-50w")
        self.sales_50w_100w = QRadioButton("50w-100w")
        self.sales_more_100w = QRadioButton("100w以上") # 新增 100w以上 选项

        sales_group = QButtonGroup(self)
        sales_group.addButton(self.sales_unlimited)
        sales_group.addButton(self.sales_less_10w)
        sales_group.addButton(self.sales_10w_50w)
        sales_group.addButton(self.sales_50w_100w)
        sales_group.addButton(self.sales_more_100w) # 将新选项添加到按钮组

        # 注意：添加到 filter_grid_layout
        filter_grid_layout.addWidget(sales_label, 0, 6)
        filter_grid_layout.addWidget(self.sales_unlimited, 0, 7)
        filter_grid_layout.addWidget(self.sales_less_10w, 0, 8)
        filter_grid_layout.addWidget(self.sales_10w_50w, 0, 9)
        filter_grid_layout.addWidget(self.sales_50w_100w, 0, 10)
        filter_grid_layout.addWidget(self.sales_more_100w, 0, 11) # 将新选项添加到布局，并调整后续控件位置

        # 有推荐方式选项
        self.has_recommendation = QCheckBox("有推荐方式")
        filter_grid_layout.addWidget(self.has_recommendation, 0, 12) # 添加到 filter_grid_layout

        # 第二行筛选条件 (添加到 filter_grid_layout)
        # 客单价筛选条件
        price_label = QLabel("客单价:")
        self.price_unlimited = QRadioButton("不限")
        self.price_unlimited.setChecked(True)
        self.price_less_100 = QRadioButton("100元下")
        self.price_100_200 = QRadioButton("100-200元")
        self.price_more_200 = QRadioButton("200元以上")

        price_group = QButtonGroup(self)
        price_group.addButton(self.price_unlimited)
        price_group.addButton(self.price_less_100)
        price_group.addButton(self.price_100_200)
        price_group.addButton(self.price_more_200)

        filter_grid_layout.addWidget(price_label, 1, 0) # 行号改为 1，列号从 0 开始
        filter_grid_layout.addWidget(self.price_unlimited, 1, 1)
        filter_grid_layout.addWidget(self.price_less_100, 1, 2)
        filter_grid_layout.addWidget(self.price_100_200, 1, 3)
        filter_grid_layout.addWidget(self.price_more_200, 1, 4)

        # 场均销售额
        avg_sales_label = QLabel("场均销售额:")
        self.avg_sales_unlimited = QRadioButton("不限")
        self.avg_sales_unlimited.setChecked(True)
        self.avg_sales_less_1w = QRadioButton("1w以下")
        self.avg_sales_1w_10w = QRadioButton("1w-10w")
        self.avg_sales_more_10w = QRadioButton("10w以上")

        avg_sales_group = QButtonGroup(self)
        avg_sales_group.addButton(self.avg_sales_unlimited)
        avg_sales_group.addButton(self.avg_sales_less_1w)
        avg_sales_group.addButton(self.avg_sales_1w_10w)
        avg_sales_group.addButton(self.avg_sales_more_10w)

        filter_grid_layout.addWidget(avg_sales_label, 1, 6) # 行号改为 1
        filter_grid_layout.addWidget(self.avg_sales_unlimited, 1, 7)
        filter_grid_layout.addWidget(self.avg_sales_less_1w, 1, 8)
        filter_grid_layout.addWidget(self.avg_sales_1w_10w, 1, 9)
        filter_grid_layout.addWidget(self.avg_sales_more_10w, 1, 10)

        # 有视频号认证
        self.has_certification = QCheckBox("有视频号认证")
        filter_grid_layout.addWidget(self.has_certification, 1, 11) # 行号改为 1

        # 第三行筛选条件 (添加到 filter_grid_layout)
        # 粉丝年龄
        age_label = QLabel("粉丝年龄:")
        self.age_unlimited = QRadioButton("不限")
        self.age_unlimited.setChecked(True)
        self.age_less_18 = QRadioButton("18岁以下")
        self.age_18_24 = QRadioButton("18-24岁")
        self.age_25_39 = QRadioButton("25-39岁")
        self.age_40_49 = QRadioButton("40-49岁")
        self.age_more_50 = QRadioButton("50岁以上")

        age_group = QButtonGroup(self)
        age_group.addButton(self.age_unlimited)
        age_group.addButton(self.age_less_18)
        age_group.addButton(self.age_18_24)
        age_group.addButton(self.age_25_39)
        age_group.addButton(self.age_40_49)
        age_group.addButton(self.age_more_50)

        filter_grid_layout.addWidget(age_label, 2, 0) # 行号改为 2
        filter_grid_layout.addWidget(self.age_unlimited, 2, 1)
        filter_grid_layout.addWidget(self.age_less_18, 2, 2)
        filter_grid_layout.addWidget(self.age_18_24, 2, 3)
        filter_grid_layout.addWidget(self.age_25_39, 2, 4)
        filter_grid_layout.addWidget(self.age_40_49, 2, 5)
        filter_grid_layout.addWidget(self.age_more_50, 2, 6)

        # 达人性别
        gender_label = QLabel("达人性别:")
        self.gender_combo = QComboBox()
        self.gender_combo.addItem("不限")
        self.gender_combo.addItem("男")
        self.gender_combo.addItem("女")



        filter_grid_layout.addWidget(gender_label, 2, 7) # 行号改为 2
        filter_grid_layout.addWidget(self.gender_combo, 2, 8)

        # 店铺选择 - 使用水平布局让下拉框紧贴标签
        shop_hbox = QHBoxLayout()
        shop_hbox.setContentsMargins(0, 0, 0, 0)  # 移除布局边距
        shop_hbox.setSpacing(2)  # 设置组件之间的间距为2像素

        shop_label = QLabel("店铺选择:")
        self.shop_combo = QComboBox()
        self.shop_combo.addItem("当前店铺")  # 默认选项
        
        # 设置为可搜索的下拉框
        self.shop_combo.setEditable(True)  # 使下拉框可编辑以支持搜索
        self.shop_combo.setInsertPolicy(QComboBox.NoInsert)  # 禁止插入新项目，只允许搜索现有项目
        self.shop_combo.setMaxVisibleItems(15)  # 设置下拉列表最多显示15条
        
        # 设置自动补全器
        from PyQt5.QtWidgets import QCompleter
        shop_completer = QCompleter()
        shop_completer.setCompletionMode(QCompleter.PopupCompletion)  # 弹出式补全
        shop_completer.setCaseSensitivity(Qt.CaseInsensitive)  # 忽略大小写
        shop_completer.setFilterMode(Qt.MatchContains)  # 包含匹配模式，支持中间匹配
        self.shop_combo.setCompleter(shop_completer)

        # 设置店铺选择下拉框的样式，简洁小巧
        self.shop_combo.setObjectName("influencer_invitation_shop_combo")
        self.shop_combo.setStyleSheet("""
            QComboBox#influencer_invitation_shop_combo {
                background-color: white !important;
                border: 1px solid #d0d0d0 !important;
                border-radius: 2px !important;
                padding: 2px 6px !important;
                min-width: 90px !important;
                max-height: 24px !important;
                font-family: 'Microsoft YaHei' !important;
                font-size: 12px !important;
                color: #333333 !important;
            }
            QComboBox#influencer_invitation_shop_combo:editable {
                background-color: white !important;
                selection-background-color: #4A6BDF !important;
                selection-color: white !important;
            }
            QComboBox#influencer_invitation_shop_combo::drop-down {
                width: 16px !important;
                border: none !important;
                background-color: #f0f0f0 !important;
            }
            QComboBox#influencer_invitation_shop_combo::down-arrow {
                image: none !important;
                border: 1px solid #d0d0d0 !important;
                width: 8px !important;
                height: 8px !important;
                background-color: #e0e0e0 !important;
            }
            QComboBox#influencer_invitation_shop_combo QAbstractItemView {
                background-color: white !important;
                selection-background-color: #4A6BDF !important;
                selection-color: white !important;
                border: 1px solid #d0d0d0 !important;
                outline: none !important;
                font-family: 'Microsoft YaHei' !important;
                font-size: 12px !important;
                color: #333333 !important;
            }
            QComboBox#influencer_invitation_shop_combo QAbstractItemView::item {
                height: 24px !important;
                padding: 2px 6px !important;
                border: none !important;
                color: #333333 !important;
                background-color: white !important;
            }
            QComboBox#influencer_invitation_shop_combo QAbstractItemView::item:selected {
                background-color: #4A6BDF !important;
                color: white !important;
                font-weight: bold !important;
            }
            QComboBox#influencer_invitation_shop_combo QAbstractItemView::item:hover {
                background-color: #e8f0ff !important;
                color: #333333 !important;
            }
            QComboBox#influencer_invitation_shop_combo QLineEdit {
                background-color: white !important;
                color: #333333 !important;
                selection-background-color: #4A6BDF !important;
                selection-color: white !important;
                border: none !important;
            }
        """)

        # 连接达人邀约页面内部的店铺选择信号，独立于主窗口
        self.shop_combo.currentIndexChanged.connect(self._on_internal_shop_selection_changed)
        print("[达人邀约] 店铺选择下拉框已创建，连接内部信号，保持独立")

        # 添加到水平布局
        shop_hbox.addWidget(shop_label)
        shop_hbox.addWidget(self.shop_combo)

        # 将水平布局添加到网格布局
        shop_widget = QWidget()
        shop_widget.setLayout(shop_hbox)
        filter_grid_layout.addWidget(shop_widget, 2, 9, 1, 2)  # 跨越两列

        # 创建一个水平布局，包含保存按钮和店铺分类
        save_category_hbox = QHBoxLayout()
        save_category_hbox.setContentsMargins(0, 0, 0, 0)  # 移除布局边距
        save_category_hbox.setSpacing(2)  # 设置组件之间的间距为2像素

        # 添加保存按钮
        self.save_button = QPushButton("保存")
        self.save_button.setFixedSize(60, 28)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #4A6BDF;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #3B5CA0;
            }
            QPushButton:pressed {
                background-color: #2A4A8F;
            }
        """)
        # 连接保存按钮的点击事件
        self.save_button.clicked.connect(self._on_save_button_clicked)

        # 添加店铺分类标签和下拉框
        category_label = QLabel("店铺分类:")
        self.shop_category_combo = QComboBox()
        self.shop_category_combo.addItem("全部分类")  # 默认选项

        # 设置店铺分类下拉框的样式，简洁小巧
        self.shop_category_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: none;
                padding: 2px 6px;
                min-width: 80px;
                max-height: 24px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
            }
            QComboBox::drop-down {
                width: 16px;
                border: none;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                border: 1px solid #d0d0d0;
                selection-background-color: #4A6BDF;
                selection-color: white;
                outline: none;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QComboBox QAbstractItemView::item {
                height: 24px;
                padding: 2px 6px;
                border: none;
                color: #333333;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #4A6BDF;
                color: white;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e8f0ff;
                color: #333333;
            }
        """)

        # 连接分类下拉框的选择变化事件
        self.shop_category_combo.currentIndexChanged.connect(self._on_category_selection_changed)

        # 添加所有组件到水平布局
        save_category_hbox.addWidget(self.save_button)
        save_category_hbox.addWidget(category_label)
        save_category_hbox.addWidget(self.shop_category_combo)
        save_category_hbox.addStretch(1)  # 添加弹性空间，将组件推向左侧

        # 将整个水平布局添加到网格布局
        save_category_widget = QWidget()
        save_category_widget.setLayout(save_category_hbox)
        filter_grid_layout.addWidget(save_category_widget, 2, 11, 1, 3)  # 从第11列开始，跨越3列

        # 初始加载店铺列表
        self.reload_shop_list()

        # 延迟1秒后再次加载店铺列表，确保账号数据已加载
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(1000, self.reload_shop_list)

        # 延迟5秒后再次加载店铺列表，确保账号数据已从服务器加载完成
        QTimer.singleShot(5000, self.reload_shop_list)


        # 设置内部布局的伸缩
        filter_grid_layout.setColumnStretch(12, 1) # 在第12列添加伸缩因子，将内容推向左侧

        # 将内部布局设置给 GroupBox
        filter_groupbox.setLayout(filter_grid_layout)
        # --- 修改: 设置固定宽度为 1035 --- #
        filter_groupbox.setFixedWidth(1035) # 替换 setMaximumWidth

        filter_groupbox.setStyleSheet("QGroupBox { margin-left: 13px; }")

        # 将 GroupBox 添加到主布局
        main_layout.addWidget(filter_groupbox)
        # --- 结束修改 --- #

        # 中间内容布局（左侧类目 + 中间表格 + 右侧信息）
        middle_layout = QHBoxLayout()

        # 左侧类目列表
        category_group = QGroupBox("达人类目") # 添加标题
        category_group.setObjectName("categoryGroup") # 设置对象名
        category_layout = QVBoxLayout()

        categories = [
            "服饰内衣", "鞋靴", "箱包皮具", "运动户外", "食品饮料",
            "生鲜", "百货", "家居家装/摆品", "家居日用", "家纺",
            "厨具", "个人护理", "美妆护肤", "母婴", "家装建材",
            "家用电器", "宠物生活", "农资园艺", "图书", "箱包"
        ]

        self.category_checkboxes = []
        for category in categories:
            checkbox = QCheckBox(category)
            category_layout.addWidget(checkbox)
            self.category_checkboxes.append(checkbox)

        category_layout.addStretch(1)
        category_group.setLayout(category_layout)

        # 设置左侧类目列表的最大宽度
        category_group.setMaximumWidth(120)
        middle_layout.addWidget(category_group)

        # 中间表格
        self.table = QTableWidget()
        # --- 新增: 设置对象名 ---
        self.table.setObjectName("influencerTable")
        # --- 结束新增 ---
        # --- 修改: 减少列数到 8 --- #
        self.table.setColumnCount(8)
        # --- 结束修改 --- #
        # --- 修改: 初始行数设置为 30，与商品表格一致，显示表格线 --- #
        self.table.setRowCount(30)  # 初始化为30行，显示表格线和交替颜色
        # --- 结束修改 --- #
        self.table.verticalHeader().setVisible(False)
        self.table.horizontalHeader().setFixedHeight(30)
        self.table.setAlternatingRowColors(True)
        # 隐藏网格线，保持简洁外观
        self.table.setShowGrid(False)

        # --- 修改: 移除表头标签 "粉丝年龄" --- #
        influencer_headers = ["序号", "达人ID", "达人昵称", "状态", "粉丝数", "销售额", "场均销售额", "客单价"]
        # --- 结束修改 --- #
        self.table.setHorizontalHeaderLabels(influencer_headers)

        # --- 再次确认/修改: 达人列表 self.table 的列设置 --- #
        # 保持原有列设置
        self.table.setColumnWidth(0, 40)  # 序号列宽度设为 40
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed) # 序号列固定
        # --- 添加打印验证 --- #
        # print(f"[DEBUG] self.table (达人列表) Column 0: Width={self.table.columnWidth(0)}, ResizeMode={self.table.horizontalHeader().sectionResizeMode(0)}")
        # --- 结束打印验证 --- #

        self.table.setColumnWidth(1, 70) # 达人ID列宽度 (加宽)
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Interactive) # 允许手动调整

        # 达人昵称列 (索引 2) - 拉伸填充
        self.table.setColumnWidth(2, 100)
        # --- 修改: 将拉伸改为交互式 --- #
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Interactive)
        # --- 结束修改 --- #

        # --- 修改: 状态列宽度改为 100px --- #
        self.table.setColumnWidth(3, 100)  # 状态列宽度
        # --- 结束修改 --- #
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Interactive) # 允许手动调整

        self.table.setColumnWidth(4, 80)  # 粉丝数列宽度
        self.table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Interactive) # 允许手动调整

        # --- 新增: 为新列设置宽度和ResizeMode --- #
        default_new_col_width = 100
        self.table.setColumnWidth(5, default_new_col_width) # 销售额
        self.table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Interactive)
        self.table.setColumnWidth(6, default_new_col_width) # 场均销售额
        self.table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Interactive)
        self.table.setColumnWidth(7, default_new_col_width) # 客单价
        self.table.horizontalHeader().setSectionResizeMode(7, QHeaderView.Interactive)
        # --- 修改: 移除粉丝年龄列 (索引 8) 的设置 --- #
        # self.table.setColumnWidth(8, default_new_col_width) # 粉丝年龄
        # self.table.horizontalHeader().setSectionResizeMode(8, QHeaderView.Interactive)
        # --- 结束修改 --- #
        # --- 结束设置 --- #

        # --- 新增: 启用右键菜单 --- #
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self._show_influencer_context_menu)
        # --- 结束新增 --- #

        middle_layout.addWidget(self.table, 2)  # 设置达人表格占据比例为2

        # --- 新增: 右侧选中商品表格 --- #
        self.selected_products_table = QTableWidget()
        self.selected_products_table.setColumnCount(6)
        self.selected_products_table.setRowCount(30) # 修改：初始化为 30 行
        self.selected_products_table.verticalHeader().setVisible(False)
        self.selected_products_table.horizontalHeader().setFixedHeight(30)
        self.selected_products_table.setAlternatingRowColors(True) # 确保已启用交替颜色
        # 隐藏网格线，保持简洁外观
        self.selected_products_table.setShowGrid(False)

        product_headers = ["序号", "商品ID", "商品标题", "价格", "佣金", "店铺"]
        self.selected_products_table.setHorizontalHeaderLabels(product_headers)

        # --- 修改: 调整列宽和 ResizeMode 设置顺序 --- #
        # 1. 先设置固定/交互列
        self.selected_products_table.setColumnWidth(0, 66)  # 序号列宽度 (修改为 66)
        # --- 修改: 恢复为 Fixed --- #
        self.selected_products_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed) # 恢复为 Fixed
        # --- 结束修改 --- #
        # --- 删除: 移除打印验证 --- #
        # print(f"[DEBUG] selected_products_table Column 0: Width={self.selected_products_table.columnWidth(0)}, ResizeMode={self.selected_products_table.horizontalHeader().sectionResizeMode(0)}")
        # --- 结束删除 --- #

        self.selected_products_table.setColumnWidth(1, 80)  # 商品ID列宽度
        self.selected_products_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Interactive)

        self.selected_products_table.setColumnWidth(3, 60)  # 价格列宽度
        self.selected_products_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Interactive)

        self.selected_products_table.setColumnWidth(4, 60)  # 佣金列宽度
        self.selected_products_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Interactive)

        # 2. 再设置拉伸列
        self.selected_products_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch) # 商品标题列拉伸
        self.selected_products_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch) # 店铺列拉伸
        # --- 结束修改 --- #

        middle_layout.addWidget(self.selected_products_table, 2) # 设置选中商品表格占据比例为2
        # --- 结束新增 ---

        # --- 修改: 将右侧面板改为 QGroupBox --- #

        right_groupbox = QGroupBox("邀约设置") # 创建 QGroupBox
        groupbox_layout = QVBoxLayout() # GroupBox 的内部布局

        # 联系信息表单
        contact_form = QFormLayout()

        # 联系人
        contact_label = QLabel("联系人:")
        self.contact_input = QLineEdit()
        self.contact_input.setText("管雅")
        contact_form.addRow(contact_label, self.contact_input)

        # 手机号
        phone_label = QLabel("手机号:")
        self.phone_input = QLineEdit()
        self.phone_input.setText("17710592979")
        contact_form.addRow(phone_label, self.phone_input)

        # 微信号
        wechat_label = QLabel("微信号:")
        self.wechat_input = QLineEdit()
        self.wechat_input.setText("long28998")
        contact_form.addRow(wechat_label, self.wechat_input)

        # 添加联系表单到 GroupBox 布局
        contact_widget = QWidget()
        contact_widget.setLayout(contact_form)
        groupbox_layout.addWidget(contact_widget) # 添加到 groupbox_layout

        # 带货描述
        desc_label = QLabel("带货描述:")
        groupbox_layout.addWidget(desc_label) # 添加到 groupbox_layout

        self.desc_text = QTextEdit()
        self.desc_text.setText("【爱慕上新女装源头工厂】\n优选面料，细腻工艺，紧跟潮流\n国际时尚设计师一线剪裁\n多次被服装市场授权订做，价格方面可议，合作达人价格方面可议，合作达人价格方面可议，合作达人优惠价格相当给力！快实力带起来吧！")
        groupbox_layout.addWidget(self.desc_text) # 添加到 groupbox_layout

        # 间隔和佣金设置
        interval_layout = QHBoxLayout()
        interval_label = QLabel("间隔:")
        self.interval_min = QSpinBox()
        self.interval_min.setMinimum(0)
        self.interval_min.setMaximum(60)
        self.interval_min.setValue(1)
        interval_dash = QLabel("-")
        self.interval_max = QSpinBox()
        self.interval_max.setMinimum(0)
        self.interval_max.setMaximum(60)
        self.interval_max.setValue(0)
        interval_unit = QLabel("秒")

        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(self.interval_min)
        interval_layout.addWidget(interval_dash)
        interval_layout.addWidget(self.interval_max)
        interval_layout.addWidget(interval_unit)
        interval_layout.addStretch(1)

        # 佣金设置
        commission_layout = QHBoxLayout()
        commission_label = QLabel("佣金≥")
        self.commission_input = QLineEdit()
        self.commission_input.setMaximumWidth(40)
        self.commission_input.setText("10")
        commission_percent = QLabel("%")

        commission_layout.addWidget(commission_label)
        commission_layout.addWidget(self.commission_input)
        commission_layout.addWidget(commission_percent)
        commission_layout.addStretch(1)

        # 定时邀约设置
        timer_layout = QHBoxLayout()
        self.timer_checkbox = QCheckBox("定时邀约")
        self.timer_hour_input = QLineEdit()
        self.timer_hour_input.setMaximumWidth(30)
        self.timer_hour_input.setText("9")
        self.timer_hour_input.setValidator(QIntValidator(0, 23))  # 限制输入0-23小时
        timer_hour_label = QLabel("时")

        self.timer_minute_input = QLineEdit()
        self.timer_minute_input.setMaximumWidth(30)
        self.timer_minute_input.setText("0")
        self.timer_minute_input.setValidator(QIntValidator(0, 59))  # 限制输入0-59分钟
        timer_minute_label = QLabel("分")

        timer_layout.addWidget(self.timer_checkbox)
        timer_layout.addWidget(self.timer_hour_input)
        timer_layout.addWidget(timer_hour_label)
        timer_layout.addWidget(self.timer_minute_input)
        timer_layout.addWidget(timer_minute_label)
        timer_layout.addStretch(1)

        # 合并间隔、佣金和定时邀约设置
        settings_widget = QWidget()
        settings_layout = QVBoxLayout()
        settings_layout.addLayout(interval_layout)
        settings_layout.addLayout(commission_layout)
        settings_layout.addLayout(timer_layout)
        settings_widget.setLayout(settings_layout)

        groupbox_layout.addWidget(settings_widget) # 添加到 groupbox_layout
        groupbox_layout.addStretch(1)

        # 设置 GroupBox 的布局和最大宽度
        right_groupbox.setLayout(groupbox_layout)
        right_groupbox.setMaximumWidth(250)
        middle_layout.addWidget(right_groupbox) # 将 GroupBox 添加到中间布局
        # --- 结束修改 ---

        # 添加中间内容布局到主布局
        middle_widget = QWidget()
        middle_widget.setLayout(middle_layout)
        main_layout.addWidget(middle_widget)

        # 底部操作按钮布局
        bottom_layout = QHBoxLayout()
        bottom_layout.setSpacing(10) # 修改：设置控件间距为 10

        # 分页控制
        self.add_to_list_btn = QPushButton("加载达人")  # 修改按钮文本
        self.page_label = QLabel("页数:")
        self.page_input = QLineEdit()
        self.page_input.setMaximumWidth(30)
        self.page_input.setText("0")  # 初始显示0
        self.page_input.setReadOnly(False)  # 设置为可编辑
        # 添加数字验证器，确保只能输入数字
        self.page_input.setValidator(QIntValidator(1, 999))  # 允许1到999的页码
        self.prev_page_btn = QPushButton("下一页")
        self.prev_page_btn.setEnabled(False)  # 初始禁用
        self.start_page_label = QLabel("开始页:")
        self.start_page_input = QLineEdit()
        self.start_page_input.setMaximumWidth(30)
        self.start_page_input.setText("21")
        self.start_invite_btn = QPushButton("开始邀约")
        self.stop_invite_btn = QPushButton("停止邀约")
        self.auto_invite_btn = QPushButton("单店邀约")
        self.product_list_btn = QPushButton("商品列表")
        self.export_btn = QPushButton("导出")

        self.auto_invite_checkbox = QCheckBox("自动选商品")
        # self.auto_invite_checkbox.setChecked(True) # 由 _load_settings 控制初始状态
        self.invite_count_label = QLabel("邀约商品数:")
        self.invite_count_input = QLineEdit()
        self.invite_count_input.setMaximumWidth(30)
        # self.invite_count_input.setText("30") # 由 _load_settings 控制初始状态
        # --- 新增: 添加输入验证器，确保只能输入数字 ---
        self.invite_count_input.setValidator(QIntValidator(1, 999)) # 允许 1 到 999
        # --- 结束新增 ---
        self.count_unit = QLabel("个")

        # 添加邀约日志按钮
        self.invite_log_btn = QPushButton("日志")
        self.invite_log_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e5e5e5;
            }
        """)

        bottom_layout.addWidget(self.add_to_list_btn)
        bottom_layout.addWidget(self.page_label)
        bottom_layout.addWidget(self.page_input)
        bottom_layout.addWidget(self.prev_page_btn)
        bottom_layout.addWidget(self.start_page_label)
        bottom_layout.addWidget(self.start_page_input)
        bottom_layout.addWidget(self.start_invite_btn)
        bottom_layout.addWidget(self.stop_invite_btn)
        bottom_layout.addWidget(self.auto_invite_btn)
        bottom_layout.addWidget(self.product_list_btn)
        bottom_layout.addWidget(self.export_btn)
        bottom_layout.addWidget(self.auto_invite_checkbox)
        bottom_layout.addWidget(self.invite_count_label)
        bottom_layout.addWidget(self.invite_count_input)
        bottom_layout.addWidget(self.count_unit)
        bottom_layout.addWidget(self.invite_log_btn)

        bottom_layout.addStretch(1) # 添加弹性空间，将所有控件推到左侧
        bottom_layout.setAlignment(Qt.AlignTop) # 设置布局内所有控件顶部对齐

        # --- 修改: 移除样式中的 margin-left --- #
        bottom_panel_frame = QFrame() # 创建一个新的 QFrame 作为容器
        bottom_panel_frame.setFixedHeight(120) # 设置固定高度
        bottom_panel_frame.setStyleSheet("QFrame { background-color: #EEF2F7; }") # 只设置背景色
        bottom_panel_frame.setLayout(bottom_layout) # 将原来的布局设置给这个 Frame

        # --- 恢复: 使用 QHBoxLayout 实现对齐 --- #
        bottom_align_layout = QHBoxLayout()
        bottom_align_layout.setContentsMargins(0, 0, 0, 0) # 移除外部布局可能产生的边距
        # --- 新增: 添加左侧固定宽度的 Spacer --- #
        bottom_align_layout.addSpacerItem(QSpacerItem(125, 0, QSizePolicy.Fixed, QSizePolicy.Minimum))
        # --- 结束新增 --- #
        bottom_align_layout.addWidget(bottom_panel_frame) # 添加 Frame
        bottom_align_layout.addStretch(1) # 添加弹性空间，将 Frame 推到左侧

        bottom_align_widget = QWidget() # 创建容器 Widget
        bottom_align_widget.setLayout(bottom_align_layout) # 设置布局
        # --- 结束恢复 --- #

        # 修改：将包含对齐布局的 Widget 添加到主布局
        main_layout.addWidget(bottom_align_widget) # 将新的包含对齐布局的 Widget 添加到主布局

        # 设置主布局
        self.setLayout(main_layout)

        # 设置样式
        self.setStyleSheet("""
            QGroupBox {
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-top: 5px; /* 为标题留出空间 */
                padding-top: 10px; /* 为标题下方的控件增加一点内边距 */
            }
            QGroupBox::title {
                subcontrol-origin: margin; /* 相对于外边距定位 */
                subcontrol-position: top left; /* 定位在左上角 */
                padding: 0 5px; /* 左右留白 5px */
                left: 25px; /* 距离左边框 10px */
                /* 尝试将标题向上移动更多，使其跨在边框上 */
                top: 3px;
                /* 可以给标题加个背景色，便于调试和美观 */
                /* background-color: #f0f0f0; */
            }


            /* --- 结束新增 --- */
            /* --- 从商品管理复制的表格样式 --- */
            QTableWidget {
                border: 1px solid #dde4ed; /* 边框颜色 */
                background-color: white; /* 背景色 */
                gridline-color: #ecf0f5; /* 网格线颜色 */
                border-radius: 4px; /* 圆角 */
                font-family: 'Microsoft YaHei'; /* 字体 */
                font-size: 12px; /* 字体大小 */
                padding: 0px; /* 表格内边距 */
            }
            QTableWidget::item {
                padding: 8px 5px;  /* 单元格内边距: 上下8px, 左右5px */
                border-bottom: 1px solid #ecf0f5; /* 单元格底部边框 */
                color: #333; /* 文本颜色 */
            }
            QTableWidget::item:selected {
                background-color: #eef3fc; /* 选中背景色 */
                color: #3a5ca0; /* 选中文本颜色 */
            }
            QTableWidget::item:hover {
                background-color: #f5f8fe; /* 悬停背景色 */
            }
            /* 交替行颜色 */
            QTableWidget::item:alternate {
                background-color: #f9fafc;
            }
            QHeaderView {
                background-color: #f5f8fe; /* 表头背景色 */
                font-weight: bold; /* 字体加粗 */
            }
            QHeaderView::section {
                background-color: #f5f8fe; /* 表头背景色 */
                padding: 4px 5px;  /* 表头内边距: 上下4px, 左右5px (调整了上下内边距) */
                border: none; /* 无边框 */
                border-bottom: 2px solid #dde4ed; /* 表头底部边框 */
                border-right: 1px solid #ecf0f5; /* 表头右侧分隔线 */
                font-weight: bold; /* 字体加粗 */
                color: #3a5ca0; /* 表头文本颜色 */
                font-family: 'Microsoft YaHei'; /* 字体 */
                font-size: 10pt; /* 使用之前设置的字体大小 */
                min-width: 20px; /* 最小宽度 */
                height: 25px; /* 尝试调整高度 (略微增加) */
            }
            QHeaderView::section:first {
                border-top-left-radius: 4px; /* 左上角圆角 */
            }
            QHeaderView::section:last {
                border-right: none; /* 最后一列无右边框 */
                border-top-right-radius: 4px; /* 右上角圆角 */
            }
            QScrollBar:vertical {
                border: none;
                background: #f0f2f7; /* 滚动条背景 */
                width: 8px; /* 滚动条宽度 */
                border-radius: 4px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #c1c9d8; /* 滚动条滑块颜色 */
                min-height: 20px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a0a9b8; /* 滑块悬停颜色 */
            }
            QScrollBar:horizontal {
                border: none;
                background: #f0f2f7; /* 滚动条背景 */
                height: 8px; /* 滚动条高度 */
                border-radius: 4px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background: #c1c9d8; /* 滚动条滑块颜色 */
                min-width: 20px;
                border-radius: 4px;
            }
            QScrollBar::handle:horizontal:hover {
                background: #a0a9b8; /* 滑块悬停颜色 */
            }
            /* --- 结束复制的表格样式 --- */
            /* --- 新增: 隐藏达人表格网格线 --- */
            #influencerTable {
                gridline-color: transparent;
            }
            /* --- 结束新增 --- */
            QLineEdit, QTextEdit, QSpinBox {
                background-color: white;
                border: 1px solid #ccc; /* 保持边框一致性 */
            }
        """)

        # 调整整体大小
        self.resize(1000, 600)

        # --- 修改: 连接商品列表按钮 ---
        self.product_list_btn.clicked.connect(self.on_load_products_clicked)
        # --- 结束修改 ---

        # --- 新增: 连接达人列表和下一页按钮 ---
        self.add_to_list_btn.clicked.connect(self.on_load_influencers_clicked)
        self.prev_page_btn.clicked.connect(self.on_next_influencer_page_clicked)
        # 连接页码输入框的回车事件
        self.page_input.returnPressed.connect(self.on_page_input_enter)
        # --- 结束新增 ---

        # --- 新增: 连接开始/停止邀约按钮 ---
        self.start_invite_btn.clicked.connect(self._start_auto_invitation)
        self.stop_invite_btn.clicked.connect(self._handle_stop_invitation_clicked)
        self.stop_invite_btn.setEnabled(False) # 初始禁用停止按钮
        # --- 结束新增 ---

        # --- 新增: 连接单店邀约按钮 ---
        self.auto_invite_btn.clicked.connect(self.on_single_shop_invite_clicked)
        # --- 结束新增 ---

        # +++ 新增: 连接间隔设置微调框的信号 +++
        self.interval_min.valueChanged.connect(self._save_settings)
        self.interval_max.valueChanged.connect(self._save_settings)
        # +++ 结束新增 +++

        # --- 新增: 连接自动选商品和邀约数量控件的信号到 _save_settings ---
        self.auto_invite_checkbox.stateChanged.connect(self._save_settings)
        self.invite_count_input.textChanged.connect(self._save_settings)
        # --- 结束新增 ---

        # --- 新增: 连接定时邀约设置的信号到 _save_settings ---
        self.timer_checkbox.stateChanged.connect(self._save_settings)
        self.timer_hour_input.textChanged.connect(self._save_settings)
        self.timer_minute_input.textChanged.connect(self._save_settings)
        # --- 结束新增 ---

        # 连接邀约日志按钮的点击事件
        self.invite_log_btn.clicked.connect(self.on_invite_log_clicked)

    def on_invite_log_clicked(self):
        """处理邀约日志按钮点击事件"""
        # 创建并显示邀约日志对话框（非模态）
        log_dialog = InviteLogDialog(self)
        # 使用show()而不是exec_()，使对话框非模态
        log_dialog.show()

    def on_single_shop_invite_clicked(self):
        """处理单店邀约按钮点击事件"""
        print("[单店邀约] 单店邀约按钮被点击")

        # 检查是否已经在邀约中
        if self.is_auto_inviting or (hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting):
            print("[单店邀约] 已经在邀约中，忽略重复点击")
            QMessageBox.warning(self, "提示", "已经在邀约中，请等待当前邀约完成")
            return

        # 检查是否有邀约线程在运行
        if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
            print("[单店邀约] 检测到有邀约线程在运行，先停止")
            # 强制停止旧线程
            self.is_single_shop_inviting = False
            self.is_auto_inviting = False
            if hasattr(self, 'invitation_wait_event'):
                self.invitation_wait_event.set()
            if hasattr(self, 'page_load_event'):
                self.page_load_event.set()
            # 等待线程退出
            self.auto_invitation_thread.join(2.0)
            print("[单店邀约] 旧邀约线程已停止")

        # 检查是否正在准备自动邀约
        if hasattr(self, 'preparing_for_auto_invite') and self.preparing_for_auto_invite:
            print("[单店邀约] 检测到正在准备自动邀约，不允许启动单店邀约")
            QMessageBox.warning(self, "提示", "正在准备自动邀约，请等待完成后再启动单店邀约")
            return

        # 检查是否选择了店铺
        if not self.current_shop_name:
            QMessageBox.warning(self, "提示", "请先选择一个店铺")
            return

        # 重置粉丝量区间记录，新的单店邀约重新开始尝试所有区间
        if hasattr(self, '_original_fans_interval_index'):
            delattr(self, '_original_fans_interval_index')
            print("[单店邀约] 已重置粉丝量区间记录，将重新开始尝试所有区间")

        # 清空单店邀约的已尝试粉丝数选项记录
        if hasattr(self, '_tried_fans_options_single_shop'):
            print("[单店邀约] 清空已尝试的粉丝数选项记录。")
            self._tried_fans_options_single_shop.clear()

        # 设置单店邀约标志
        self.is_single_shop_inviting = True
        print(f"[单店邀约] 开始单店邀约，店铺: '{self.current_shop_name}'")

        # 更新按钮状态
        self.auto_invite_btn.setEnabled(False)
        self.auto_invite_btn.setText("邀约中...")
        self.stop_invite_btn.setEnabled(True)

        # 重置当前店铺的邀约状态
        self.current_shop_invite_status = {
            "status": "开始邀约",
            "count": 0,
            "recorded": False
        }
        print(f"[单店邀约] 已重置店铺 '{self.current_shop_name}' 的邀约状态")

        # 重置单店邀约的独立标志
        self.single_shop_loading_products = False
        self.single_shop_loading_influencers = False
        self.single_shop_products_loaded = False
        self.single_shop_influencers_loaded = False
        self.single_shop_waiting_for_products_then_influencers = False
        self.single_shop_waiting_for_influencers_to_start_invite = False
        print(f"[单店邀约] 已重置所有单店邀约独立标志")

        # 启动单店邀约流程：先加载商品，再加载达人，最后开始邀约
        self._start_single_shop_invitation_workflow()

    def _start_single_shop_invitation_workflow(self):
        """启动单店邀约工作流程：读取邀约记录 -> 商品加载 -> 达人加载 -> 邀约"""
        print(f"[单店邀约] 开始单店邀约工作流程，店铺: '{self.current_shop_name}'")

        # 第零步：从服务器读取店铺的邀约记录并应用筛选条件
        print(f"[单店邀约] 第零步：读取店铺 '{self.current_shop_name}' 的邀约记录...")
        self._load_shop_invitation_record(self.current_shop_name)

        # 第一步：加载商品
        self._start_single_shop_product_loading()

    def _start_single_shop_product_loading(self):
        """单店邀约第一步：加载商品"""
        print(f"[单店邀约] 第一步：开始为店铺 '{self.current_shop_name}' 加载商品...")

        # 设置单店邀约独立标志，表示正在为单店邀约加载商品
        self.single_shop_loading_products = True
        self.loading_products_for_single_shop_invite = True
        self.single_shop_product_loading_shop = self.current_shop_name
        print(f"[单店邀约] 设置单店邀约商品加载标志")

        # 更新按钮状态
        self.auto_invite_btn.setText("加载商品中...")

        try:
            # 调用商品加载方法
            self.on_load_products_clicked()
            print(f"[单店邀约] 商品加载已启动，等待结果...")
        except Exception as e:
            print(f"[单店邀约] 商品加载启动失败: {e}")
            self._handle_single_shop_product_loading_failed(self.current_shop_name, f"启动失败: {e}")

    def _handle_single_shop_product_loading_success(self, shop_name):
        """单店邀约商品加载成功，继续第二步：加载达人"""
        print(f"[单店邀约] 店铺 '{shop_name}' 商品加载成功，继续加载达人...")

        # 重置商品加载标志
        self.loading_products_for_single_shop_invite = False
        self.single_shop_product_loading_shop = None

        # 第二步：开始加载达人
        self._start_single_shop_influencer_loading(shop_name)

    def _handle_single_shop_product_loading_failed(self, shop_name, reason):
        """单店邀约商品加载失败，停止邀约"""
        print(f"[单店邀约] 店铺 '{shop_name}' 商品加载失败: {reason}")

        # 重置商品加载标志
        self.loading_products_for_single_shop_invite = False
        self.single_shop_product_loading_shop = None

        # 停止单店邀约
        self._stop_single_shop_invitation(f"商品加载失败: {reason}")

    def _start_single_shop_influencer_loading(self, shop_name):
        """单店邀约第二步：加载达人"""
        print(f"[单店邀约] 第二步：开始为店铺 '{shop_name}' 加载达人...")

        # 设置单店邀约独立标志，表示正在为单店邀约加载达人
        self.single_shop_loading_influencers = True
        self.loading_influencers_for_single_shop_invite = True
        self.single_shop_influencer_loading_shop = shop_name
        print(f"[单店邀约] 设置单店邀约达人加载标志")

        # 更新按钮状态
        self.auto_invite_btn.setText("加载达人中...")

        try:
            # 调用达人加载方法
            self._prepare_and_load_influencers()
            print(f"[单店邀约] 达人加载已启动，等待结果...")
        except Exception as e:
            print(f"[单店邀约] 达人加载启动失败: {e}")
            self._handle_single_shop_influencer_loading_failed(shop_name, f"启动失败: {e}")

    def _handle_single_shop_influencer_loading_success(self, shop_name):
        """单店邀约达人加载成功，开始第三步：邀约流程"""
        print(f"[单店邀约] 店铺 '{shop_name}' 达人加载成功，开始邀约流程...")

        # 重置单店邀约独立标志
        self.single_shop_loading_influencers = False
        self.loading_influencers_for_single_shop_invite = False
        self.single_shop_influencer_loading_shop = None
        print(f"[单店邀约] 重置单店邀约达人加载标志")

        # 第三步：开始邀约
        self._start_single_shop_invitation_process(shop_name)

    def _handle_single_shop_influencer_loading_failed(self, shop_name, reason):
        """单店邀约达人加载失败，先尝试切换粉丝量区间，如果所有区间都试过了再停止邀约"""
        print(f"[单店邀约] 店铺 '{shop_name}' 达人加载失败: {reason}")

        # 重置单店邀约独立标志
        self.single_shop_loading_influencers = False
        self.loading_influencers_for_single_shop_invite = False
        self.single_shop_influencer_loading_shop = None
        print(f"[单店邀约] 重置单店邀约达人加载标志（失败）")

        # 先尝试切换粉丝量区间重新加载
        if self._try_switch_fans_interval_and_reload():
            print(f"[单店邀约] 已切换粉丝量区间，重新加载达人数据")
            # 重新设置单店邀约独立标志，准备重新加载
            self.single_shop_loading_influencers = True
            self.loading_influencers_for_single_shop_invite = True
            self.single_shop_influencer_loading_shop = shop_name
            print(f"[单店邀约] 重新设置单店邀约达人加载标志（重试）")
            return

        # 如果无法切换粉丝量区间，则停止单店邀约
        print(f"[单店邀约] 所有粉丝量区间都已尝试，停止单店邀约")
        self._stop_single_shop_invitation(f"达人加载失败: {reason}")

    def _start_single_shop_invitation_process(self, shop_name):
        """单店邀约第三步：开始邀约流程"""
        print(f"[单店邀约] 第三步：开始店铺 '{shop_name}' 的邀约流程...")

        # 检查是否已经有邀约线程在运行
        if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
            print(f"[单店邀约] 检测到已有邀约线程在运行，先停止旧线程")
            # 停止旧线程
            self.is_single_shop_inviting = False
            self.is_auto_inviting = False
            if hasattr(self, 'invitation_wait_event'):
                self.invitation_wait_event.set()
            if hasattr(self, 'page_load_event'):
                self.page_load_event.set()
            # 等待旧线程退出
            self.auto_invitation_thread.join(3.0)
            if self.auto_invitation_thread.is_alive():
                print(f"[单店邀约] 警告：旧邀约线程在3秒内未退出")
            else:
                print(f"[单店邀约] 旧邀约线程已成功停止")

        # 设置邀约状态
        self.is_single_shop_inviting = True

        # 更新UI状态
        self.auto_invite_btn.setText("邀约中...")

        # 启动新的邀约线程
        print(f"[单店邀约] 创建新的邀约线程...")
        self.auto_invitation_thread = threading.Thread(target=self._single_shop_invitation_worker, daemon=True)
        self.auto_invitation_thread.start()

        print(f"[单店邀约] 店铺 '{shop_name}' 邀约线程已启动")

    def _single_shop_invitation_worker(self):
        """单店邀约的后台工作线程"""
        print(f"[单店邀约线程] 开始单店邀约工作线程，店铺: '{self.current_shop_name}'")

        # 重置单店邀约的行索引（使用独立的变量）
        self.current_single_shop_invite_row = 0
        processed_in_session = 0

        try:
            while self.is_single_shop_inviting:
                # 检查表格行数
                if self.current_single_shop_invite_row >= self.table.rowCount():
                    print(f"[单店邀约线程] 已处理完所有达人，当前页邀约完成")

                    # 尝试翻页继续邀约
                    if self._try_load_next_page_for_single_shop():
                        print(f"[单店邀约线程] 已翻页，继续邀约下一页")
                        continue
                    else:
                        print(f"[单店邀约线程] 无法翻页，但单店邀约只有达到上限才算完成")
                        print(f"[单店邀约线程] 当前状态：未达到上限，等待5秒后重试翻页")

                        # 等待5秒后重试翻页，最多重试3次
                        retry_count = 0
                        max_retries = 3
                        while retry_count < max_retries and self.is_single_shop_inviting:
                            time.sleep(5)
                            retry_count += 1
                            print(f"[单店邀约线程] 第 {retry_count} 次重试翻页...")

                            if self._try_load_next_page_for_single_shop():
                                print(f"[单店邀约线程] 重试翻页成功，继续邀约")
                                break
                            else:
                                print(f"[单店邀约线程] 第 {retry_count} 次重试翻页失败")

                        if retry_count >= max_retries:
                            print(f"[单店邀约线程] 翻页重试次数已达上限，但未达到邀约上限，继续等待...")
                            # 继续循环，不退出
                            time.sleep(10)  # 等待更长时间

                        continue

                # 获取延时设置
                min_delay = self.interval_min.value()
                max_delay = self.interval_max.value()
                if max_delay < min_delay:
                    max_delay = min_delay
                delay_seconds = 0
                if min_delay > 0 or max_delay > 0:
                    delay_seconds = random.uniform(min_delay, max_delay)

                # 保存当前要处理的行号（使用单店邀约的独立行号）
                current_row = self.current_single_shop_invite_row
                print(f"[单店邀约线程] 发送邀约信号 (行号: {current_row})")
                print(f"[单店邀约线程] 当前表格总行数: {self.table.rowCount()}")

                # 检查行号是否有效
                if current_row >= self.table.rowCount():
                    print(f"[单店邀约线程] 错误：行号 {current_row} 超出表格范围 {self.table.rowCount()}，跳过")
                    break

                # 获取当前行的达人信息用于调试
                try:
                    influencer_id_item = self.table.item(current_row, 1)
                    influencer_nickname_item = self.table.item(current_row, 2)
                    if influencer_id_item and influencer_nickname_item:
                        print(f"[单店邀约线程] 即将邀约达人: {influencer_nickname_item.text()} (ID={influencer_id_item.text()})")
                    else:
                        print(f"[单店邀约线程] 警告：第 {current_row} 行达人信息不完整")
                except Exception as e:
                    print(f"[单店邀约线程] 获取达人信息时出错: {e}")

                # 重置等待事件
                self.invitation_wait_event.clear()
                print(f"[单店邀约线程] 已重置邀约等待事件")

                # 发送信号到主线程执行邀约
                print(f"[单店邀约线程] 发送邀约信号到主线程...")
                self.invite_influencer_signal.emit(current_row)
                processed_in_session += 1

                # 等待邀约结果返回
                print(f"[单店邀约线程] 等待邀约结果返回...")
                wait_timeout = 60  # 增加等待时间到60秒
                wait_start_time = time.time()
                wait_success = False

                while not self.invitation_wait_event.wait(timeout=0.5):  # 增加检查间隔
                    # 检查是否超时
                    if time.time() - wait_start_time > wait_timeout:
                        print(f"[单店邀约线程] 等待邀约结果超时 ({wait_timeout}秒)")
                        break

                    # 检查是否被停止
                    if not self.is_single_shop_inviting:
                        print(f"[单店邀约线程] 检测到停止请求")
                        return

                # 检查等待结果
                if self.invitation_wait_event.is_set():
                    wait_success = True
                    print(f"[单店邀约线程] 邀约结果已返回")
                else:
                    print(f"[单店邀约线程] 邀约结果等待失败")

                # 等待结束后再次检查是否被停止（防止在等待期间被停止）
                if not self.is_single_shop_inviting:
                    print(f"[单店邀约线程] 等待结束后检测到停止请求，立即退出")
                    return

                # 检查邀约结果，如果达到上限则立即停止
                if hasattr(self, 'invitation_result') and self.invitation_result.get('error_code') == 13002:
                    print(f"[单店邀约线程] 检测到达到上限（错误码13002），单店邀约完成")
                    # 更新店铺状态为"已达上限"
                    if self.current_shop_name and hasattr(self, 'current_shop_invite_status'):
                        self.current_shop_invite_status["status"] = "已达上限"
                        print(f"[单店邀约线程] 设置店铺 '{self.current_shop_name}' 状态为'已达上限'")
                    return

                # 如果等待成功，额外等待一段时间确保UI更新完成
                if wait_success:
                    print(f"[单店邀约线程] 额外等待0.5秒确保UI更新完成...")
                    time.sleep(0.5)

                # 移动到下一行（使用单店邀约的独立行号）
                old_row = self.current_single_shop_invite_row
                self.current_single_shop_invite_row += 1
                print(f"[单店邀约线程] 行号递增: {old_row} -> {self.current_single_shop_invite_row}")
                print(f"[单店邀约线程] 下一轮将处理第 {self.current_single_shop_invite_row + 1} 行达人")

                # 添加延时
                if delay_seconds > 0:
                    print(f"[单店邀约线程] 等待 {delay_seconds:.1f} 秒后继续...")
                    time.sleep(delay_seconds)

        except Exception as e:
            print(f"[单店邀约线程] 邀约过程中发生错误: {e}")

        print(f"[单店邀约线程] 单店邀约结束，共处理 {processed_in_session} 个达人")

        # 检查是否是因为达到上限而结束
        reached_limit = False
        if hasattr(self, 'current_shop_invite_status') and self.current_shop_invite_status.get("status") == "已达上限":
            reached_limit = True
            print(f"[单店邀约线程] 单店邀约因达到上限而完成")
        else:
            print(f"[单店邀约线程] 单店邀约因其他原因结束（非达到上限）")

        # 发送完成信号到主线程
        self.single_shop_invite_finished_signal.emit(False)

    def _try_load_next_page_for_single_shop(self):
        """为单店邀约尝试加载下一页达人数据"""
        print(f"[单店邀约] 尝试加载下一页达人数据...")
        print(f"[单店邀约] 当前页码: {getattr(self, 'current_influencer_page', '未知')}")
        print(f"[单店邀约] 当前表格行数: {self.table.rowCount()}")

        try:
            # 首先检查翻页按钮是否启用
            if hasattr(self, 'prev_page_btn'):
                button_enabled = self.prev_page_btn.isEnabled()
                print(f"[单店邀约] 翻页按钮状态: {'启用' if button_enabled else '禁用'}")
                if not button_enabled:
                    print(f"[单店邀约] 翻页按钮未启用，尝试随机切换粉丝数筛选条件")
                    # 尝试随机切换粉丝数筛选条件
                    if self._try_random_fans_filter_for_single_shop():
                        print(f"[单店邀约] 已随机切换粉丝数筛选条件，重新从第一页开始")
                        return True  # 成功切换粉丝数，表示可以继续
                    else:
                        print(f"[单店邀约] 所有粉丝数选项均已尝试，无法继续翻页")
                        return False
                print(f"[单店邀约] 翻页按钮已启用，可以翻页")
            else:
                print(f"[单店邀约] 未找到翻页按钮，尝试强制翻页")

            # 重置等待事件
            self.page_load_event.clear()
            print(f"[单店邀约] 已重置页面加载事件")

            # 在主线程中触发翻页
            print(f"[单店邀约] 调用翻页方法...")
            QMetaObject.invokeMethod(self, "on_next_influencer_page_clicked", Qt.QueuedConnection)

            # 等待翻页完成
            wait_start_time = time.time()
            max_wait_time = 30  # 最多等待30秒
            print(f"[单店邀约] 开始等待翻页完成，最多等待 {max_wait_time} 秒...")

            while not self.page_load_event.wait(timeout=0.1):
                # 检查是否超时
                if time.time() - wait_start_time > max_wait_time:
                    print(f"[单店邀约] 等待翻页超时")
                    return False

                # 检查是否被停止
                if not self.is_single_shop_inviting:
                    print(f"[单店邀约] 在等待翻页时检测到停止请求")
                    return False

            # 检查是否成功翻页
            if self.page_load_event.is_set():
                print(f"[单店邀约] 翻页成功，重置单店邀约行索引")
                print(f"[单店邀约] 翻页后表格行数: {self.table.rowCount()}")
                self.current_single_shop_invite_row = 0  # 重置单店邀约的行索引
                return True

        except Exception as e:
            print(f"[单店邀约] 翻页失败: {e}")

        return False

    def _stop_single_shop_invitation(self, reason="手动停止", show_message=True):
        """停止单店邀约

        Args:
            reason (str): 停止原因
            show_message (bool): 是否显示完成提示弹窗，默认为True
        """
        print(f"[单店邀约] 停止单店邀约，原因: {reason}, 显示提示: {show_message}")

        # 检查是否已经停止，避免重复处理
        if not hasattr(self, 'is_single_shop_inviting') or not self.is_single_shop_inviting:
            print(f"[单店邀约] 单店邀约已经停止，忽略重复的停止请求")
            return

        # 重置单店邀约标志
        self.is_single_shop_inviting = False

        # 重置单店邀约独立标志系统
        self.single_shop_loading_products = False
        self.single_shop_loading_influencers = False
        self.single_shop_products_loaded = False
        self.single_shop_influencers_loaded = False
        self.single_shop_waiting_for_products_then_influencers = False
        self.single_shop_waiting_for_influencers_to_start_invite = False
        print(f"[单店邀约] 已重置所有单店邀约独立标志")

        # 确保自动邀约相关标志也被重置，防止意外触发自动邀约
        self.is_auto_inviting = False
        self.preparing_for_auto_invite = False
        self.switch_shop_requested = False

        # 清除所有可能触发自动邀约的标志
        if hasattr(self, 'auto_adjust_page_for_invitation'):
            self.auto_adjust_page_for_invitation = False
        if hasattr(self, 'waiting_for_products_then_influencers'):
            self.waiting_for_products_then_influencers = False
        if hasattr(self, 'waiting_for_influencers_to_start_invite'):
            self.waiting_for_influencers_to_start_invite = False
        if hasattr(self, 'loading_influencers_for_auto_invite'):
            self.loading_influencers_for_auto_invite = False
        if hasattr(self, 'loading_products_for_auto_invite'):
            self.loading_products_for_auto_invite = False

        # 重要：防止单店邀约完成后触发自动邀约流程
        # 单店邀约是独立的操作，不应该影响自动邀约的状态
        if hasattr(self, 'all_shops_completed_notified'):
            # 不重置这个标志，避免触发"所有店铺完成"的检查
            pass

        print(f"[单店邀约] 已重置所有邀约相关标志，防止意外触发自动邀约")

        # 停止任何正在运行的邀约线程
        if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
            print(f"[单店邀约] 停止邀约线程...")
            # 设置等待事件，让线程能够退出
            if hasattr(self, 'invitation_wait_event'):
                self.invitation_wait_event.set()
            if hasattr(self, 'page_load_event'):
                self.page_load_event.set()
            # 等待线程退出
            self.auto_invitation_thread.join(2.0)
            print(f"[单店邀约] 邀约线程已停止")

        # 停止商品加载线程
        self._stop_product_loading_threads()

        # 停止达人加载线程
        self._stop_influencer_loading_threads()

        # 重置定时邀约触发标志，防止定时邀约意外触发自动邀约
        if hasattr(self, 'timer_invitation_triggered'):
            self.timer_invitation_triggered = False
            print(f"[单店邀约] 已重置定时邀约触发标志，防止意外触发自动邀约")

        # 重置按钮状态（如果还没有在达到上限时处理过）
        if not hasattr(self, '_button_state_handled_on_limit') or not self._button_state_handled_on_limit:
            self.auto_invite_btn.setEnabled(True)
            self.auto_invite_btn.setText("单店邀约")
            self.stop_invite_btn.setEnabled(False)

            # 恢复开始邀约按钮状态
            if hasattr(self, 'start_invite_btn'):
                self.start_invite_btn.setEnabled(True)
                self.start_invite_btn.setText("开始邀约")

            # 恢复加载达人按钮状态
            if hasattr(self, 'add_to_list_btn'):
                self.add_to_list_btn.setEnabled(True)
                self.add_to_list_btn.setText("加载达人")

            # 恢复商品加载按钮状态
            if hasattr(self, 'product_list_btn'):
                self.product_list_btn.setEnabled(True)
                self.product_list_btn.setText("商品列表")

            print(f"[单店邀约] 已恢复所有按钮状态为正常")
        else:
            print(f"[单店邀约] 按钮状态已在达到上限时处理过，跳过重复设置")
            # 重置标志，为下次使用做准备
            self._button_state_handled_on_limit = False

        # 记录邀约状态
        if self.current_shop_name and hasattr(self, 'current_shop_invite_status'):
            status = self.current_shop_invite_status.get("status", "")
            count = self.current_shop_invite_status.get("count", 0)

            # 根据停止原因和当前状态决定最终状态
            if status == "已达上限":
                # 如果已经设置为"已达上限"，保持不变
                final_status = "已达上限"
            elif reason == "手动停止":
                final_status = "手动停止"
            elif reason == "程序退出":
                final_status = "程序退出"
            else:
                # 其他情况，如果没有达到上限，则记录为"未完成"
                final_status = "未完成"

            if not self.current_shop_invite_status.get("recorded", False):
                print(f"[单店邀约] 记录店铺 '{self.current_shop_name}' 邀约状态: {final_status}, 次数: {count}")
                InviteLogDialog.add_log_entry(self.current_shop_name, final_status, count)
                self.current_shop_invite_status["recorded"] = True
                self.current_shop_invite_status["status"] = final_status

        # 根据参数决定是否显示完成提示
        if show_message:
            if self.current_shop_name and hasattr(self, 'current_shop_invite_status'):
                final_status = self.current_shop_invite_status.get("status", "未知")
                if final_status == "已达上限":
                    QMessageBox.information(self, "单店邀约完成", f"店铺 '{self.current_shop_name}' 已达到邀约上限，邀约完成。")
                elif final_status == "手动停止":
                    QMessageBox.information(self, "单店邀约停止", f"店铺 '{self.current_shop_name}' 的邀约已手动停止。")
                else:
                    QMessageBox.information(self, "单店邀约结束", f"店铺 '{self.current_shop_name}' 的邀约已结束（{final_status}）。")
            else:
                QMessageBox.information(self, "单店邀约结束", f"店铺 '{self.current_shop_name}' 的邀约已结束。")

    def _stop_product_loading_threads(self):
        """停止商品加载线程"""
        print("[停止加载] 开始停止商品加载线程...")

        # 停止达人邀约自己的商品加载线程
        if hasattr(self, 'product_loading_stop_flag'):
            self.product_loading_stop_flag = True
            print("[停止加载] 已设置达人邀约商品加载停止标志")

        # 停止商品管理器中的加载线程
        if hasattr(self, 'main_window_ref') and self.main_window_ref:
            try:
                # 获取商品管理器实例
                product_manager = getattr(self.main_window_ref, 'product_manager', None)
                if product_manager and hasattr(product_manager, 'stop_loading'):
                    print("[停止加载] 调用商品管理器的停止加载方法")
                    product_manager.stop_loading()
                else:
                    print("[停止加载] 商品管理器不存在或没有停止加载方法")
            except Exception as e:
                print(f"[停止加载] 停止商品管理器加载线程时出错: {e}")

        # 重置商品加载相关标志
        if hasattr(self, 'loading_products_for_single_shop_invite'):
            self.loading_products_for_single_shop_invite = False
            print("[停止加载] 已重置单店邀约商品加载标志")

        if hasattr(self, 'loading_products_for_auto_invite'):
            self.loading_products_for_auto_invite = False
            print("[停止加载] 已重置自动邀约商品加载标志")

        # 恢复商品加载按钮状态
        try:
            self.product_list_btn.setEnabled(True)
            self.product_list_btn.setText("商品列表")
            print("[停止加载] 已恢复商品加载按钮状态")
        except Exception as e:
            print(f"[停止加载] 恢复商品加载按钮状态时出错: {e}")

    def _stop_influencer_loading_threads(self):
        """停止达人加载线程"""
        print("[停止加载] 开始停止达人加载线程...")

        # 停止达人加载线程
        if hasattr(self, 'influencer_loading_stop_flag'):
            self.influencer_loading_stop_flag = True
            print("[停止加载] 已设置达人加载停止标志")

        # 重置达人加载相关标志
        if hasattr(self, 'loading_influencers'):
            self.loading_influencers = False
            print("[停止加载] 已重置达人加载标志")

        if hasattr(self, 'loading_influencers_for_single_shop_invite'):
            self.loading_influencers_for_single_shop_invite = False
            print("[停止加载] 已重置单店邀约达人加载标志")

        if hasattr(self, 'loading_influencers_for_auto_invite'):
            self.loading_influencers_for_auto_invite = False
            print("[停止加载] 已重置自动邀约达人加载标志")

        # 恢复达人加载按钮状态
        try:
            self.add_to_list_btn.setEnabled(True)
            self.add_to_list_btn.setText("加载达人")
            self.prev_page_btn.setEnabled(False)
            print("[停止加载] 已恢复达人加载按钮状态")
        except Exception as e:
            print(f"[停止加载] 恢复达人加载按钮状态时出错: {e}")

        # 注意：由于达人加载使用的是daemon线程，它们会在主程序退出时自动结束
        # 这里主要是重置状态标志和UI状态

    def _handle_single_shop_invite_finished(self, interrupted):
        """处理单店邀约完成信号"""
        print(f"[单店邀约] 收到单店邀约完成信号，中断状态: {interrupted}")

        # 调用停止单店邀约方法
        reason = "手动停止" if interrupted else "邀约完成"
        self._stop_single_shop_invitation(reason)

    def _handle_stop_invitation_clicked(self):
        """处理停止邀约按钮点击事件，支持自动邀约和单店邀约"""
        print("[停止邀约] 停止邀约按钮被点击")

        # 检查当前是哪种邀约模式
        if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
            print("[停止邀约] 检测到单店邀约模式，停止单店邀约")
            self._stop_single_shop_invitation("手动停止")
        elif self.is_auto_inviting:
            print("[停止邀约] 检测到自动邀约模式，停止自动邀约")
            self._stop_auto_invitation(finished_naturally=False, reason="手动停止")
        else:
            print("[停止邀约] 当前没有正在进行的邀约")

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        # 如果有正在进行的邀约，提示用户
        if self.is_auto_inviting or (hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting):
            invitation_type = "单店邀约" if (hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting) else "自动邀约"
            reply = QMessageBox.question(self, '确认退出', f'{invitation_type}正在进行中，确定要退出吗？',
                                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                event.ignore()
                return
            else:
                # 停止邀约
                if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
                    self._stop_single_shop_invitation("程序退出")
                elif self.is_auto_inviting:
                    self._stop_auto_invitation(finished_naturally=False, reason="程序退出")

        # 记录最后一个店铺的邀约情况
        if self.current_shop_name and not self.current_shop_invite_status["recorded"] and self.current_shop_invite_status["status"]:
            # 记录最后一个店铺的邀约状态
            status = self.current_shop_invite_status["status"]
            count = self.current_shop_invite_status["count"]

            # 检查是否是重复记录，但在退出时不需要处理重复记录的情况
            InviteLogDialog.add_log_entry(self.current_shop_name, status, count)
            print(f"[邀约日志] 程序退出时记录店铺 '{self.current_shop_name}' 的邀约状态: {status}, 次数: {count}")

        # 接受关闭事件
        event.accept()

    def _check_shop_reached_limit(self, shop_name):
        """检查店铺是否已达上限

        Args:
            shop_name (str): 店铺名称

        Returns:
            bool: 如果店铺已达上限返回True，否则返回False
        """
        try:
            # 使用配置文件路径函数获取正确的日志文件路径
            log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))
            print(f"[邀约日志] 尝试读取日志文件: {log_file_path}")

            # 如果日志文件不存在，直接返回False
            if not os.path.exists(log_file_path):
                print(f"[邀约日志] 日志文件不存在，店铺 '{shop_name}' 未达上限")
                return False

            # 读取日志文件
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # 获取当前日期
            current_date = time.strftime("%Y-%m-%d")

            # 检查店铺是否在当天的日志中且状态为"已达上限"
            for entry in log_data:
                if (entry.get("shop_name") == shop_name and
                    entry.get("date", "").startswith(current_date) and
                    entry.get("status") == "已达上限"):
                    print(f"[邀约日志] 店铺 '{shop_name}' 在日志中状态为'已达上限'")
                    return True

            print(f"[邀约日志] 店铺 '{shop_name}' 未达上限")
            return False

        except Exception as e:
            print(f"[错误] 检查店铺是否达上限失败: {e}")
            return False

    def _check_shop_in_log(self, shop_name):
        """检查店铺是否在当天的邀约日志中有记录

        现在使用独立的ShopManager类

        Args:
            shop_name (str): 店铺名称

        Returns:
            bool: 如果店铺在日志中有记录返回True，否则返回False
        """
        return ShopManager.check_shop_in_log(shop_name)

    def _get_next_shop_name(self, current_shop_name):
        """获取下一个店铺名称，跳过已在日志中有记录的店铺

        现在使用独立的ShopManager类从config\账号列表.json读取店铺信息

        Args:
            current_shop_name (str): 当前店铺名称

        Returns:
            str: 下一个店铺名称，如果没有下一个店铺则返回None
        """
        print(f"[店铺切换] 使用ShopManager查找店铺 '{current_shop_name}' 的下一个店铺")
        return ShopManager.get_next_shop_name(current_shop_name)

    def _check_all_shops_in_log(self):
        """检查所有店铺是否都在当天的邀约日志中有记录

        Returns:
            bool: 如果所有店铺都在日志中有记录返回True，否则返回False
        """
        # 检查是否已经通知过所有店铺完成
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print("[邀约日志] 已经通知过所有店铺完成，跳过重复检查")
            return True

        print(f"[邀约日志] 使用ShopManager检查所有店铺是否都在日志中")
        return ShopManager.check_all_shops_in_log()

    def _handle_all_shops_completed(self, caller_name="未知"):
        """统一处理所有店铺邀约完成的情况，避免重复弹窗和处理

        Args:
            caller_name (str): 调用者名称，用于调试

        Returns:
            bool: 如果成功处理返回True，如果已经处理过返回False
        """
        print(f"[邀约完成处理] 被 {caller_name} 调用")

        # 检查是否已经处理过
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print(f"[邀约完成处理] 已经处理过所有店铺完成，忽略来自 {caller_name} 的重复调用")
            return False

        # 再次确认所有店铺确实都完成了
        if not self._check_all_shops_in_log():
            print(f"[邀约完成处理] 检查发现并非所有店铺都完成，忽略来自 {caller_name} 的调用")
            return False

        print(f"[邀约完成处理] 确认所有店铺都已完成，开始处理...")

        # 设置标志，防止重复处理
        self.all_shops_completed_notified = True

        # 在停止邀约流程前，先确保最后一个店铺的状态被记录
        if self.current_shop_name and hasattr(self, 'current_shop_invite_status'):
            current_status = self.current_shop_invite_status.get("status", "")
            current_count = self.current_shop_invite_status.get("count", 0)
            already_recorded = self.current_shop_invite_status.get("recorded", False)

            if current_status and not already_recorded:
                print(f"[邀约完成处理] 记录最后一个店铺 '{self.current_shop_name}' 的状态")
                InviteLogDialog.add_log_entry(self.current_shop_name, current_status, current_count)
                self.current_shop_invite_status["recorded"] = True
                print(f"[邀约完成处理] 已记录最后一个店铺 '{self.current_shop_name}' 状态: {current_status}, 次数: {current_count}")
            elif current_status and already_recorded:
                print(f"[邀约完成处理] 最后一个店铺 '{self.current_shop_name}' 状态已记录: {current_status}, 次数: {current_count}")
            else:
                # 如果没有状态，设置一个默认状态并记录
                default_status = "邀约完成"
                print(f"[邀约完成处理] 最后一个店铺 '{self.current_shop_name}' 无状态，设置默认状态: {default_status}")
                InviteLogDialog.add_log_entry(self.current_shop_name, default_status, current_count)
                self.current_shop_invite_status["status"] = default_status
                self.current_shop_invite_status["recorded"] = True
                print(f"[邀约完成处理] 已记录最后一个店铺 '{self.current_shop_name}' 默认状态: {default_status}, 次数: {current_count}")

        # 强制停止邀约流程和线程
        print(f"[邀约完成处理] 强制停止邀约流程...")
        self.is_auto_inviting = False
        self.preparing_for_auto_invite = False

        # 强制设置所有等待事件，确保邀约线程能够立即退出
        if hasattr(self, 'invitation_wait_event'):
            self.invitation_wait_event.set()
            print(f"[邀约完成处理] 已设置invitation_wait_event")
        if hasattr(self, 'page_load_event'):
            self.page_load_event.set()
            print(f"[邀约完成处理] 已设置page_load_event")

        # 等待邀约线程退出
        if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
            print(f"[邀约完成处理] 等待邀约线程退出...")
            self.auto_invitation_thread.join(3.0)  # 最多等待3秒
            if self.auto_invitation_thread.is_alive():
                print(f"[邀约完成处理] 警告：邀约线程在3秒内未退出")
            else:
                print(f"[邀约完成处理] 邀约线程已成功退出")

        # 显示弹窗（只显示一次）
        QMessageBox.information(self, "邀约完成", "所有店铺已经邀约完成，自动停止邀约过程。")

        # 调用停止邀约方法（会自动处理按钮状态更新和邀约流程停止）
        self._stop_auto_invitation(finished_naturally=True, reason="所有店铺已邀约完成")

        print(f"[邀约完成处理] 处理完成，来自 {caller_name}")
        return True

    def _force_record_current_shop_and_complete(self, reason="无下一个店铺"):
        """强制记录当前店铺状态并完成邀约流程

        当找不到下一个店铺时调用，确保最后一个店铺的状态被记录

        Args:
            reason (str): 完成原因
        """
        print(f"[强制完成] 强制记录当前店铺并完成邀约，原因: {reason}")

        # 强制记录当前店铺状态
        if self.current_shop_name and hasattr(self, 'current_shop_invite_status'):
            current_status = self.current_shop_invite_status.get("status", "")
            current_count = self.current_shop_invite_status.get("count", 0)
            already_recorded = self.current_shop_invite_status.get("recorded", False)

            if not already_recorded:
                if not current_status:
                    # 如果没有状态，设置默认状态
                    current_status = "邀约完成"
                    self.current_shop_invite_status["status"] = current_status

                print(f"[强制完成] 强制记录店铺 '{self.current_shop_name}' 状态: {current_status}, 次数: {current_count}")
                InviteLogDialog.add_log_entry(self.current_shop_name, current_status, current_count)
                self.current_shop_invite_status["recorded"] = True
            else:
                print(f"[强制完成] 店铺 '{self.current_shop_name}' 状态已记录: {current_status}, 次数: {current_count}")

        # 设置完成标志
        self.all_shops_completed_notified = True

        # 停止邀约流程
        self.is_auto_inviting = False
        self.preparing_for_auto_invite = False

        # 更新UI
        self.start_invite_btn.setEnabled(True)
        self.start_invite_btn.setText("已完成")
        self.stop_invite_btn.setEnabled(False)

        # 强制设置所有等待事件
        if hasattr(self, 'invitation_wait_event'):
            self.invitation_wait_event.set()
        if hasattr(self, 'page_load_event'):
            self.page_load_event.set()

        # 等待邀约线程退出
        if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
            print(f"[强制完成] 等待邀约线程退出...")
            self.auto_invitation_thread.join(3.0)

        # 显示完成消息
        QMessageBox.information(self, "邀约完成", "所有店铺已经邀约完成，自动停止邀约过程。")

        print(f"[强制完成] 邀约流程已强制完成")

    def _record_current_shop_status(self, status, count=None):
        """记录当前店铺的状态到日志

        Args:
            status (str): 店铺状态
            count (int, optional): 邀约次数，如果为None则使用当前状态中的count
        """
        if not self.current_shop_name:
            print(f"[记录状态] 没有当前店铺，跳过记录")
            return

        if count is None:
            count = self.current_shop_invite_status.get("count", 0) if hasattr(self, 'current_shop_invite_status') else 0

        print(f"[记录状态] 记录店铺 '{self.current_shop_name}' 状态: {status}, 次数: {count}")
        InviteLogDialog.add_log_entry(self.current_shop_name, status, count)

        # 更新当前店铺状态
        if hasattr(self, 'current_shop_invite_status'):
            self.current_shop_invite_status["status"] = status
            self.current_shop_invite_status["count"] = count
            self.current_shop_invite_status["recorded"] = True

    def update_invitation_settings(self, shop_name):
        """根据店铺名称更新邀约设置字段（现在使用ShopManager）"""
        print(f"[邀约设置] 使用ShopManager更新店铺 '{shop_name}' 的邀约设置")

        # 使用ShopManager获取店铺数据
        shop_data = ShopManager.get_shop_by_name(shop_name)

        if shop_data:
            # 根据映射关系更新字段
            self.contact_input.setText(shop_data.get("邀约姓名", ""))
            self.phone_input.setText(shop_data.get("邀约手机号", ""))
            self.wechat_input.setText(shop_data.get("邀约微信", ""))
            self.desc_text.setText(shop_data.get("邀约描述", ""))
            print(f"[邀约设置] 已更新邀约设置，使用店铺 '{shop_name}' 的数据")
        else:
            # 如果找不到店铺数据，清空字段
            self.contact_input.clear()
            self.phone_input.clear()
            self.wechat_input.clear()
            self.desc_text.clear()
            print(f"[邀约设置] 警告：未找到店铺 '{shop_name}' 的邀约数据，已清空字段")

    def set_current_shop(self, shop_name):
        """由主窗口调用，设置当前选中的店铺名称（已禁用，达人邀约功能现在独立）"""
        print(f"[DEBUG InfluencerUI] set_current_shop 被主窗口调用，但达人邀约功能已独立，忽略此调用")
        print(f"[DEBUG InfluencerUI] 主窗口尝试设置店铺: {shop_name}，但不会影响达人邀约功能")
        return

    @pyqtSlot()
    def on_load_products_clicked(self):
        """处理"商品列表"按钮点击事件 - 使用并发 API 请求"""
        # 检查是否已经在加载商品，防止重复加载
        if self.product_list_btn.text() == "加载中...":
            print("[UI] 商品正在加载中，跳过重复加载请求")
            return

        if not self.current_shop_name:
            QMessageBox.warning(self, "提示", "请先在顶部菜单栏选择一个店铺")
            return

        # 显示加载状态
        self.product_list_btn.setEnabled(False)
        self.product_list_btn.setText("加载中...")
        self.selected_products_table.setRowCount(0)  # 清空表格

        # 在表格中显示加载中提示
        loading_item = QTableWidgetItem("正在动态加载店铺所有商品数据，请稍候...")
        loading_item.setTextAlignment(Qt.AlignCenter)
        self.selected_products_table.setRowCount(1)
        self.selected_products_table.setItem(0, 0, loading_item)
        self.selected_products_table.setSpan(0, 0, 1, self.selected_products_table.columnCount())

        # 更新UI
        QApplication.processEvents()

        print(f"[并发API] 请求加载店铺 '{self.current_shop_name}' 的商品列表...")

        # 使用ShopManager获取店铺信息
        shop_info = ShopManager.get_shop_by_name(self.current_shop_name)
        if not shop_info:
            self.product_list_btn.setEnabled(True)
            self.product_list_btn.setText("商品列表")
            self.selected_products_table.setRowCount(0)
            QMessageBox.warning(self, "错误", f"未找到店铺 '{self.current_shop_name}' 的信息。")
            return

        cookies_list = shop_info.get('cookies_list', [])
        if not cookies_list:
            self.product_list_btn.setEnabled(True)
            self.product_list_btn.setText("商品列表")
            self.selected_products_table.setRowCount(0)
            QMessageBox.warning(self, "错误", f"未能获取店铺 '{self.current_shop_name}' 的 Cookie。\n请先在账号管理页面登录该店铺。")
            return

        # --- 提取关键 Cookie 值 --- #
        cookie_dict = {c['name']: c['value'] for c in cookies_list}
        biz_magic = cookie_dict.get('biz_magic')
        api_token = cookie_dict.get('biz_token')
        cookie_header_string = "; ".join([f"{c['name']}={c['value']}" for c in cookies_list])

        if not biz_magic or not api_token:
            missing = []
            if not biz_magic: missing.append('biz_magic')
            if not api_token: missing.append('biz_token')

            self.product_list_btn.setEnabled(True)
            self.product_list_btn.setText("商品列表")
            self.selected_products_table.setRowCount(0)

            QMessageBox.warning(self, "错误", f"从 Cookie 中未能提取必要的参数: {', '.join(missing)}。")
            return # 确保和 QMessageBox 对齐

        # --- 重置状态和UI --- #
        self.page_results_cache = {} # 清空缓存
        self.received_pages = 0
        # --- 修改：动态加载所有商品，初始加载更多页数 ---
        initial_concurrent_pages = 10 # 初始同时获取前10页，如果商品很多会继续加载
        num_concurrent_pages = initial_concurrent_pages
        self.expected_pages = num_concurrent_pages

        print(f"[并发API] 准备启动 {num_concurrent_pages} 个线程获取前 {num_concurrent_pages} 页数据（动态加载所有商品）...")
        # --- 启动后台线程执行并发请求 --- #
        # 将认证信息打包传递
        auth_info = {
            'api_token': api_token,
            'biz_magic': biz_magic,
            'cookie_header_string': cookie_header_string
        }

        try:
            # 重置停止标志
            self.product_loading_stop_flag = False
            print("[商品加载] 已重置商品加载停止标志")

            # 创建并启动线程
            worker_thread = threading.Thread(target=self._concurrent_fetch_worker,
                                            args=(initial_concurrent_pages, auth_info),
                                            daemon=True) # 设置为守护线程
            worker_thread.start()

            # UI 线程不阻塞，等待 worker 线程通过信号通知结果
            print("[并发API] 后台任务已启动，UI 保持响应。")

            # 添加超时处理，如果30秒后仍未有结果，恢复按钮状态
            # 注意：这里只是UI超时保护，实际的成功/失败判断由API返回结果决定
            def check_timeout():
                if self.product_list_btn.text() == "加载中...":
                    self.product_list_btn.setEnabled(True)
                    self.product_list_btn.setText("商品列表")
                    print("[UI] 商品加载UI超时保护触发，恢复按钮状态")
                    # 注意：不在这里处理自动邀约逻辑，由API返回结果决定

            # 30秒后执行超时检查（仅恢复UI状态）
            QTimer.singleShot(30000, check_timeout)

        except Exception as e:
            print(f"[UI] 启动工作线程时出错: {str(e)}")
            self.product_list_btn.setEnabled(True)
            self.product_list_btn.setText("商品列表")
            QMessageBox.warning(self, "错误", f"启动数据获取任务失败: {str(e)}")
            self.selected_products_table.setRowCount(0)

    def _concurrent_fetch_worker(self, initial_pages, auth_info):
        """后台工作线程，动态加载所有商品数据，实时显示加载进度"""
        print(f"[WorkerThread] 开始执行动态商品加载，初始页数: {initial_pages}")
        page_size = 20 # 保持与 _fetch_single_page 一致
        current_batch_start = 1
        max_workers = min(initial_pages, 3) # 降低并发数，避免服务器限流
        total_loaded_products = 0

        # 初始化停止标志
        if not hasattr(self, 'product_loading_stop_flag'):
            self.product_loading_stop_flag = False

        # --- 新增: 改进的停止条件判断 ---
        max_pages = 200  # 最大加载页数限制，避免无限加载
        empty_page_threshold = 2  # 连续2页为空才停止
        # --- 结束新增 ---

        while current_batch_start <= max_pages:
            # 检查停止标志
            if hasattr(self, 'product_loading_stop_flag') and self.product_loading_stop_flag:
                print(f"[WorkerThread] 检测到停止标志，终止商品加载")
                break
            # 计算当前批次要获取的页面范围
            current_batch_end = min(current_batch_start + initial_pages - 1, max_pages)
            current_batch_pages = list(range(current_batch_start, current_batch_end + 1))

            print(f"[WorkerThread] 开始获取第 {current_batch_start}-{current_batch_end} 页...")

            # 当前批次的结果
            batch_results = {}
            should_stop = False

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(self._fetch_single_page, page,
                                           auth_info['api_token'],
                                           auth_info['biz_magic'],
                                           auth_info['cookie_header_string']): page
                           for page in current_batch_pages}

                for future in concurrent.futures.as_completed(futures):
                    # 在处理每个结果前检查停止标志
                    if hasattr(self, 'product_loading_stop_flag') and self.product_loading_stop_flag:
                        print(f"[WorkerThread] 在处理结果时检测到停止标志，取消剩余任务")
                        # 取消所有未完成的任务
                        for f in futures:
                            if not f.done():
                                f.cancel()
                        break

                    page_num = futures[future]
                    try:
                        _, products = future.result()
                        print(f"[WorkerThread] 收到 page={page_num} 的结果，商品数: {len(products) if products is not None else 'Error'}")

                        batch_results[page_num] = products # 记录结果（包括 None）

                        # 实时发送单页结果到主线程进行显示
                        if products is not None and len(products) > 0:
                            print(f"[WorkerThread] 实时发送第 {page_num} 页数据到主线程显示")
                            self.products_loaded.emit(products, page_num)
                            total_loaded_products += len(products)

                    except Exception as exc:
                        print(f'[WorkerThread] page={page_num} 生成异常: {exc}')
                        batch_results[page_num] = None # 标记错误

            # --- 修改: 改进的停止条件判断 ---
            # 按页码顺序检查结果，更新连续计数器
            sorted_pages = sorted(batch_results.keys())

            # 重新计算连续空页面数（从最高页码开始往下检查）
            consecutive_empty_from_end = 0
            for page_num in reversed(sorted_pages):
                products = batch_results[page_num]

                if products is None:
                    # API调用失败，不计入空页面判断
                    print(f"[WorkerThread] page={page_num} API调用失败，不计入停止判断")
                    continue
                elif len(products) == 0:
                    # 空页面，从末尾开始计数
                    consecutive_empty_from_end += 1
                    print(f"[WorkerThread] page={page_num} 为空页面，从末尾连续空页面数: {consecutive_empty_from_end}")
                else:
                    # 有商品的页面，停止计数
                    print(f"[WorkerThread] page={page_num} 返回 {len(products)} 个商品，停止从末尾计数")
                    break

            # 检查是否应该停止 - 只有从最高页码开始连续空页才停止
            if consecutive_empty_from_end >= empty_page_threshold and len(sorted_pages) >= empty_page_threshold:
                print(f"[WorkerThread] 从最高页码开始连续 {consecutive_empty_from_end} 页为空，达到阈值 {empty_page_threshold}，停止加载")
                should_stop = True
            elif current_batch_end >= max_pages:
                print(f"[WorkerThread] 已达到最大页数限制 {max_pages}，停止加载")
                should_stop = True
            else:
                print(f"[WorkerThread] 继续加载：从末尾连续空页数={consecutive_empty_from_end}，阈值={empty_page_threshold}，当前批次页数={len(sorted_pages)}")

            if should_stop:
                break

            # 检查当前批次是否有成功的页面，如果没有则停止
            successful_pages_in_batch = [p for p, data in batch_results.items() if data is not None]
            if not successful_pages_in_batch:
                print(f"[WorkerThread] 当前批次没有成功获取任何页面，停止加载")
                break

            # 准备下一批次
            current_batch_start = current_batch_end + 1
            print(f"[WorkerThread] 准备加载下一批次，从第 {current_batch_start} 页开始...")

        # 检查是否被停止
        if hasattr(self, 'product_loading_stop_flag') and self.product_loading_stop_flag:
            print(f"[WorkerThread] 商品加载被停止，总计加载 {total_loaded_products} 个商品")
            # 被停止时也发送完成信号，但标记为停止状态
            self.batch_fetch_complete.emit({"total_products": total_loaded_products, "stopped": True})
            return

        print(f"[WorkerThread] 动态加载完成，总计加载 {total_loaded_products} 个商品")

        # --- 发送加载完成信号给主线程，包含实际商品数量 --- #
        print(f"[WorkerThread] 发送加载完成信号，实际商品数: {total_loaded_products}")
        # 发送包含实际商品数量的结果字典，主线程会根据此数量判断加载结果
        self.batch_fetch_complete.emit({"total_products": total_loaded_products})

    # --- 修改: 处理最终批量结果的槽函数，使用API返回的实际商品数量 --- #
    def _handle_batch_fetch_complete(self, results_dict):
        """槽函数：在主线程处理动态加载完成信号"""
        print(f"[UI 主线程] 收到动态加载完成信号")

        # 恢复按钮状态
        self.product_list_btn.setEnabled(True)
        self.product_list_btn.setText("商品列表")

        # 获取API返回的实际商品总数和停止状态
        actual_product_count = results_dict.get("total_products", 0)
        is_stopped = results_dict.get("stopped", False)
        print(f"[UI 主线程] 动态加载完成，API返回实际商品数: {actual_product_count}，是否被停止: {is_stopped}")

        # 获取当前表格中的行数（可能包含提示信息）
        current_table_rows = self.selected_products_table.rowCount()
        print(f"[UI 主线程] 当前表格行数: {current_table_rows}")

        # 判断加载是否成功（基于API返回的实际商品数量，而不是表格行数）
        # 如果被停止，则根据已加载的商品数量判断
        product_load_success = actual_product_count > 0

        if actual_product_count == 0:
            # 没有加载到任何商品，显示提示信息
            print("[UI 主线程] 未加载到任何商品数据")
            item = QTableWidgetItem("店铺暂无符合条件的商品数据。")
            item.setTextAlignment(Qt.AlignCenter)
            self.selected_products_table.setRowCount(1)
            self.selected_products_table.setItem(0, 0, item)
            self.selected_products_table.setSpan(0, 0, 1, self.selected_products_table.columnCount())
        else:
            print(f"[UI 主线程] 动态加载成功完成，共加载 {actual_product_count} 个商品")
            # 滚动到顶部方便查看
            self.selected_products_table.scrollToTop()

        # 处理被停止的情况
        if is_stopped:
            print("[UI 主线程] 商品加载被用户停止")
            return  # 被停止时直接返回，不执行后续逻辑

        # 处理自动邀约相关逻辑
        if not product_load_success: # 加载本身失败
             print("[UI 主线程] 错误：商品数据加载失败（API调用失败或返回异常）")

             # 检查是否在自动邀约过程中
             if self.is_auto_inviting:
                 # 在自动邀约过程中，不显示弹窗
                 print(f"[UI 主线程] 自动邀约过程中商品数据加载失败")

                 # 记录店铺状态为"加载失败"
                 if self.current_shop_name:
                     # 更新邀约日志中的状态
                     InviteLogDialog.add_log_entry(self.current_shop_name, "加载失败", 0)

                     # 记录跳过信息
                     skip_reason = "加载失败"
                     self.skipped_shops_info[self.current_shop_name] = skip_reason
                     print(f"[Skip Record] 已记录店铺 '{self.current_shop_name}' 跳过，原因: {skip_reason}")

                     # 先记录当前店铺的失败状态
                     print(f"[商品加载失败] 先记录当前店铺 '{self.current_shop_name}' 的失败状态")
                     self._record_current_shop_status("商品加载失败")

                     # 然后获取下一个未在日志中有记录的店铺
                     next_shop = self._get_next_shop_name(self.current_shop_name)
                     if next_shop:
                         print(f"[邀约日志] 找到下一个店铺: '{next_shop}'，准备切换")
                         # 使用新方法清空达人列表并发送切换店铺信号
                         self._clear_table_and_switch_shop(next_shop)
                     else:
                         print("[邀约日志] 无法找到下一个店铺，所有店铺已完成")
                         # 所有店铺已完成
                         self._force_record_current_shop_and_complete("商品加载失败-所有店铺已完成")
             elif not (hasattr(self, 'waiting_for_products_then_influencers') and self.waiting_for_products_then_influencers):
                  # 非自动邀约过程中，显示弹窗提示
                  QMessageBox.warning(self, "加载失败", "加载商品数据失败，请检查网络或Cookie。")

        # --- 修改: 处理有序邀约流程中的商品加载，使用实际商品数量 ---
        # 添加调试日志检查标志状态
        print(f"[调试] 检查有序邀约标志: hasattr(loading_products_for_auto_invite)={hasattr(self, 'loading_products_for_auto_invite')}")
        if hasattr(self, 'loading_products_for_auto_invite'):
            print(f"[调试] loading_products_for_auto_invite值: {self.loading_products_for_auto_invite}")

        if hasattr(self, 'loading_products_for_auto_invite') and self.loading_products_for_auto_invite:
            print(f"[有序邀约] 检测到正在为自动邀约加载商品")
            shop_name = getattr(self, 'product_loading_shop', self.current_shop_name)

            if product_load_success and actual_product_count > 0:
                print(f"[有序邀约] 店铺 '{shop_name}' 商品加载成功，共 {actual_product_count} 个商品")
                self._handle_product_loading_success(shop_name)
            elif product_load_success and actual_product_count == 0:
                print(f"[有序邀约] 店铺 '{shop_name}' 商品加载成功但列表为空")
                self._handle_product_loading_failed(shop_name, "商品列表为空")
            else:
                print(f"[有序邀约] 店铺 '{shop_name}' 商品加载失败")
                self._handle_product_loading_failed(shop_name, "API调用失败")
            return  # 有序邀约流程处理完毕，不继续执行旧的链式加载逻辑

        # +++ 修改: 处理单店邀约流程中的商品加载，使用实际商品数量 +++
        if hasattr(self, 'loading_products_for_single_shop_invite') and self.loading_products_for_single_shop_invite:
            print(f"[单店邀约] 检测到正在为单店邀约加载商品")
            shop_name = getattr(self, 'single_shop_product_loading_shop', self.current_shop_name)

            if product_load_success and actual_product_count > 0:
                print(f"[单店邀约] 店铺 '{shop_name}' 商品加载成功，共 {actual_product_count} 个商品")
                self._handle_single_shop_product_loading_success(shop_name)
            elif product_load_success and actual_product_count == 0:
                print(f"[单店邀约] 店铺 '{shop_name}' 商品加载成功但列表为空")
                self._handle_single_shop_product_loading_failed(shop_name, "商品列表为空")
            else:
                print(f"[单店邀约] 店铺 '{shop_name}' 商品加载失败")
                self._handle_single_shop_product_loading_failed(shop_name, "API调用失败")
            return  # 单店邀约流程处理完毕，不继续执行旧的链式加载逻辑
        # +++ 结束修改 +++

        # --- 修改: 处理链式加载 ---
        if hasattr(self, 'waiting_for_products_then_influencers') and self.waiting_for_products_then_influencers:
            print(f"[Chain Load] 检测到 waiting_for_products_then_influencers 标志位为 True.") # <--- 添加标志位日志
            self.waiting_for_products_then_influencers = False # 重置标志
            print(f"[Chain Load] waiting_for_products_then_influencers 标志位已重置为 False.") # <--- 添加标志位重置日志

            if product_load_success:
                # --- 修改商品数量检查日志，使用实际商品数量 ---
                print(f"[Chain Load Check] 商品加载成功. API返回实际商品数: {actual_product_count}")
                # --- 结束修改 ---
                if actual_product_count == 0:
                     print(f"[Chain Load Check] 商品列表加载成功但实际商品数为0，这是正常情况（店铺没有符合条件的商品）")

                     # 商品列表为空，记录到日志但不视为失败
                     skip_reason = "商品列表为空"
                     print(f"[店铺切换] 检测到店铺 '{self.current_shop_name}' {skip_reason}。准备切换店铺...")

                     # 记录跳过信息
                     if self.current_shop_name:
                         self.skipped_shops_info[self.current_shop_name] = skip_reason
                         print(f"[Skip Record] 已记录店铺 '{self.current_shop_name}' 跳过，原因: {skip_reason}")

                         # 更新邀约日志中的状态 - 记录为具体原因而不是"停止邀约"
                         InviteLogDialog.add_log_entry(self.current_shop_name, skip_reason, 0)

                     # 获取下一个未在日志中有记录的店铺
                     next_shop = self._get_next_shop_name(self.current_shop_name)
                     if next_shop:
                         print(f"[邀约日志] 找到下一个未在日志中有记录的店铺: '{next_shop}'，准备切换")
                         # 发送切换店铺的信号
                         self.request_shop_switch_signal.emit(next_shop)
                     else:
                         print("[邀约日志] 无法找到下一个未在日志中有记录的店铺，强制记录当前店铺并停止邀约")
                         # 强制记录当前店铺并完成邀约
                         self._force_record_current_shop_and_complete("商品列表为空处理-无下一个店铺")
                else:
                    # 商品加载成功且有数据，继续加载达人
                    # 设置等待达人加载完成后启动邀约的标志
                    self.waiting_for_influencers_to_start_invite = True
                    print(f"[Chain Load] 设置 waiting_for_influencers_to_start_invite 标志为 True，达人加载完成后将启动邀约")

                    print("[Chain Load] 调用 _prepare_and_load_influencers 加载达人...")
                    self._prepare_and_load_influencers() # 商品OK，接着加载达人
                    print("[Chain Load] _prepare_and_load_influencers 调用完成。") # <--- 添加调用后日志
            else:
                # 商品加载失败，直接处理失败情况
                print(f"[商品加载] 商品列表加载失败，API返回结果显示加载失败")

                # 记录跳过原因
                skip_reason = "商品列表加载失败" # 定义原因
                print(f"[店铺切换] 检测到店铺 '{self.current_shop_name}' {skip_reason}。准备切换店铺...")

                # 记录跳过信息
                if self.current_shop_name:
                    self.skipped_shops_info[self.current_shop_name] = skip_reason
                    print(f"[Skip Record] 已记录店铺 '{self.current_shop_name}' 跳过，原因: {skip_reason}")

                    # 更新邀约日志中的状态
                    InviteLogDialog.add_log_entry(self.current_shop_name, "商品加载失败", 0)

                # 记录失败的店铺 (如果尚未记录)
                if self.original_shop_hit_limit is None:
                    self.original_shop_hit_limit = self.current_shop_name

                # 获取下一个未在日志中有记录的店铺
                next_shop = self._get_next_shop_name(self.current_shop_name)
                if next_shop:
                    print(f"[邀约日志] 找到下一个未在日志中有记录的店铺: '{next_shop}'，准备切换")
                    # 使用新方法清空达人列表并发送切换店铺信号
                    self._clear_table_and_switch_shop(next_shop)
                else:
                    print("[邀约日志] 无法找到下一个未在日志中有记录的店铺，强制记录当前店铺并停止邀约")
                    # 强制记录当前店铺并完成邀约
                    self._force_record_current_shop_and_complete("商品加载失败-链式加载-无下一个店铺")

                # 设置标志，表示这是自动切换店铺后应该直接开始邀约
                self.is_auto_inviting = True
                # 不设置切换标志，避免循环切换
                # self.switch_shop_requested = True  # 注释掉这行，避免循环切换

                # 不调用_stop_auto_invitation，避免停止邀约流程
                print("[店铺切换] 不调用_stop_auto_invitation，避免停止邀约流程")
                # self._stop_auto_invitation(finished_naturally=False, reason=skip_reason) # 注释掉这行，避免停止邀约流程
                # --- 结束修改 ---
        # --- 结束修改 ---

        # --- 新增: 处理等待达人加载标志 ---
        if hasattr(self, 'waiting_for_influencers_to_start_invite') and self.waiting_for_influencers_to_start_invite:
            print("[Chain Load] 检测到 waiting_for_influencers_to_start_invite 标志位为 True，直接加载达人列表...")
            self.waiting_for_influencers_to_start_invite = False
            # 直接加载达人列表
            self.on_load_influencers_clicked()
        # --- 结束新增 ---

        # --- 移除旧的准备阶段检查 ---
        # (代码已移除)

    # --- 修改: 按顺序显示结果的函数 --- #
    def _display_results(self, results_dict):
        """(修改) 按页码顺序从传入的结果字典中填充表格"""
        print("[UI 主线程] 开始按顺序填充表格...")
        table = self.selected_products_table
        table.setRowCount(0) # 先清空表格

        # --- 新增: 重新设置表头 --- #
        product_headers = ["序号", "商品ID", "商品标题", "价格", "佣金", "店铺"]
        table.setHorizontalHeaderLabels(product_headers)
        print("[UI 主线程] 表头已重新设置。")
        # --- 结束新增 --- #

        if not results_dict:
             print("[UI 主线程] 结果字典为空，无需填充。")
             # 显示一个提示，表示未能加载到任何商品
             item = QTableWidgetItem("未能加载到符合条件的商品数据。")
             item.setTextAlignment(Qt.AlignCenter)
             table.setRowCount(1)
             table.setItem(0, 0, item)
             table.setSpan(0, 0, 1, table.columnCount())
             return # 确保和 if 块内的语句对齐

        # 按页码排序
        page_keys = sorted(results_dict.keys())
        print(f"[UI 主线程] 将要填充的页码: {page_keys}")

        total_products = 0
        for page_num in page_keys:
            products = results_dict.get(page_num)
            if products: # 确保有数据才填充
                print(f"[UI 主线程] 正在填充 page={page_num} 的数据，商品数: {len(products)}...")
                self.populate_product_table(products, page_num)
                total_products += len(products)
            else:
                print(f"[UI 主线程] page={page_num} 数据为空或为None，跳过填充。")

        if total_products > 0:
            print(f"[UI 主线程] 动态加载完成！{len(page_keys)} 页数据按顺序填充完成，总计 {total_products} 个商品。")
            table.scrollToTop() # 滚动到顶部方便查看
        else:
            print("[UI 主线程] 警告：没有填充任何商品数据到表格中")
            # 显示一个提示，表示未能加载到任何商品
            item = QTableWidgetItem("店铺暂无符合条件的商品数据。")
            item.setTextAlignment(Qt.AlignCenter)
            table.setRowCount(1)
            table.setItem(0, 0, item)
            table.setSpan(0, 0, 1, table.columnCount())

    def populate_product_table(self, products_data, page_number):
        """(修改) 用 API 返回的数据实时填充商品表格 - 支持实时追加显示"""
        table = self.selected_products_table

        # 如果是第一页，清空表格并重新设置表头
        if page_number == 1:
            table.setRowCount(0)
            # 重新设置表头
            product_headers = ["序号", "商品ID", "商品标题", "价格", "佣金", "店铺"]
            table.setHorizontalHeaderLabels(product_headers)
            print("[UI] 第1页数据，已清空表格并重新设置表头")

        if not products_data:
            print(f"[UI] 第 {page_number} 页没有符合条件的商品数据传入")
            return # 如果传入空列表，直接返回

        start_row = table.rowCount()
        table.setRowCount(start_row + len(products_data))

        # 计算当前总商品数
        total_products = start_row + len(products_data)
        print(f"[UI] 实时追加第 {page_number} 页的 {len(products_data)} 条商品，当前总计: {total_products} 个商品")

        for row_idx, product in enumerate(products_data):
            current_row = start_row + row_idx

            try:
                # --- 修改: 第一列添加复选框和序号 --- #
                col_idx = 0
                # 创建包含 CheckBox 和 Label 的 Widget
                cell_widget = QWidget()
                checkbox = QCheckBox()
                number_label = QLabel(str(current_row + 1))

                cell_layout = QHBoxLayout(cell_widget) # 直接将布局设置给 widget
                cell_layout.setContentsMargins(0, 0, 0, 0) # 移除边距
                cell_layout.addWidget(checkbox)
                cell_layout.addWidget(number_label)
                cell_layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter) # 序号列左对齐，垂直居中
                cell_widget.setLayout(cell_layout)

                table.setCellWidget(current_row, col_idx, cell_widget)
                col_idx += 1 # 列索引从 1 开始计数后续列
                # --- 结束修改 --- #

                # --- 修改: 使用 'spuId' 填充商品ID列 --- #
                id_value = str(product.get('spuId', 'N/A')) # 使用 spuId 字段
                # --- 结束修改 --- #
                table.setItem(current_row, col_idx, QTableWidgetItem(id_value)); col_idx += 1

                # 商品标题
                title_value = str(product.get('title', 'N/A'))
                title_item = QTableWidgetItem(title_value)
                title_item.setToolTip(title_value)  # 设置悬停提示，显示完整标题
                table.setItem(current_row, col_idx, title_item); col_idx += 1

                # 价格
                price_value = str(product.get('price', 'N/A'))
                price_item = QTableWidgetItem(price_value)
                price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)  # 右对齐
                table.setItem(current_row, col_idx, price_item); col_idx += 1

                # 佣金
                commission_str = str(product.get('commission', 'N/A'))
                commission_item = QTableWidgetItem(f"{commission_str}%")
                commission_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)  # 右对齐
                table.setItem(current_row, col_idx, commission_item); col_idx += 1

                # 店铺
                shop_item = QTableWidgetItem(self.current_shop_name or "")
                table.setItem(current_row, col_idx, shop_item); col_idx += 1

            except Exception as e:
                print(f"[UI] 填充表格行 {current_row} 时出错: {str(e)}")
                # 继续处理下一行，不中断整个过程

    def show_scraping_error(self, error_message):
        """显示抓取错误信息"""
        QMessageBox.warning(self, "商品加载错误", f"加载或抓取商品列表时出错：\n{error_message}")
        # 可以在这里更新状态栏或日志

    def _cleanup_scraper(self):
        """清理抓取用的浏览器资源"""
        print("清理抓取浏览器资源...")

        # --- 新增: 停止检查计时器 --- #
        if self.check_timer and self.check_timer.isActive():
            print("停止元素检查计时器...")
            self.check_timer.stop()
        self.check_timer = None # 清理引用
        self._current_check_js = ""
        self._current_success_callback = None
        # --- 结束新增 --- #

        # --- 修改: 调整清理顺序... (保持之前的修改) --- #
        if self.scraper_page:
            try:
                # 尝试断开所有与 page 相关连接
                self.scraper_page.loadFinished.disconnect(self._handle_page_load)
                # 可能还需要断开 runJavaScript 的回调，但这比较复杂，通常不需要
            except TypeError:
                pass

        if self.scraper_view:
            self.scraper_view.stop()
            self.scraper_view.close() # 先关闭窗口
            self.scraper_view.deleteLater() # 安全删除 View，这应该也会删除 Page
            self.scraper_view = None

        if self.scraper_profile:
             # Profile 的清理比较复杂，如果频繁创建可能需要管理
             # 例如，可以尝试清除缓存和 cookies，但这可能不是必须的，因为 Profile 是临时的
             # self.scraper_profile.clearHttpCache()
             # cookie_store = self.scraper_profile.cookieStore()
             # cookie_store.deleteAllCookies()
             self.scraper_profile = None # 释放引用，让 Python 的垃圾回收处理 Profile 对象
             # 注意：QWebEngineProfile 的底层资源管理可能仍由 Qt 处理
        # --- 结束修改 ---
        print("抓取浏览器资源清理完毕。")

    # --- 新增: 检查循环的辅助方法 ---

    def _start_element_check_loop(self, js_check_function, success_callback):
        """启动一个定时器，轮询检查页面上是否存在由 js_check_function 定义的元素"""
        print(f"开始检查循环，目标回调: {success_callback.__name__}")
        # 先停止可能存在的旧计时器
        if self.check_timer and self.check_timer.isActive():
            self.check_timer.stop()

        self._current_check_js = js_check_function
        self._current_success_callback = success_callback
        self.check_start_time = time.time()

        if not self.check_timer:
            self.check_timer = QTimer(self)
            self.check_timer.timeout.connect(self._check_for_element)

        self.check_timer.start(self.check_interval_ms)
        self._check_for_element() # 立即执行第一次检查

    def _check_for_element(self):
        """执行一次元素检查，并处理超时"""
        if not self.scraper_page:
            print("错误：尝试检查元素时 scraper_page 不存在，停止检查。")
            if self.check_timer and self.check_timer.isActive():
                 self.check_timer.stop()
            return

        # 检查超时
        elapsed_time = (time.time() - self.check_start_time) * 1000 # 转换为毫秒
        if elapsed_time > self.check_timeout_ms:
            print(f"错误：检查元素超时 ({self.check_timeout_ms}ms)，停止检查。")
            if self.check_timer and self.check_timer.isActive():
                self.check_timer.stop()
            self.scraping_error.emit(f"页面元素加载超时 ({self.check_timeout_ms / 1000}秒)")
            self._cleanup_scraper()
            return

        # 执行 JavaScript 检查
        # print(f"执行 JS 检查 (已用时 {int(elapsed_time)}ms)...") # 可以取消注释用于调试
        self.scraper_page.runJavaScript(self._current_check_js, self._handle_element_check_result)

    def _handle_element_check_result(self, result):
        """处理 JavaScript 检查的结果"""
        if not self.scraper_page: # 页面可能在回调返回前被清理
            print("警告：处理元素检查结果时 scraper_page 已不存在。")
            if self.check_timer and self.check_timer.isActive():
                 self.check_timer.stop()
            return

        if result: # 元素已找到
            print("元素检查成功！")
            if self.check_timer and self.check_timer.isActive():
                self.check_timer.stop()
            if self._current_success_callback:
                print(f"调用成功回调: {self._current_success_callback.__name__}")
                self._current_success_callback() # 调用成功后的处理函数
            else:
                print("警告：元素检查成功，但没有设置成功回调函数。")
        else: # 元素未找到，等待下一次 Timer 触发
            # print("元素未找到，等待下一次检查...") # 可以取消注释用于调试
            pass

    # --- 结束新增 ---

    def _fetch_single_page(self, page_num, api_token, biz_magic, cookie_header_string):
        """请求并处理单页商品数据的 API (供并发调用)"""
        # Note: This function runs in a worker thread

        page_size = 20 # 保持每页20条
        offset = (page_num - 1) * page_size

        api_url = "https://store.weixin.qq.com/shop-faas/mmchannelstradeleague/plan/getItemListBySearch"
        params = {
            "token": api_token,
            "lang": "zh_CN",
            "offset": offset,
            "size": page_size,
            "product_title": "",
            "status": 1,
            "list_type": 2,
            "need_total": 0,
            "needWarning": "true",
            "needStockNum": "true"
        }

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'biz_magic': biz_magic,
            'Cookie': cookie_header_string,
            'Referer': 'https://store.weixin.qq.com/shop/shopleague/home',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
             'Accept-Encoding': 'gzip, deflate, br, zstd',
             'Accept-Language': 'zh-CN,zh;q=0.9',
             'Connection': 'keep-alive',
             'Host': 'store.weixin.qq.com',
             'potter-scene': 'weixinShop',
             'Sec-Fetch-Dest': 'empty',
             'Sec-Fetch-Mode': 'cors',
             'Sec-Fetch-Site': 'same-origin',
             'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
             'sec-ch-ua-mobile': '?0',
             'sec-ch-ua-platform': '"Windows"',
        }

        try:
            print(f"[Thread-{threading.current_thread().name}] 请求 API: page={page_num}, offset={offset}")
            print(f"[Thread-{threading.current_thread().name}] 请求URL: {api_url}")
            print(f"[Thread-{threading.current_thread().name}] 请求参数: {params}")

            # 使用send_request_with_cookie函数替代直接的requests.get调用
            data = send_request_with_cookie(
                url=api_url,
                cookie_input=cookie_header_string,
                method='GET',
                params=params,
                extra_headers=headers,
                timeout=25
            )

            print(f"[Thread-{threading.current_thread().name}] API响应类型: {type(data)}")
            if isinstance(data, dict):
                print(f"[Thread-{threading.current_thread().name}] API响应code: {data.get('code', 'N/A')}")
                if 'itemList' in data:
                    print(f"[Thread-{threading.current_thread().name}] itemList长度: {len(data.get('itemList', []))}")
                else:
                    print(f"[Thread-{threading.current_thread().name}] 响应中没有itemList字段")
                    print(f"[Thread-{threading.current_thread().name}] 响应字段: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
            else:
                print(f"[Thread-{threading.current_thread().name}] 非字典响应: {str(data)[:200]}")

            # 检查返回的数据是否包含错误码
            if isinstance(data, dict) and data.get('code') == -1001:
                print(f"[Thread-{threading.current_thread().name}] 检测到API错误: {data.get('msg', '未知错误')}")
                return page_num, None # 返回 None 表示失败

            # 正常处理API返回数据
            if isinstance(data, dict) and data.get('code') == 0:
                items = data.get('itemList', [])
                print(f"[Thread-{threading.current_thread().name}] page={page_num} 成功获取 {len(items)} 条原始数据")

                products_this_page = []
                for item in items:
                    product_info = item.get('productInfo', {})
                    price_value = product_info.get('price', 0)
                    commission_value = item.get('ratio', 0)

                    # 确保价格和佣金是数字类型
                    try:
                        price = float(price_value) / 100 if price_value else 0
                        commission = float(commission_value) / 10000 if commission_value else 0
                    except (TypeError, ValueError):
                        price = 0
                        commission = 0

                    # --- 新增: 提取 spuId --- #
                    spu_id = str(item.get('spuId', '')) # 直接从 item 获取 spuId
                    # --- 结束新增 --- #

                    product = {
                        'id': str(item.get('id', '')), # 这个可能是 productId 或内部 ID
                        'spuId': spu_id, # 添加 spuId 字段
                        'title': product_info.get('title', '未知商品'),
                        'price': f"{price:.2f}",
                        'commission': f"{commission:.2f}",
                    }
                    products_this_page.append(product)

                print(f"[Thread-{threading.current_thread().name}] page={page_num} 处理后数据: {len(products_this_page)} 条")
                return page_num, products_this_page # 返回页码和处理后的商品列表
            else:
                error_msg = data.get('msg', '未知 API 错误') if isinstance(data, dict) else str(data)
                print(f"[Thread-{threading.current_thread().name}] page={page_num} API 返回错误: {error_msg}")
                return page_num, None

        except Exception as e:
            print(f"[Thread-{threading.current_thread().name}] page={page_num} 处理时发生未知错误: {e}")
            return page_num, None

    # --- 新增: 加载达人相关方法 ---
    def on_load_influencers_clicked(self):
        """处理"加载达人"按钮点击事件 - 现在会并发加载多页"""
        print("[UI] 加载达人按钮被点击") # 添加日志
        if self.loading_influencers:
            print("[UI] 正在加载达人数据，请稍候...")
            return

        if not self.current_shop_name:
            QMessageBox.warning(self, "提示", "请先在顶部菜单栏选择一个店铺")
            return

        # --- 修改: 优先使用current_influencer_page，如果它已经被设置为1（自动调整页码时） --- #
        if hasattr(self, 'current_influencer_page') and self.current_influencer_page == 1:
            start_page = 1
            print(f"[UI] 检测到current_influencer_page已设置为1，使用该值作为起始页码")
        else:
            # 从UI读取起始页码
            try:
                start_page = int(self.start_page_input.text())
                if start_page <= 0: start_page = 1 # 保证页码至少为1
            except ValueError:
                start_page = 1 # 如果输入无效，默认从第1页开始
                self.start_page_input.setText("1") # 同时更新UI显示
        print(f"[UI] 获取到起始页码: {start_page}")
        # --- 结束修改 --- #

        # --- 修改: 每次只加载 1 页 --- #
        num_pages_to_fetch = 1 # 设定每次加载 1 页
        print(f"[UI] 设定加载页数: {num_pages_to_fetch}")
        # --- 结束修改 --- #

        # 使用ShopManager获取 Cookie 和认证信息
        shop_info = ShopManager.get_shop_by_name(self.current_shop_name)
        if not shop_info:
            QMessageBox.warning(self, "错误", f"未找到店铺 '{self.current_shop_name}' 的信息。")
            return

        cookies_list = shop_info.get('cookies_list', [])
        if not cookies_list:
             QMessageBox.warning(self, "错误", f"未能获取店铺 '{self.current_shop_name}' 的 Cookie。")
             return
        cookie_dict = {c['name']: c['value'] for c in cookies_list}
        biz_token = cookie_dict.get('biz_token')
        biz_magic = cookie_dict.get('biz_magic')
        if not biz_token or not biz_magic:
             QMessageBox.warning(self, "错误", f"从 Cookie 中未能提取必要的认证参数。")
             return

        # --- 修改: 收集所有勾选的类目 ID --- #
        category_ids = []
        print("[UI] 正在收集勾选的类目ID...")
        if hasattr(self, 'category_checkboxes') and self.category_checkboxes:
            for checkbox in self.category_checkboxes:
                if checkbox.isChecked():
                    category_name = checkbox.text()
                    cat_id = self.category_id_map.get(category_name)
                    if cat_id:
                        category_ids.append(cat_id)
                        print(f"  - 已勾选: {category_name} (ID: {cat_id})")
                    else:
                        print(f"  - 警告: 找到已勾选类目 '{category_name}' 但未在映射中找到ID。")
        else:
            print("[UI] 警告: category_checkboxes 未初始化或为空。")

        if not category_ids:
            print("[UI] 未选择任何有效类目，将加载所有类目达人")
        # --- 结束修改 --- #

        # 设置加载状态
        self.loading_influencers = True
        # 重置达人加载停止标志
        self.influencer_loading_stop_flag = False
        print("[达人加载] 已重置达人加载停止标志")

        self.add_to_list_btn.setEnabled(False) # 禁用加载按钮
        self.prev_page_btn.setEnabled(False) # --- 确保加载时禁用下一页按钮 ---
        self.add_to_list_btn.setText("加载中...") # 更新按钮文本

        # --- 新增: 重置断点续传索引 --- #
        # self.auto_invite_resume_index = 0
        # --- 结束新增 --- #

        print(f"[UI] 开始并发加载达人数据，起始页: {start_page}，共 {num_pages_to_fetch} 页")
        QApplication.processEvents() # 刷新 UI

        # 启动后台线程获取数据
        try:
            # 打包认证和筛选信息
            auth_info = {
                'api_token': biz_token,
                'biz_magic': biz_magic,
                'cookie_input': cookies_list
            }
            filter_info = {
                'category_ids': category_ids
                # 未来可以添加更多筛选条件
            }

            worker_thread = threading.Thread(
                target=self._concurrent_fetch_influencers_worker,
                args=(num_pages_to_fetch, start_page, auth_info, filter_info),
                daemon=True
            )
            worker_thread.start()
        except Exception as e:
            print(f"[UI] 启动达人加载线程时出错: {str(e)}")
            # --- 修复: 恢复加载按钮，而不是下一页按钮 --- #
            self.loading_influencers = False
            self.add_to_list_btn.setEnabled(True)
            self.add_to_list_btn.setText("加载达人")
            self.prev_page_btn.setEnabled(False) # 初始加载出错，下一页保持禁用
            self.table.setRowCount(0) # 清空表格，可能显示错误
            QMessageBox.warning(self, "错误", f"启动达人数据获取任务失败: {str(e)}")
            # --- 结束修复 --- #

    def _fetch_influencer_page(self, page_num, category_ids, biz_token, biz_magic, cookie_input, random_seed):
        """后台线程函数：请求并处理单页达人数据 (现在接收 random_seed)"""
        print(f"[WorkerThread-Influencer] 开始获取第 {page_num} 页达人数据... (使用种子: {random_seed})")
        limit = 20
        offset = (page_num - 1) * limit
        # random_seed = random.randint(1000, 9999) # 不再内部生成

        # 确保category_ids是列表格式
        cat_ids = category_ids if isinstance(category_ids, list) else [category_ids]
        if not cat_ids:
            cat_ids = []

        # 构造统计数据请求参数
        stats_url = "https://store.weixin.qq.com/shop-faas/statistic/cgi/mmdata?token=&lang=zh_CN"

        current_time = int(time.time() * 1000)  # 当前时间戳（毫秒）
        stats_payload = {
            "data": {
                "12": "deviceModel",
                "13": "deviceBrand",
                "14": "osName",
                "15": "osVersion",
                "16": "languageVersion",
                "17": "startTime",
                "18": "endTime",
                "19": "count",
                "21": "appid",
                "22": "spuid",
                "23": "businessid",
                "24": "actionTime",
                "25": "actionType",
                "26": "pageid",
                "27": "appSessionid",
                "28": "pageInfo",
                "29": "eleid",
                "30": "eleInfo",
                "31": "actionInfo",
                "32": "finderusername",
                "foo": "bar"
            },
            "deviceModel": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "deviceBrand": "Chromium",
            "osName": "Windows",
            "osVersion": "",
            "languageVersion": "zh-CN",
            "startTime": 0,
            "endTime": 0,
            "count": 1,
            "appid": "",
            "spuid": "",
            "businessid": 3,
            "actionTime": current_time,
            "actionType": "element_click",
            "pageid": "daren_plaza",
            "appSessionid": "",
            "pageInfo": "",
            "eleid": "daihuo_cate",
            "eleInfo": json.dumps({"topCatId": cat_ids}),
            "actionInfo": "",
            "finderusername": ""
        }

        # 构造达人列表请求参数
        api_url = "https://store.weixin.qq.com/shop-faas/mmchannelstradeleague/finderSquare/cgi/getSquareTalentList?token=&lang=zh_CN"

        # 构造达人列表请求体
        influencer_payload = {
            "filter": {
                "isLiveFinder": 0,
                "isVideoFinder": 0,
                "isMpPromoter": 0,
                "limit": limit,
                "offset": offset,
                # "topCatId": cat_ids, # 将移动到下面处理
                "randomSeed": random_seed,
                "sortKey": 0,
                "sortDirection": 1,
                # "topCatIdList": cat_ids # 将移动到下面处理
            }
        }

        # --- 处理类目 ID --- #
        if cat_ids:
            influencer_payload["filter"]["topCatId"] = cat_ids
            influencer_payload["filter"]["topCatIdList"] = cat_ids
            print(f"[WorkerThread-Influencer] 添加类目筛选: {cat_ids}")
        else:
            # 根据新的严格要求，如果未选择，可能不需要传空数组，或者需要传空数组？
            # 暂时遵循旧逻辑，传递空数组，如果 API 报错再调整
            influencer_payload["filter"]["topCatId"] = []
            influencer_payload["filter"]["topCatIdList"] = []
            print("[WorkerThread-Influencer] 未指定类目筛选，传递空数组")

        # --- 新增: 处理粉丝数区间 --- #
        fans_interval = [] # 默认空列表
        if self.fans_unlimited.isChecked():
            print("[WorkerThread-Influencer] 添加粉丝数筛选: 不限 ([])")
            fans_interval = [] # 不限 -> 空列表
        elif self.fans_less_1w.isChecked():
            fans_interval = [1] # 1w以下 对应 [1]
            print("[WorkerThread-Influencer] 添加粉丝数筛选: 1w以下 ([1])")
        elif self.fans_1w_10w.isChecked():
            fans_interval = [2] # 1w-10w 对应 [2]
            print("[WorkerThread-Influencer] 添加粉丝数筛选: 1w-10w ([2])")
        elif self.fans_10w_50w.isChecked():
            fans_interval = [3] # 10w-50w 对应 [3]
            print("[WorkerThread-Influencer] 添加粉丝数筛选: 10w-50w ([3])")
        elif self.fans_more_50w.isChecked():
            fans_interval = [4] # 50w以上 对应 [4]
            print("[WorkerThread-Influencer] 添加粉丝数筛选: 50w以上 ([4])")
        # else: # 理论上不应发生
        #      print("[WorkerThread-Influencer] 警告: 未选中任何粉丝数选项?")

        # 总是添加 fansNumberInterval 键，值为列表 (可能为空)
        influencer_payload["filter"]["fansNumberInterval"] = fans_interval
        # --- 结束修改 --- #

        # --- 新增: 处理销售额区间 --- #
        gmv_interval = [] # 默认空列表
        if self.sales_unlimited.isChecked():
            print("[WorkerThread-Influencer] 添加销售额筛选: 不限 ([])")
            gmv_interval = [] # 不限 -> 空列表
        elif self.sales_less_10w.isChecked():
            gmv_interval = [1]
            print("[WorkerThread-Influencer] 添加销售额筛选: 10w以下 ([1])")
        elif self.sales_10w_50w.isChecked():
            gmv_interval = [2]
            print("[WorkerThread-Influencer] 添加销售额筛选: 10w-50w ([2])")
        elif self.sales_50w_100w.isChecked(): # 使用用户新增的变量名
            gmv_interval = [3]
            print("[WorkerThread-Influencer] 添加销售额筛选: 50w-100w ([3])")
        elif self.sales_more_100w.isChecked(): # 使用用户新增的变量名
            gmv_interval = [4]
            print("[WorkerThread-Influencer] 添加销售额筛选: 100w以上 ([4])")
        # else:
        #      print("[WorkerThread-Influencer] 警告: 未选中任何销售额选项?")

        # 总是添加 gmvInterval 键，值为列表 (可能为空)
        influencer_payload["filter"]["gmvInterval"] = gmv_interval
        # --- 结束修改 --- #

        # --- 新增: 处理场均销售额区间 --- #
        live_gmv_interval = [] # 默认空列表
        if self.avg_sales_unlimited.isChecked():
            print("[WorkerThread-Influencer] 添加场均销售额筛选: 不限 ([])")
            live_gmv_interval = [] # 不限 -> 空列表
        elif self.avg_sales_less_1w.isChecked():
            live_gmv_interval = [1]
            print("[WorkerThread-Influencer] 添加场均销售额筛选: 1w以下 ([1])")
        elif self.avg_sales_1w_10w.isChecked():
            live_gmv_interval = [2]
            print("[WorkerThread-Influencer] 添加场均销售额筛选: 1w-10w ([2])")
        elif self.avg_sales_more_10w.isChecked():
            live_gmv_interval = [3]
            print("[WorkerThread-Influencer] 添加场均销售额筛选: 10w以上 ([3])")
        # else:
        #     print("[WorkerThread-Influencer] 警告: 未选中任何场均销售额选项?")

        # 总是添加 liveGmvInterval 键，值为列表 (可能为空)
        influencer_payload["filter"]["liveGmvInterval"] = live_gmv_interval
        # --- 结束修改 --- #

        # --- 新增: 处理客单价区间 --- #
        price_interval = [] # 默认空列表
        if self.price_unlimited.isChecked():
            print("[WorkerThread-Influencer] 添加客单价筛选: 不限 ([])")
            price_interval = [] # 不限 -> 空列表
        elif self.price_less_100.isChecked():
            price_interval = [1]
            print("[WorkerThread-Influencer] 添加客单价筛选: 100元下 ([1])")
        elif self.price_100_200.isChecked():
            price_interval = [2]
            print("[WorkerThread-Influencer] 添加客单价筛选: 100-200元 ([2])")
        elif self.price_more_200.isChecked():
            price_interval = [3]
            print("[WorkerThread-Influencer] 添加客单价筛选: 200元以上 ([3])")
        # elif self.price_unlimited.isChecked(): # 已在开头处理
        #     price_interval = [0]
        #     print("[WorkerThread-Influencer] 添加客单价筛选: 不限 ([0])")
        # else:
        #     print("[WorkerThread-Influencer] 警告: 未选中任何客单价选项?")

        # 总是添加 perUvPriceInterval 键，值为列表 (可能为空)
        influencer_payload["filter"]["perUvPriceInterval"] = price_interval
        # --- 结束修改 --- #

        # --- 新增: 处理粉丝年龄区间 --- #
        age_interval = [] # 默认空列表
        if self.age_unlimited.isChecked():
            print("[WorkerThread-Influencer] 添加粉丝年龄筛选: 不限 ([])")
            age_interval = [] # 不限 -> 空列表
        elif self.age_less_18.isChecked():
            age_interval = [1]
            print("[WorkerThread-Influencer] 添加粉丝年龄筛选: 18岁以下 ([1])")
        elif self.age_18_24.isChecked():
            age_interval = [2]
            print("[WorkerThread-Influencer] 添加粉丝年龄筛选: 18-24岁 ([2])")
        elif self.age_25_39.isChecked():
            age_interval = [3]
            print("[WorkerThread-Influencer] 添加粉丝年龄筛选: 25-39岁 ([3])")
        elif self.age_40_49.isChecked():
            age_interval = [4]
            print("[WorkerThread-Influencer] 添加粉丝年龄筛选: 40-49岁 ([4])")
        elif self.age_more_50.isChecked():
            age_interval = [5]
            print("[WorkerThread-Influencer] 添加粉丝年龄筛选: 50岁以上 ([5])")
        # elif self.age_unlimited.isChecked(): # 已在开头处理
        #     age_interval = [0]
        #     print("[WorkerThread-Influencer] 添加粉丝年龄筛选: 不限 ([0])")
        # else:
        #     print("[WorkerThread-Influencer] 警告: 未选中任何粉丝年龄选项?")

        # 总是添加 fansFeatureAge 键，值为列表 (可能为空)
        influencer_payload["filter"]["fansFeatureAge"] = age_interval
        # --- 结束修改 --- #

        # --- 修改: 处理达人性别 --- #
        selected_gender = self.gender_combo.currentText()
        fans_sex_list = [] # 默认空列表
        if selected_gender == "不限":
            print("[WorkerThread-Influencer] 添加达人性别筛选: 不限 ([])")
            fans_sex_list = [] # 不限 -> 空列表
        elif selected_gender == "男":
            fans_sex_list = [1]
            print("[WorkerThread-Influencer] 添加达人性别筛选: 男 ([1])")
        elif selected_gender == "女":
            fans_sex_list = [2]
            print("[WorkerThread-Influencer] 添加达人性别筛选: 女 ([2])")

        # 总是添加 fansFeatureSex 键，值为列表 (可能为空)
        influencer_payload["filter"]["fansFeatureSex"] = fans_sex_list
        # --- 结束修改 --- #

        # --- 新增: 处理粉丝地域 (如果需要) --- #
        # selected_region = self.region_combo.currentText()

        # 请求头
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'biz_magic': biz_magic,
            'Content-Type': 'application/json',
            'Origin': 'https://store.weixin.qq.com',
            'Referer': 'https://store.weixin.qq.com/shop/findersquare/find',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Sec-Fetch-Site': 'same-origin'
        }

        # --- 新增: 打印最终的 influencer_payload --- #
        print("--- 准备发送达人列表请求 --- Payload: ---")
        try:
            print(json.dumps(influencer_payload, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"无法打印 payload: {e}")
        print("------------------------------------------")
        # --- 结束新增 --- #

        # --- 新增: 详细日志记录请求参数 --- #
        # print(f"[WorkerThread-Influencer][Page {page_num}] 准备发送第二次请求 (获取达人列表)")
        # print(f"  URL: {api_url}")
        # print(f"  Payload: {json.dumps(influencer_payload, indent=2)}")
        # print(f"  Headers: {json.dumps(headers, indent=2)}") # 注意: Cookie在headers中，但也会被send_request_with_cookie处理
        # --- 结束新增 --- #

        # 创建两个线程同时发送请求
        stats_response = None
        influencer_response = None
        stats_error = None
        influencer_error = None

        def fetch_stats():
            nonlocal stats_response, stats_error
            try:
                print(f"[WorkerThread-Stats] 发送统计数据请求到 {stats_url}")
                stats_response = send_request_with_cookie(
                    url=stats_url,
                    cookie_input=cookie_input,
                    method='POST',
                    data=stats_payload,
                    extra_headers=headers,
                    timeout=20
                )
                if isinstance(stats_response, dict) and stats_response.get('ret') == 0:
                    print(f"[WorkerThread-Stats] 统计数据请求成功: {stats_response}")
                else:
                    stats_error = f"统计数据请求未返回预期结果: {stats_response}"
                    print(f"[WorkerThread-Stats] {stats_error}")
            except Exception as e:
                stats_error = f"统计数据请求出错: {str(e)}"
                print(f"[WorkerThread-Stats] {stats_error}")

        def fetch_influencers():
            nonlocal influencer_response, influencer_error
            try:
                print(f"[WorkerThread-Influencer] 发送达人列表请求到 {api_url}")
                influencer_response = send_request_with_cookie(
                    url=api_url,
                    cookie_input=cookie_input,
                    method='POST',
                    data=influencer_payload,
                    extra_headers=headers,
                    timeout=20
                )
            except Exception as e:
                influencer_error = f"处理达人数据时发生错误: {str(e)}"
                print(f"[WorkerThread-Influencer] {influencer_error}")
                # 不再在这里检查403错误，因为send_request_with_cookie函数已经处理了这种情况

        # 创建并启动线程
        stats_thread = threading.Thread(target=fetch_stats)
        influencer_thread = threading.Thread(target=fetch_influencers)

        stats_thread.start()
        influencer_thread.start()

        # 等待两个线程完成
        stats_thread.join()
        influencer_thread.join()

        print("[WorkerThread] 两个请求都已完成")

        # 检查返回的数据是否包含错误码
        if isinstance(influencer_response, dict) and influencer_response.get('code') == -1001:
            print(f"[WorkerThread-Influencer] 检测到API错误: {influencer_response.get('msg', '未知错误')}")
            # 返回特定的错误信息
            return page_num, {"error": True, "msg": influencer_response.get('msg', '未知错误')}

        # 检查统计请求结果
        if stats_error:
            print(f"[WorkerThread] 统计请求出错: {stats_error}")
            # 即使统计请求失败，我们仍继续处理达人列表结果

        # 处理达人列表结果
        influencer_list = []
        total_count = 0
        error_message = influencer_error
        api_success = False

        if not influencer_error and influencer_response:
            # 解析响应
            if isinstance(influencer_response, dict):
                # 打印API响应（限制长度避免日志过大）
                response_str = json.dumps(influencer_response)
                print(f"[WorkerThread-Influencer] API 响应: {response_str[:500]}..." if len(response_str) > 500 else f"[WorkerThread-Influencer] API 响应: {response_str}")

                if influencer_response.get('code') == 0:
                    api_success = True
                    # 从'list'字没段获取达人列表
                    raw_list = influencer_response.get('list', [])
                    # 如果API返回了total字段则使用，否则使用列表长度
                    total_count = influencer_response.get('total', len(raw_list))
                    print(f"[WorkerThread-Influencer] API 成功返回，原始列表长度: {len(raw_list)}, 总数: {total_count}")

                    # --- 新增: 打印前 3 条原始达人数据用于调试 --- #
                    if raw_list:
                        print("--- 原始达人数据示例 (前3条) ---")
                        for i, dbg_item in enumerate(raw_list[:3]):
                            print(f"记录 {i+1}: {json.dumps(dbg_item, ensure_ascii=False, indent=2)}")
                        print("--- 结束原始数据示例 ---")
                    # --- 结束新增 --- #

                    # 提取需要的信息
                    for item in raw_list:
                        # finder_info = item.get('finderInfo', {}) # 不再需要单独提取 finderInfo
                        influencer_id = 'N/A' # Default to N/A

                        # --- 修改: 使用递归函数查找 finderUsername --- #
                        found_username = find_first_valid_value(item, 'finderUsername')
                        if found_username:
                            influencer_id = found_username
                            # print(f"[DEBUG] Recursive search found ID: '{influencer_id}'") # 可选调试日志
                        else:
                            print(f"[WorkerThread-Influencer] 警告: 在记录中递归未找到有效的 finderUsername: {item}")
                        # --- 结束修改 --- #

                        # --- 最终 ID 确认 (现在直接使用 influencer_id) --- #
                        print(f"[DEBUG] Final ID being used for table: '{influencer_id}'")

                        # --- 其他字段提取逻辑保持不变，但注意可能也需要递归查找？(暂时只改ID) --- #
                        finder_info = item.get('finderInfo', {}) # 仍然获取 finderInfo 以便提取其他字段
                        nickname = finder_info.get('nickname', 'N/A') # 暂时保持原样
                        head_img = finder_info.get('headImg', '') # 暂时保持原样
                        fans_count = finder_info.get('fansNumber', '未知') # 暂时保持原样
                        is_certified = finder_info.get('officialCertification', 0) == 1 # 暂时保持原样
                        status = "已认证" if is_certified else "未认证"
                        top_cat_list = finder_info.get('topCatList', []) # 暂时保持原样
                        main_category = "未分类"
                        if top_cat_list and len(top_cat_list) > 0:
                            main_category = top_cat_list[0].get('topCatName', '未分类')

                        # --- 新增: 提取销售额、客单价、场均销售额、粉丝年龄 --- #
                        # --- 修改: 使用递归查找 --- #
                        sales_gmv = find_first_valid_value(item, 'gmv') or 'N/A'
                        avg_price = find_first_valid_value(item, 'perUvPrice') or 'N/A'
                        avg_live_gmv = find_first_valid_value(item, 'liveGmv') or 'N/A'
                        print(f"[Recursive Search] gmv={sales_gmv}, perUvPrice={avg_price}, liveGmv={avg_live_gmv}") # 调试日志
                        # --- 结束修改 --- #
                        # --- 修改: 移除粉丝年龄的提取 --- #
                        # fans_age_range = item.get('fansFeatureAge', 'N/A') # 粉丝年龄 (尝试使用 fansFeatureAge)
                        # --- 结束修改 --- #
                        # --- 结束新增 --- #

                        influencer_list.append({
                            "id": influencer_id, # <--- 确保这里使用的是 influencer_id
                            "nickname": nickname,
                            "avatar": head_img,
                            "fans": fans_count,
                            "status": status, # Status is calculated but not used in the current populate_influencer_table
                            "category": main_category, # Category is extracted but not used
                            # --- 新增: 将提取的数据添加到字典 --- #
                            "gmv": sales_gmv,
                            "perUvPrice": avg_price,
                            "liveGmv": avg_live_gmv,
                            # --- 修改: 移除粉丝年龄 --- #
                            # "fansAge": fans_age_range # 使用 fansAge 作为内部 key
                            # --- 结束修改 --- #
                            # --- 结束新增 --- #
                        })
                    print(f"[WorkerThread-Influencer] 处理后达人列表长度: {len(influencer_list)}")
                else:
                    error_message = f"API 返回错误 code: {influencer_response.get('code')}, msg: {influencer_response.get('msg', '未知错误')}"
                    print(f"[WorkerThread-Influencer] {error_message}")
            elif isinstance(influencer_response, str):
                # 如果返回的是字符串，可能是 HTML 或其他错误
                error_message = f"请求失败，服务器返回非 JSON 数据: {influencer_response[:200]}..." # 显示部分内容
                print(f"[WorkerThread-Influencer] {error_message}")
            else:
                error_message = f"请求返回了未知类型的数据: {type(influencer_response)}"
                print(f"[WorkerThread-Influencer] {error_message}")

        # --- 修改: 总是返回结果元组，不再发射信号 --- #
        print(f"[WorkerThread-Influencer] page={page_num} 完成，准备返回结果 (Success: {api_success}, Count: {len(influencer_list) if influencer_list else 0}, Total: {total_count})")
        return influencer_list, page_num, total_count, api_success
        # --- 结束修改 --- #

    # --- 移除旧的单页处理槽 --- #
    # def _handle_influencer_load_result(self, influencer_list, page_num, total_count, success):
    #     ...

    # +++ 新增: 批量处理达人加载结果的槽函数 +++
    def _handle_batch_influencer_load_result(self, combined_list, last_page_num, total_count, overall_success):
        """槽函数：在主线程处理批量加载到的达人数据"""
        print(f"[UI 主线程] 收到批量达人加载结果 - 截止页码: {last_page_num}, 总数量: {len(combined_list)}, API总计: {total_count}, 成功: {overall_success}")

        # 恢复 UI 状态
        self.loading_influencers = False
        self.add_to_list_btn.setEnabled(True)
        self.add_to_list_btn.setText("加载达人")

        # 设置页面加载事件，通知等待的线程
        if hasattr(self, 'page_load_event') and not self.page_load_event.is_set():
            print("[UI 主线程] 设置页面加载事件，通知等待的线程")
            self.page_load_event.set()

        # 特别处理单店邀约模式的翻页事件
        if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
            print("[UI 主线程] 单店邀约模式：确保页面加载事件已设置")
            if hasattr(self, 'page_load_event'):
                self.page_load_event.set()

        # 检查是否在自动邀约过程中，如果是且加载失败，则先尝试切换粉丝量区间，如果所有区间都试过了再切换店铺
        if not overall_success and hasattr(self, 'is_auto_inviting') and self.is_auto_inviting:
            print("[UI 主线程] 自动邀约过程中检测到达人数据加载失败")

            # 先尝试切换粉丝量区间重新加载
            if self._try_switch_fans_interval_and_reload():
                print("[UI 主线程] 已切换粉丝量区间，重新加载达人数据")
                return

            # 如果无法切换粉丝量区间，则切换到下一个店铺
            print("[UI 主线程] 所有粉丝量区间都已尝试，切换到下一个店铺")

            # 记录店铺状态为"加载失败"
            if self.current_shop_name:
                # 更新邀约日志中的状态
                InviteLogDialog.add_log_entry(self.current_shop_name, "加载失败", 0)

                # 记录跳过信息
                skip_reason = "加载失败"
                self.skipped_shops_info[self.current_shop_name] = skip_reason
                print(f"[Skip Record] 已记录店铺 '{self.current_shop_name}' 跳过，原因: {skip_reason}")

                # 先记录当前店铺的失败状态
                print(f"[达人加载失败] 先记录当前店铺 '{self.current_shop_name}' 的失败状态")
                self._record_current_shop_status("达人加载失败")

                # 然后获取下一个未在日志中有记录的店铺
                next_shop = self._get_next_shop_name(self.current_shop_name)
                if next_shop:
                    print(f"[店铺切换] 找到下一个店铺: '{next_shop}'，准备切换")
                    # 设置切换标志
                    self.switch_shop_requested = True
                    # 使用新方法清空达人列表并发送切换店铺信号
                    self._clear_table_and_switch_shop(next_shop)

                    # 设置page_load_event，让自动邀约线程继续执行
                    print("[主线程] 达人加载失败，设置 page_load_event。")
                    self.page_load_event.set()

                    # 直接返回，不再执行后续代码
                    return
                else:
                    print("[店铺切换] 无法找到下一个店铺，所有店铺已完成")
                    # 所有店铺已完成
                    self._force_record_current_shop_and_complete("达人加载失败处理-所有店铺已完成")

                    # 设置page_load_event，让自动邀约线程继续执行
                    print("[主线程] 达人加载失败，设置 page_load_event。")
                    self.page_load_event.set()

                    # 直接返回，不再执行后续代码
                    return

        if not overall_success:
            # 加载失败时的处理
            self.table.setRowCount(0)
            item = QTableWidgetItem(f"加载第 {last_page_num} 页达人数据失败。")
            item.setTextAlignment(Qt.AlignCenter)
            self.table.setRowCount(1)
            self.table.setItem(0, 0, item)
            self.table.setSpan(0, 0, 1, self.table.columnCount())
            self.prev_page_btn.setEnabled(False)
            # --- 修改: 加载失败也要设置事件 --- #
            print("[主线程] 达人加载失败，设置 page_load_event。")
            self.page_load_event.set()

            # 特别处理单店邀约模式的翻页事件
            if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
                print("[主线程] 单店邀约模式：达人加载失败，确保页面加载事件已设置")
                self.page_load_event.set()
            # --- 结束修改 --- #
            # --- 移除弹框，避免打断自动流程 --- #
            # QMessageBox.warning(self, "加载失败", f"加载第 {last_page_num} 页达人数据时出错。")
            # --- 结束移除 --- #
            return

        # 加载成功时的处理
        self.current_influencer_page = last_page_num
        self.page_input.setText(str(last_page_num))
        # --- 修改: 只有在非待处理情况才更新总数 --- #
        # self.total_influencers = total_count

        # +++ 新增: 处理"待商家处理"情况 +++
        if overall_success and not combined_list and total_count > 0:
            print(f"[UI] 检测到特殊情况: 加载成功，列表为空，但API总数 > 0 ({total_count})。可能为'待商家处理'。")
            self.table.setRowCount(0) # 清空表格
            item = QTableWidgetItem(f"加载成功，列表为空 (可能待商家处理)。API 报告总数: {total_count}")
            item.setTextAlignment(Qt.AlignCenter)
            self.table.setRowCount(1)
            self.table.setItem(0, 0, item)
            self.table.setSpan(0, 0, 1, self.table.columnCount())
            self.prev_page_btn.setEnabled(False) # 禁用下一页
            self.total_influencers = 0 # 实际加载到的为 0

            # 检查是否在自动邀约过程中，如果是则切换到下一个店铺
            if hasattr(self, 'is_auto_inviting') and self.is_auto_inviting:
                print("[UI 主线程] 自动邀约过程中检测到达人列表为空")

                # 记录店铺状态为"达人列表为空"
                if self.current_shop_name:
                    # 更新邀约日志中的状态
                    InviteLogDialog.add_log_entry(self.current_shop_name, "达人列表为空", 0)

                    # 记录跳过信息
                    skip_reason = "达人列表为空"
                    self.skipped_shops_info[self.current_shop_name] = skip_reason
                    print(f"[Skip Record] 已记录店铺 '{self.current_shop_name}' 跳过，原因: {skip_reason}")

                    # 获取下一个未在日志中有记录的店铺
                    next_shop = self._get_next_shop_name(self.current_shop_name)
                    if next_shop:
                        print(f"[店铺切换] 找到下一个店铺: '{next_shop}'，准备切换")
                        # 设置切换标志
                        self.switch_shop_requested = True
                        # 使用新方法清空达人列表并发送切换店铺信号
                        self._clear_table_and_switch_shop(next_shop)

                        # 设置page_load_event，让自动邀约线程继续执行
                        print("[主线程] 达人列表为空，设置 page_load_event。")
                        self.page_load_event.set()

                        # 直接返回，不再执行后续代码
                        return
                    else:
                        print("[店铺切换] 无法找到下一个店铺，强制记录当前店铺并停止邀约")
                        # 强制记录当前店铺并完成邀约
                        self._force_record_current_shop_and_complete("达人列表为空处理-无下一个店铺")

            print("[主线程] 特殊情况处理完成，设置 page_load_event。")
            self.page_load_event.set()

            # 特别处理单店邀约模式的翻页事件
            if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
                print("[主线程] 单店邀约模式：特殊情况处理完成，确保页面加载事件已设置")
                self.page_load_event.set()

            # --- 移除旧的准备阶段检查 ---
            # (准备阶段检查已移到函数末尾，此处无需重复)
            return # 处理完毕，直接返回
        # +++ 结束新增 +++
        else:
            # --- 只有在非待处理情况下才设置总数和填充表格 --- #
            self.total_influencers = total_count
            # 填充表格
            self.populate_influencer_table(combined_list)
        # --- 结束修改 ---

        # --- 修改: 根据当前页数量判断是否禁用下一页 (移到 populate_influencer_table 之后) --- #
        limit = 20 # 每页数量
        has_more = False # 先假设禁用

        # --- 修改: 使用 combined_list 判断数量 --- #
        if overall_success and combined_list is not None:
            current_page_count = len(combined_list)
        # --- 结束修改 --- #
            print(f"[UI] 当前页加载成功，返回 {current_page_count} 条数据。")

            # 对于单店邀约模式，使用更宽松的翻页条件
            if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
                # 单店邀约模式：只要返回数据等于limit，就认为可能还有下一页
                if current_page_count == limit:
                    has_more = True
                    print(f"[UI] 单店邀约模式：当前页返回 {current_page_count} 条（满页），启用'下一页'按钮。")
                else:
                    has_more = False
                    print(f"[UI] 单店邀约模式：当前页返回 {current_page_count} 条（不满页），禁用'下一页'按钮。")
            else:
                # 普通模式：只有当返回数量等于 limit 时，才启用下一页
                if current_page_count == limit:
                    has_more = True
                    print(f"[UI] 普通模式：当前页返回 {limit} 条，启用'下一页'按钮。")
                else:
                    # 如果返回数量小于 limit (包括 0)，则禁用
                    has_more = False
                    print(f"[UI] 普通模式：当前页返回 {current_page_count} 条 (< {limit})，禁用'下一页'按钮。")
        else:
            # 加载失败或返回数据不是列表，禁用
            print("[UI] 加载失败或返回数据无效，禁用下一页。")
            has_more = False

        self.prev_page_btn.setEnabled(has_more)

        if not has_more and overall_success and combined_list is not None:
            # 如果是因为加载成功但数量不足而禁用，提示已到最后一页
            # 移除弹窗提示，只在日志中记录
            print("[UI] 已到达最后一页，已加载所有达人数据。")

            # 如果正在自动邀约，随机切换粉丝数属性并从第一页重新开始加载
            if self.is_auto_inviting:
                print("[UI] 检测到自动邀约中，准备随机切换粉丝数属性并重新加载...")

                # 获取所有粉丝数单选按钮
                fans_radio_buttons = [
                    self.fans_unlimited,
                    self.fans_less_1w,
                    self.fans_1w_10w,
                    self.fans_10w_50w,
                    self.fans_more_50w
                ]

                # 获取当前选中的粉丝数单选按钮
                current_selected = None
                for rb in fans_radio_buttons:
                    if rb.isChecked():
                        current_selected = rb
                        break

                # 从其他单选按钮中随机选择一个
                other_buttons = [rb for rb in fans_radio_buttons if rb != current_selected]
                if other_buttons:
                    import random
                    new_selected = random.choice(other_buttons)

                    # 设置新选中的单选按钮
                    new_selected.setChecked(True)
                    print(f"[UI] 随机切换粉丝数属性从 '{current_selected.text() if current_selected else '未知'}' 到 '{new_selected.text()}'")

                    # 重置页码为1
                    self.current_influencer_page = 1
                    self.page_input.setText("1")
                    print("[UI] 重置页码为1，准备重新加载达人数据...")

                    # 延迟一小段时间后重新加载达人数据
                    QTimer.singleShot(500, self._reload_influencers_and_continue_invitation)
                else:
                    print("[UI] 无法找到其他粉丝数单选按钮，继续邀约...")
        # --- 结束修改 --- #
        # --- 移除完成提示，避免打断自动流程 --- #
        # if not has_more ... QMessageBox.information ...
        # --- 结束移除 --- #

        print("[主线程] 达人加载成功并填充表格，设置 page_load_event。")
        self.page_load_event.set()

        # 特别处理单店邀约模式的翻页事件
        if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
            print("[主线程] 单店邀约模式：达人加载成功，确保页面加载事件已设置")
            self.page_load_event.set()
        # --- 结束修改 --- #

        # --- 新增: 检查是否是自动翻页后的加载 --- #
        if hasattr(self, 'auto_inviting_before_next_page') and self.auto_inviting_before_next_page:
            print("[主线程] 检测到是自动翻页后的加载，恢复自动邀约状态...")
            self.is_auto_inviting = True
            self.auto_inviting_before_next_page = False
            # 使用QTimer延迟启动邀约循环，确保UI更新完成
            QTimer.singleShot(100, self._execute_auto_invitation_loop)
            return
        # --- 结束新增 --- #

        # --- 新增: 检查是否需要启动邀约 --- #
        # 检查是否所有店铺都已邀约完成，如果是则不启动邀约
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print("[Main Thread] 检测到所有店铺已邀约完成，跳过启动邀约")
            return

        # 检查是否是单店邀约模式，如果是则只处理单店邀约的专门逻辑
        if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
            print("[Main Thread] 检测到单店邀约模式，只处理单店邀约的专门逻辑")
            # 单店邀约有专门的处理逻辑在下面的 loading_influencers_for_single_shop_invite 检查中
            # 这里不直接返回，让它继续执行到单店邀约的专门处理逻辑

        # 检查是否是单店邀约的达人加载完成
        if hasattr(self, 'single_shop_loading_influencers') and self.single_shop_loading_influencers:
            print("[Main Thread] 检测到单店邀约达人加载完成，跳过自动邀约触发检查")
            # 单店邀约有专门的处理逻辑，继续执行到单店邀约的专门处理部分
            # 不在这里返回，让代码继续执行到单店邀约的专门处理逻辑

        # 只有在非单店邀约模式下才检查自动邀约触发条件
        if not (hasattr(self, 'single_shop_loading_influencers') and self.single_shop_loading_influencers) and \
           (hasattr(self, 'auto_adjust_page_for_invitation') and self.auto_adjust_page_for_invitation or
            hasattr(self, 'switch_shop_requested') and self.switch_shop_requested or
            hasattr(self, 'waiting_for_products_then_influencers') and self.waiting_for_products_then_influencers or
            hasattr(self, 'is_auto_inviting') and self.is_auto_inviting) and overall_success and combined_list is not None and len(combined_list) > 0:
            # 如果是自动调整页码后的加载或店铺切换后的加载或等待商品和达人加载后的邀约或已设置自动邀约标志，且加载成功且列表不为空，直接开始邀约
            print("[Main Thread] 检测到auto_adjust_page_for_invitation或switch_shop_requested或waiting_for_products_then_influencers或is_auto_inviting标志，且达人列表加载成功且不为空，直接开始邀约...")

            # 设置自动邀约标志
            self.is_auto_inviting = True

            # 使用QTimer延迟启动邀约循环，确保UI更新完成
            QTimer.singleShot(100, self._execute_auto_invitation_loop)

            # 重置标志
            if hasattr(self, 'auto_adjust_page_for_invitation'):
                self.auto_adjust_page_for_invitation = False
            if hasattr(self, 'switch_shop_requested'):
                self.switch_shop_requested = False
            if hasattr(self, 'waiting_for_products_then_influencers'):
                self.waiting_for_products_then_influencers = False

            # 停止当前邀约线程（如果存在）
            if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
                print("[Main Thread] 检测到已有邀约线程在运行，先停止当前邀约...")
                self.is_auto_inviting = False # 设置标志，让线程自行退出
                print("[Main Thread] 等待当前邀约线程退出...")
                self.auto_invitation_thread.join(1.0) # 最多等待1秒
                # 恢复自动邀约标志
                self.is_auto_inviting = True

            # 重置行索引
            self.current_auto_invite_row = 0
            print("[Main Thread] 重置行索引为0")

            # 设置UI状态
            self.is_auto_inviting = True # 设置自动邀约标志
            self.start_invite_btn.setEnabled(False) # 禁用开始按钮
            self.start_invite_btn.setText("邀约中...") # 更新按钮文本
            self.stop_invite_btn.setEnabled(True) # 启用停止按钮

            # 使用_execute_auto_invitation_loop方法启动邀约线程，确保只有一个邀约线程在运行
            print("[Main Thread] 使用_execute_auto_invitation_loop方法启动邀约线程...")
            # 确保之前的邀约线程已经停止
            if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
                print("[Main Thread] 等待之前的邀约线程停止...")
                # 先设置标志，让线程自行退出
                self.is_auto_inviting = False
                self.auto_invitation_thread.join(2.0)  # 最多等待2秒

            # 重新设置标志，确保_execute_auto_invitation_loop方法能够启动邀约线程
            self.is_auto_inviting = False

            # 使用QTimer延迟启动，确保UI更新完成
            QTimer.singleShot(500, self._execute_auto_invitation_loop)
        elif hasattr(self, 'waiting_for_influencers_to_start_invite') and self.waiting_for_influencers_to_start_invite:
            # 检查是否是单店邀约模式
            if hasattr(self, 'single_shop_loading_influencers') and self.single_shop_loading_influencers:
                print("[Main Thread] 检测到单店邀约模式，跳过等待标志处理（单店邀约有专门的处理逻辑）")
                self.waiting_for_influencers_to_start_invite = False
                # 不直接返回，让单店邀约的专门处理逻辑继续执行
            else:
                print("[Main Thread] 检测到等待标志，准备启动邀约循环...")
                self.waiting_for_influencers_to_start_invite = False # 重置标志

                # 检查加载是否真的成功且列表不为空
                if overall_success and combined_list is not None and len(combined_list) > 0:
                     print("[Main Thread] 达人列表加载成功且不为空，调用 _execute_auto_invitation_loop...")
                     QTimer.singleShot(100, self._execute_auto_invitation_loop) # 稍微延迟启动
                else:
                     print("[Main Thread] 达人列表加载失败或为空，尝试将页数改为第1页并重新加载...")

                     # 检查当前页是否已经是第1页
                     if self.current_influencer_page == 1:
                         print("[Main Thread] 当前已经是第1页，无法继续尝试。重置状态...")
                         self.start_invite_btn.setEnabled(True)
                         self.start_invite_btn.setText("开始邀约")
                         self.stop_invite_btn.setEnabled(False)
                         # 显示提示信息
                         if not overall_success:
                              QMessageBox.warning(self, "加载失败", "加载达人列表失败，无法开始自动邀约。")
                         elif not combined_list or len(combined_list) == 0:
                              QMessageBox.warning(self, "列表为空", "第1页达人列表为空，无法开始自动邀约。请检查筛选条件。")
                     else:
                         # 将页数改为第1页并重新加载
                         print("[Main Thread] 将页数改为第1页并重新加载达人列表...")
                         self.current_influencer_page = 1
                         self.page_input.setText("1")
                         # 同时更新开始页输入框，确保一致性
                         self.start_page_input.setText("1")

                         # 设置标志，表示这是自动调整页码后的加载，加载完成后应直接开始邀约
                         self.auto_adjust_page_for_invitation = True
                         print("[Main Thread] 设置auto_adjust_page_for_invitation标志为True，加载完成后将直接开始邀约")

                         # 重新加载达人列表
                         QTimer.singleShot(500, self.on_load_influencers_clicked)
        # 如果是自动邀约模式，处理达人列表加载结果
        elif self.is_auto_inviting:
            # 检查是否是单店邀约模式
            if hasattr(self, 'single_shop_loading_influencers') and self.single_shop_loading_influencers:
                print("[Main Thread] 检测到单店邀约模式，跳过自动邀约模式处理")
                # 不返回，让单店邀约的专门处理逻辑继续执行
            else:
                if overall_success and combined_list is not None and len(combined_list) > 0:
                    # 达人列表加载成功且不为空，启动邀约
                    print("[Main Thread] 检测到自动邀约模式，达人列表加载成功且不为空，准备启动邀约循环...")
                    QTimer.singleShot(100, self._execute_auto_invitation_loop) # 稍微延迟启动
                else:
                    # 达人列表加载失败或为空，尝试将页数改为第1页并重新加载
                    print("[Main Thread] 检测到自动邀约模式，但达人列表加载失败或为空，尝试将页数改为第1页并重新加载...")

                    # 检查当前页是否已经是第1页
                    if self.current_influencer_page == 1:
                        print("[Main Thread] 当前已经是第1页，无法继续尝试。重置状态...")
                        self.start_invite_btn.setEnabled(True)
                        self.start_invite_btn.setText("开始邀约")
                        self.stop_invite_btn.setEnabled(False)
                        # 显示提示信息
                        if not overall_success:
                            QMessageBox.warning(self, "加载失败", "加载达人列表失败，无法开始自动邀约。")
                        elif not combined_list or len(combined_list) == 0:
                            QMessageBox.warning(self, "列表为空", "第1页达人列表为空，无法开始自动邀约。请检查筛选条件。")
                    else:
                        # 将页数改为第1页并重新加载
                        print("[Main Thread] 将页数改为第1页并重新加载达人列表...")
                        self.current_influencer_page = 1
                        self.page_input.setText("1")
                        # 同时更新开始页输入框，确保一致性
                        self.start_page_input.setText("1")

                        # 设置标志，表示这是自动调整页码后的加载，加载完成后应直接开始邀约
                        self.auto_adjust_page_for_invitation = True
                        print("[Main Thread] 设置auto_adjust_page_for_invitation标志为True，加载完成后将直接开始邀约")

                        # 如果当前正在邀约中，先停止当前邀约
                        if hasattr(self, 'is_auto_inviting') and self.is_auto_inviting:
                            print("[Main Thread] 检测到已有邀约在进行中，先停止当前邀约...")
                            self.is_auto_inviting = False # 设置标志，让线程自行退出

                        # 重新加载达人列表
                        QTimer.singleShot(500, self.on_load_influencers_clicked)
        # --- 结束新增 --- #

        # --- 新增: 处理有序邀约流程中的达人加载 ---
        if hasattr(self, 'loading_influencers_for_auto_invite') and self.loading_influencers_for_auto_invite:
            print(f"[有序邀约] 检测到正在为自动邀约加载达人")
            shop_name = getattr(self, 'influencer_loading_shop', self.current_shop_name)

            if overall_success and len(combined_list) > 0:
                print(f"[有序邀约] 店铺 '{shop_name}' 达人加载成功，共 {len(combined_list)} 个达人")
                self._handle_influencer_loading_success(shop_name)
            elif overall_success and len(combined_list) == 0:
                print(f"[有序邀约] 店铺 '{shop_name}' 达人加载成功但列表为空")
                self._handle_influencer_loading_failed(shop_name, "达人列表为空")
            else:
                print(f"[有序邀约] 店铺 '{shop_name}' 达人加载失败")
                self._handle_influencer_loading_failed(shop_name, "API调用失败")
            return  # 有序邀约流程处理完毕，不继续执行旧的逻辑

        # +++ 新增: 处理单店邀约流程中的达人加载 +++
        if hasattr(self, 'loading_influencers_for_single_shop_invite') and self.loading_influencers_for_single_shop_invite:
            print(f"[单店邀约] 检测到正在为单店邀约加载达人")
            shop_name = getattr(self, 'single_shop_influencer_loading_shop', self.current_shop_name)

            if overall_success and len(combined_list) > 0:
                print(f"[单店邀约] 店铺 '{shop_name}' 达人加载成功，共 {len(combined_list)} 个达人")
                self._handle_single_shop_influencer_loading_success(shop_name)
            elif overall_success and len(combined_list) == 0:
                print(f"[单店邀约] 店铺 '{shop_name}' 达人加载成功但列表为空")
                self._handle_single_shop_influencer_loading_failed(shop_name, "达人列表为空")
            else:
                print(f"[单店邀约] 店铺 '{shop_name}' 达人加载失败")
                self._handle_single_shop_influencer_loading_failed(shop_name, "API调用失败")
            return  # 单店邀约流程处理完毕，不继续执行旧的逻辑
        # +++ 结束新增 +++

        # --- 移除旧的准备阶段检查 --- #
        # if self.preparing_for_auto_invite:
        #     # +++ 调试日志 +++
        #     print("[DEBUG AutoInvite Prep] Entering _handle_batch_influencer_load_result check")
        #     # +++ 结束调试 +++
        #     if overall_success:
        #         print("[AutoInvite Prep] 达人列表加载成功。")
        #         self.influencers_loaded_for_auto_invite = True
        #         # +++ 调试日志 +++
        #         print(f"[DEBUG AutoInvite Prep] influencers_loaded flag set to: {self.influencers_loaded_for_auto_invite}")
        #         print("[DEBUG AutoInvite Prep] Calling _check_and_execute_auto_invite...")
        #         # +++ 结束调试 +++
        #         self._check_and_execute_auto_invite()
        #     else:
        #         print("[AutoInvite Prep] 达人列表加载失败。")
        #         # +++ 调试日志 +++
        #         print("[DEBUG AutoInvite Prep] Calling _reset_auto_invite_preparation due to influencer load failure.")
        #         # +++ 结束调试 +++
        #         self._reset_auto_invite_preparation(error=True)
        # --- 结束移除 ---

    def populate_influencer_table(self, influencers):
        """用获取到的达人数据填充表格"""
        print(f"[UI] 正在填充达人表格，共 {len(influencers)} 条数据...")
        self.table.setRowCount(0) # 清空现有数据

        if not influencers:
            print("[UI] 达人数据为空，表格不填充。")
            # 显示一个"无数据"的提示行
            item = QTableWidgetItem("没有符合条件的达人数据。") # 修改提示文本
            item.setTextAlignment(Qt.AlignCenter)
            self.table.setRowCount(1)
            self.table.setItem(0, 0, item)
            self.table.setSpan(0, 0, 1, self.table.columnCount())
            return

        self.table.setRowCount(len(influencers))

        for row_idx, influencer in enumerate(influencers):
            col_idx = 0
            # 序号
            self.table.setItem(row_idx, col_idx, QTableWidgetItem(str(row_idx + 1))); col_idx += 1
            # 达人ID
            self.table.setItem(row_idx, col_idx, QTableWidgetItem(str(influencer.get('id', 'N/A')))); col_idx += 1
            # 达人昵称
            self.table.setItem(row_idx, col_idx, QTableWidgetItem(str(influencer.get('nickname', 'N/A')))); col_idx += 1
            # --- 修改: 状态列留空 --- #
            # status = str(influencer.get('status', 'N/A')) # 获取状态（之前计算好的）
            self.table.setItem(row_idx, col_idx, QTableWidgetItem("")); col_idx += 1
            # --- 结束修改 --- #
            # 粉丝数
            fans_item = QTableWidgetItem(str(influencer.get('fans', 0)))
            fans_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter) # 粉丝数右对齐
            self.table.setItem(row_idx, col_idx, fans_item); col_idx += 1

            # --- 新增: 填充销售额、场均销售额、客单价、粉丝年龄 --- #
            # 销售额 (列 5)
            gmv_item = QTableWidgetItem(str(influencer.get('gmv', 'N/A')))
            gmv_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row_idx, col_idx, gmv_item); col_idx += 1

            # 场均销售额 (列 6)
            live_gmv_item = QTableWidgetItem(str(influencer.get('liveGmv', 'N/A')))
            live_gmv_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row_idx, col_idx, live_gmv_item); col_idx += 1

            # 客单价 (列 7)
            price_item = QTableWidgetItem(str(influencer.get('perUvPrice', 'N/A')))
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row_idx, col_idx, price_item); col_idx += 1

            # --- 修改: 移除粉丝年龄的填充代码 --- #
            # 粉丝年龄 (列 8)
            # age_item = QTableWidgetItem(str(influencer.get('fansAge', 'N/A')))
            # # 年龄通常不需要特定对齐，保持默认左对齐即可
            # self.table.setItem(row_idx, col_idx, age_item); col_idx += 1
            # --- 结束修改 --- #

        print("[UI] 达人表格填充完成。")
        # self.table.resizeColumnsToContents() # --- 修改: 注释掉或移除此行 --- #

    # +++ 新增: 并发获取达人数据的 Worker +++
    def _concurrent_fetch_influencers_worker(self, num_pages, start_page, auth_info, filter_info):
        """后台工作线程，使用线程池并发获取多页达人数据"""
        print(f"[WorkerThread-BatchInfluencer] 开始执行，目标页数: {num_pages}，起始页: {start_page}")

        # 初始化停止标志
        if not hasattr(self, 'influencer_loading_stop_flag'):
            self.influencer_loading_stop_flag = False

        # 检查停止标志
        if hasattr(self, 'influencer_loading_stop_flag') and self.influencer_loading_stop_flag:
            print(f"[WorkerThread-BatchInfluencer] 检测到停止标志，终止达人加载")
            return

        results_in_batch = {} # 存储当轮获取的所有结果 {page_num: (influencer_list, total_count, success)}
        batch_random_seed = random.randint(1000, 9999) # 为整个批次生成一个种子
        print(f"[WorkerThread-BatchInfluencer] 本批次使用 Random Seed: {batch_random_seed}")

        with ThreadPoolExecutor(max_workers=num_pages) as executor:
            # 准备提交任务
            futures = {}
            for i in range(num_pages):
                page_to_fetch = start_page + i
                print(f"[WorkerThread-BatchInfluencer] 提交任务: 获取第 {page_to_fetch} 页")
                future = executor.submit(self._fetch_influencer_page,
                                         page_num=page_to_fetch,
                                         category_ids=filter_info['category_ids'],
                                         biz_token=auth_info['api_token'],
                                         biz_magic=auth_info['biz_magic'],
                                         cookie_input=auth_info['cookie_input'],
                                         random_seed=batch_random_seed) # <--- 传递批次种子
                futures[future] = page_to_fetch

            # 处理完成的任务
            for future in concurrent.futures.as_completed(futures):
                # 在处理每个结果前检查停止标志
                if hasattr(self, 'influencer_loading_stop_flag') and self.influencer_loading_stop_flag:
                    print(f"[WorkerThread-BatchInfluencer] 在处理结果时检测到停止标志，取消剩余任务")
                    # 取消所有未完成的任务
                    for f in futures:
                        if not f.done():
                            f.cancel()
                    break

                page_num = futures[future]
                try:
                    # 获取返回结果: (influencer_list, page_num_returned, total_count, success)
                    result_tuple = future.result()
                    if result_tuple:
                        influencer_list, _, total_count, success = result_tuple
                        print(f"[WorkerThread-BatchInfluencer] 收到 page={page_num} 的结果，成功: {success}, 商品数: {len(influencer_list) if influencer_list is not None else 'Error'}, 总数: {total_count}")
                        results_in_batch[page_num] = (influencer_list, total_count, success)
                    else:
                        print(f"[WorkerThread-BatchInfluencer] page={page_num} 返回 None 结果")
                        results_in_batch[page_num] = (None, 0, False) # 标记为失败

                except Exception as exc:
                    print(f'[WorkerThread-BatchInfluencer] page={page_num} 生成异常: {exc}')
                    results_in_batch[page_num] = (None, 0, False) # 标记为失败

        # --- 所有任务完成后，整理最终结果 --- #
        combined_list = []
        overall_success = True
        last_successful_page = start_page - 1 # 初始化为起始页的前一页
        final_total_count = 0

        print(f"[WorkerThread-BatchInfluencer] 所有并发任务完成，开始整理 {start_page} 到 {start_page + num_pages - 1} 页的结果")
        for page in range(start_page, start_page + num_pages):
            result_data = results_in_batch.get(page)
            if result_data:
                influencer_list, total_count, success = result_data
                if success and influencer_list is not None:
                    combined_list.extend(influencer_list)
                    last_successful_page = page # 更新最后成功加载的页码
                    final_total_count = total_count # 使用最后成功页的总数
                    print(f"[WorkerThread-BatchInfluencer] 添加 page={page} 的 {len(influencer_list)} 条数据到最终结果 (API总数: {total_count}) ")
                    if not influencer_list:
                        print(f"[WorkerThread-BatchInfluencer] 注意: page={page} 成功返回但列表为空，可能已无更多数据。")
                        # 如果某页成功返回空列表，我们认为后续页可能也没有数据了，可以提前停止
                        # overall_success 保持 True，但不再添加后续页（即使它们被请求了）
                        break
                else:
                    overall_success = False # 任何一页失败都标记为整体不完全成功
                    print(f"[WorkerThread-BatchInfluencer] page={page} 获取失败或列表为None，不添加到最终结果")
                    # 如果希望任何一页失败都停止处理后续页，可以加 break
                    # break
            else:
                # 理论上不应发生，因为 results_in_batch 会包含所有页码的键
                print(f"[WorkerThread-BatchInfluencer] 警告: 未找到 page={page} 的结果")
                overall_success = False

        # 检查是否被停止
        if hasattr(self, 'influencer_loading_stop_flag') and self.influencer_loading_stop_flag:
            print(f"[WorkerThread-BatchInfluencer] 达人加载被停止，总计加载 {len(combined_list)} 条数据")
            # 被停止时不发送完成信号，避免触发后续逻辑
            return

        # --- 发送最终结果给主线程 --- #
        print(f"[WorkerThread-BatchInfluencer] 整理完毕，发送包含 {len(combined_list)} 条数据的信号。最后成功页: {last_successful_page}, API总数: {final_total_count}, 整体成功状态: {overall_success}")
        self.batch_influencers_loaded.emit(combined_list, last_successful_page, final_total_count, overall_success)
    # +++ 结束新增 +++

    # +++ 新增: 处理页码输入框回车事件 +++
    def on_page_input_enter(self):
        """处理页码输入框回车事件 - 加载指定页码的达人数据"""
        if self.loading_influencers:
            print("[UI] 正在加载达人数据，请稍候...")
            return

        if not self.current_shop_name:
            QMessageBox.warning(self, "提示", "请先在顶部菜单栏选择一个店铺")
            return

        # 获取用户输入的页码
        try:
            page_num = int(self.page_input.text())
            if page_num <= 0:
                page_num = 1
                self.page_input.setText("1")
        except ValueError:
            # 如果输入无效，使用当前页码
            page_num = self.current_influencer_page if hasattr(self, 'current_influencer_page') and self.current_influencer_page > 0 else 1
            self.page_input.setText(str(page_num))

        print(f"[UI] 用户输入页码: {page_num}，准备加载该页达人数据...")

        # 设置加载状态
        self.loading_influencers = True
        self.add_to_list_btn.setEnabled(False)
        self.prev_page_btn.setEnabled(False)
        self.add_to_list_btn.setText("加载中...")

        # 获取认证信息
        try:
            # 使用ShopManager获取当前店铺的认证信息
            biz_token = ""
            biz_magic = ""
            cookies_list = []

            shop_info = ShopManager.get_shop_by_name(self.current_shop_name)
            if shop_info:
                biz_token = shop_info.get("accesstoken", "")
                biz_magic = shop_info.get("biz_magic", "")
                cookies_list = shop_info.get("cookies_list", [])

            # 获取筛选条件
            category_ids = []

            # 启动后台线程获取数据
            auth_info = {
                'api_token': biz_token,
                'biz_magic': biz_magic,
                'cookie_input': cookies_list
            }
            filter_info = {
                'category_ids': category_ids
            }

            # 设置当前页码
            self.current_influencer_page = page_num

            # 启动后台线程
            worker_thread = threading.Thread(
                target=self._concurrent_fetch_influencers_worker,
                args=(1, page_num, auth_info, filter_info),
                daemon=True
            )
            worker_thread.start()

        except Exception as e:
            print(f"[UI] 启动达人加载线程时出错: {str(e)}")
            self.loading_influencers = False
            self.add_to_list_btn.setEnabled(True)
            self.add_to_list_btn.setText("加载达人")
            self.prev_page_btn.setEnabled(False)
            self.table.setRowCount(0)
            QMessageBox.warning(self, "错误", f"启动达人数据获取任务失败: {str(e)}")
    # +++ 结束新增 +++

    # +++ 恢复: 处理"下一页"按钮点击事件 +++
    def on_next_influencer_page_clicked(self):
        """处理"下一页"按钮点击事件 - 现在加载下一批数据"""
        if self.loading_influencers:
            print("[UI] 正在加载达人数据，请稍候...")
            return

        if not self.current_shop_name:
            QMessageBox.warning(self, "提示", "请先在顶部菜单栏选择一个店铺")
            return
        # 检查当前页是否大于0，表示至少已加载过一批
        if self.current_influencer_page <= 0:
            QMessageBox.information(self, "提示", "请先点击'加载达人'加载第一批数据。")
            return

        # 如果正在自动邀约，保存当前状态，但不停止邀约
        if hasattr(self, 'is_auto_inviting') and self.is_auto_inviting:
            print("[UI] 检测到正在自动邀约中，保存当前状态...")
            # 不停止自动邀约，只是记录状态
            self.auto_inviting_before_next_page = True
        elif hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
            print("[UI] 检测到正在单店邀约中，继续翻页...")
            self.auto_inviting_before_next_page = False
        else:
            self.auto_inviting_before_next_page = False

        # 使用ShopManager获取 Cookie 和认证信息 (与加载第一批时相同)
        shop_info = ShopManager.get_shop_by_name(self.current_shop_name)
        if not shop_info:
            QMessageBox.warning(self, "错误", f"未找到店铺 '{self.current_shop_name}' 的信息。")
            return

        cookies_list = shop_info.get('cookies_list', [])
        if not cookies_list:
             QMessageBox.warning(self, "错误", f"未能获取店铺 '{self.current_shop_name}' 的 Cookie。")
             return
        cookie_dict = {c['name']: c['value'] for c in cookies_list}
        biz_token = cookie_dict.get('biz_token')
        biz_magic = cookie_dict.get('biz_magic')
        if not biz_token or not biz_magic:
             QMessageBox.warning(self, "错误", f"从 Cookie 中未能提取必要的认证参数。")
             return

        # --- 修改: 收集所有勾选的类目 ID --- #
        category_ids = []
        print("[UI] (翻页) 正在收集勾选的类目ID...")
        if hasattr(self, 'category_checkboxes') and self.category_checkboxes:
            for checkbox in self.category_checkboxes:
                if checkbox.isChecked():
                    category_name = checkbox.text()
                    cat_id = self.category_id_map.get(category_name)
                    if cat_id:
                        category_ids.append(cat_id)
                        print(f"  - 已勾选: {category_name} (ID: {cat_id})")
                    else:
                        print(f"  - 警告: 找到已勾选类目 '{category_name}' 但未在映射中找到ID。")
        else:
            print("[UI] (翻页) 警告: category_checkboxes 未初始化或为空。")

        if not category_ids:
            print("[UI] (翻页) 未选择任何有效类目，将加载所有类目达人")
        # --- 结束修改 --- #

        # 计算下一批的起始页码
        next_start_page = self.current_influencer_page + 1
        num_pages_to_fetch = 1 # 修改: 每次只获取 1 页

        # 设置加载状态
        self.loading_influencers = True
        self.add_to_list_btn.setEnabled(False) # 禁用加载按钮
        self.prev_page_btn.setEnabled(False) # --- 确保加载时禁用下一页按钮 ---
        self.add_to_list_btn.setText("加载达人") # 确保文本正确

        # --- 新增: 重置断点续传索引 --- #
        # self.auto_invite_resume_index = 0
        # --- 结束新增 --- #

        print(f"[UI] 请求加载下一批达人数据，起始页: {next_start_page}，共 {num_pages_to_fetch} 页")
        QApplication.processEvents() # 刷新 UI

        # 启动后台线程获取下一批数据
        try:
            # 打包认证和筛选信息
            auth_info = {
                'api_token': biz_token,
                'biz_magic': biz_magic,
                'cookie_input': cookies_list
            }
            filter_info = {
                'category_ids': category_ids
                # 未来可以添加更多筛选条件
            }

            worker_thread = threading.Thread(
                target=self._concurrent_fetch_influencers_worker,
                args=(num_pages_to_fetch, next_start_page, auth_info, filter_info),
                daemon=True
            )
            worker_thread.start()
        except Exception as e:
            print(f"[UI] 启动达人加载线程时出错: {str(e)}")
            # --- 修复: 恢复加载按钮，而不是下一页按钮 --- #
            self.loading_influencers = False
            self.add_to_list_btn.setEnabled(True)
            self.add_to_list_btn.setText("加载达人")
            self.prev_page_btn.setEnabled(False) # 初始加载出错，下一页保持禁用
            self.table.setRowCount(0) # 清空表格，可能显示错误
            QMessageBox.warning(self, "错误", f"启动达人数据获取任务失败: {str(e)}")
            # --- 结束修复 --- #
        # --- 结束修改 --- #

    # +++ 新增: 重新加载达人数据并继续邀约 +++
    def _reload_influencers_and_continue_invitation(self):
        """重新加载达人数据并继续邀约"""
        print("[达人邀约] 重新加载达人数据并继续邀约...")

        # 确保自动邀约标志为True
        self.is_auto_inviting = True

        # 加载达人数据
        self.on_load_influencers_clicked()

        # 后续流程会在达人数据加载完成后自动触发邀约
        # 参见: _handle_batch_influencer_load_result -> _execute_auto_invitation_loop
    # +++ 结束新增 +++

    # +++ 新增: 尝试切换粉丝量区间并重新加载 +++
    def _try_switch_fans_interval_and_reload(self):
        """尝试切换到下一个粉丝量区间并重新加载达人数据

        返回:
            bool: 如果成功切换到新的粉丝量区间则返回True，否则返回False
        """
        print("[粉丝区间切换] 尝试切换粉丝量区间...")

        # 获取所有粉丝数单选按钮
        fans_radio_buttons = [
            (self.fans_unlimited, "不限"),
            (self.fans_less_1w, "1w以下"),
            (self.fans_1w_10w, "1w-10w"),
            (self.fans_10w_50w, "10w-50w"),
            (self.fans_more_50w, "50w以上")
        ]

        # 获取当前选中的粉丝数单选按钮
        current_selected = None
        current_index = -1
        for i, (rb, name) in enumerate(fans_radio_buttons):
            if rb.isChecked():
                current_selected = rb
                current_index = i
                print(f"[粉丝区间切换] 当前选中的粉丝量区间: {name}")
                break

        if current_selected is None:
            print("[粉丝区间切换] 错误：未找到当前选中的粉丝量区间")
            return False

        # 尝试切换到下一个区间（循环切换）
        next_index = (current_index + 1) % len(fans_radio_buttons)

        # 如果已经回到起始区间，说明所有区间都试过了
        if not hasattr(self, '_original_fans_interval_index'):
            # 第一次切换，记录原始区间
            self._original_fans_interval_index = current_index
            print(f"[粉丝区间切换] 记录原始粉丝量区间索引: {current_index}")
        elif next_index == self._original_fans_interval_index:
            print("[粉丝区间切换] 已尝试所有粉丝量区间，无法继续切换")
            # 重置原始区间记录
            delattr(self, '_original_fans_interval_index')
            return False

        # 切换到下一个区间
        next_button, next_name = fans_radio_buttons[next_index]
        next_button.setChecked(True)
        print(f"[粉丝区间切换] 已切换粉丝量区间从 '{fans_radio_buttons[current_index][1]}' 到 '{next_name}'")

        # 重置页码为1
        self.current_influencer_page = 1
        self.page_input.setText("1")
        print("[粉丝区间切换] 重置页码为1")

        # 延迟一小段时间后重新加载达人数据
        QTimer.singleShot(500, self._reload_influencers_and_continue_invitation)

        return True
    # +++ 结束新增 +++

    # +++ 新增: 右键菜单处理逻辑 +++
    def _show_influencer_context_menu(self, pos):
        """显示达人表格的右键菜单"""
        item = self.table.itemAt(pos)
        if item is None:
            return # 没有点击在单元格上

        row = item.row()
        menu = QMenu(self)

        invite_action = menu.addAction("邀约达人")
        copy_nickname_action = menu.addAction("复制达人昵称")

        # 显示菜单并获取用户选择的动作
        action = menu.exec_(self.table.mapToGlobal(pos))

        # 根据选择的动作执行相应操作
        if action == invite_action:
            self._invite_selected_influencer(row)
        elif action == copy_nickname_action:
            self._copy_selected_influencer_nickname(row)

    def _invite_selected_influencer(self, row):
        """处理"邀约达人"菜单项点击 - 实现发送邀约逻辑"""
        influencer_id_item = self.table.item(row, 1) # 达人ID (finderUsername) 在第2列 (索引1)
        influencer_nickname_item = self.table.item(row, 2) # 达人昵称在第3列 (索引2)

        if not (influencer_id_item and influencer_nickname_item):
            print(f"错误: 无法获取行 {row+1} 的达人ID或昵称")
            # --- 修改: 不再弹框，只打印错误 --- #
            # QMessageBox.warning(self, "错误", f"无法获取行 {row+1} 的达人信息。")
            # --- 结束修改 --- #
            return

        finder_username = influencer_id_item.text()
        nickname = influencer_nickname_item.text()
        print(f"开始处理邀约: 达人={nickname} (ID={finder_username})")

        # --- 在弹出消息框前检查是否处于自动邀约模式 ---
        show_message_box = not self.is_auto_inviting
        # --- 结束检查 --- #

        # --- 所有前置检查移到这里，保持在主线程 ---
        # 1. 获取达人信息 (已在方法开头完成)

        # 2. 获取商品列表
        item_list = []
        spu_id_col_index = 1
        all_spu_ids = []
        for r in range(self.selected_products_table.rowCount()):
            spu_id_item = self.selected_products_table.item(r, spu_id_col_index)
            if spu_id_item and spu_id_item.text():
                all_spu_ids.append(spu_id_item.text())

        if not all_spu_ids:
            print("警告: 右侧商品列表为空，主动触发商品加载...")
            # 在自动邀约模式下，主动触发商品加载并等待
            if self.is_auto_inviting:
                # 更新状态为等待商品加载
                status_col_index = 3; status_item = self.table.item(row, status_col_index)
                if not status_item: status_item = QTableWidgetItem(""); self.table.setItem(row, status_col_index, status_item)
                status_item.setText("正在加载商品...")

                # 将row赋值给row_index，以便传递给_wait_for_products_worker
                row_index = row

                # 创建一个事件，用于线程间通信
                product_loaded_event = threading.Event()

                # 创建一个共享变量，用于存储加载结果
                product_load_result = {"success": False, "spu_ids": []}

                # 创建并启动后台线程加载商品
                product_load_thread = threading.Thread(
                    target=self._wait_for_products_worker,
                    args=(row_index, spu_id_col_index, product_loaded_event, product_load_result),
                    daemon=True
                )
                product_load_thread.start()

                # 在主线程中等待商品加载完成，但不阻塞UI
                # 使用QTimer定时检查加载状态
                start_time = time.time()
                max_wait_time = 30 if hasattr(self, 'switch_shop_requested') and self.switch_shop_requested else 15

                while not product_loaded_event.is_set() and (time.time() - start_time) < max_wait_time:
                    # 处理UI事件，保持界面响应
                    QApplication.processEvents()
                    # 短暂休眠，减少CPU使用
                    time.sleep(0.1)

                # 检查加载结果
                if product_load_result["success"]:
                    print(f"[商品加载] 商品已成功加载，共 {len(product_load_result['spu_ids'])} 个商品")
                    all_spu_ids = product_load_result["spu_ids"]
                else:
                    print("[商品加载] 等待超时或加载失败，商品列表仍为空，但不显示提示，继续下一个达人")
                    # 安全地设置状态，检查表格项是否仍然有效
                    try:
                        # 重新获取状态项，确保它仍然存在
                        current_status_item = self.table.item(row, status_col_index)
                        if current_status_item:
                            current_status_item.setText("商品加载超时")
                        else:
                            print(f"[商品加载] 警告：行 {row} 的状态项已被删除，无法设置状态")
                    except RuntimeError as e:
                        print(f"[商品加载] 设置状态时出错（表格项可能已被删除）: {e}")
                    return
            else:
                # 手动模式下仍然提示，但不弹窗
                print("警告: 右侧商品列表为空，无法邀约。")
                return

        # 获取邀约商品数并选择
        try:
            invite_count = int(self.invite_count_input.text())
            if invite_count <= 0: invite_count = 1
        except ValueError: invite_count = 30
        sample_size = min(len(all_spu_ids), invite_count)
        selected_spu_ids = random.sample(all_spu_ids, sample_size)
        item_list = [{"spuId": spu_id} for spu_id in selected_spu_ids]
        print(f"已随机选择 {len(item_list)} 个商品 (目标: {invite_count})。")

        # 在发送前标记 (移除只在手动模式)
        # if show_message_box: <-- 移除此条件
        self._mark_invited_products(selected_spu_ids)
        QApplication.processEvents() # 确保 UI 更新

        # 3. 获取联系信息
        contact_name = self.contact_input.text().strip()
        contact_wx_id = self.wechat_input.text().strip()
        contact_phone = self.phone_input.text().strip()
        invitation_introduction = self.desc_text.toPlainText().strip()
        if not (contact_name and contact_wx_id and contact_phone and invitation_introduction):
             print(f"错误: 邀约设置信息不完整")
             if self.is_auto_inviting:
                  status_col_index = 3
                  try:
                      status_item = self.table.item(row, status_col_index)
                      if not status_item:
                          status_item = QTableWidgetItem("")
                          self.table.setItem(row, status_col_index, status_item)
                      status_item.setText("邀约设置不完整")
                  except RuntimeError as e:
                      print(f"[邀约设置检查] 设置状态时出错（表格项可能已被删除）: {e}")
             else:
                  QMessageBox.warning(self, "信息不全", "请填写完整的邀约设置信息。") # 手动模式提示
             return
        contact_info = {
            "contactName": contact_name,
            "contactWxId": contact_wx_id,
            "contactPhoneNumber": contact_phone,
            "invitationIntroduction": invitation_introduction,
            "inviteScene": "1" # 场景固定为 "1"
        }
        # print("已获取邀约设置信息。") # 日志可以保留

        # 4. 获取认证信息 (token, cookie, biz_magic) - 使用ShopManager
        account_info = ShopManager.get_shop_by_name(self.current_shop_name)
        if not account_info:
            print(f"[邀约] 错误：未找到店铺 '{self.current_shop_name}' 的信息")
            if self.is_auto_inviting:
                self._handle_invitation_error("未找到店铺信息")
            else:
                QMessageBox.warning(self, "错误", f"未找到店铺 '{self.current_shop_name}' 的信息")
            return
        access_token = account_info.get('accesstoken')
        cookie_input = account_info.get('cookies_list', [])
        if not access_token:
            print(f"[邀约] 错误：店铺 '{self.current_shop_name}' 缺少access_token")
            if self.is_auto_inviting:
                self._handle_invitation_error("缺少access_token")
            else:
                QMessageBox.warning(self, "错误", f"店铺 '{self.current_shop_name}' 缺少access_token")
            return

        # +++ 调试日志 +++
        print(f"[DEBUG Invite-{row+1}] Before extracting biz_magic. Cookie type: {type(cookie_input)}")
        # +++ 结束调试 +++
        biz_magic = None
        if isinstance(cookie_input, list):
            for cookie in cookie_input:
                if isinstance(cookie, dict) and cookie.get('name') == 'biz_magic':
                    biz_magic = cookie.get('value')
                    break
        elif isinstance(cookie_input, str): # 如果是字符串，尝试解析
             try:
                 parts = cookie_input.split(';')
                 for part in parts:
                      if '=' in part:
                           name, value = part.strip().split('=', 1)
                           if name == 'biz_magic':
                                biz_magic = value
                                break
             except Exception as parse_err:
                  print(f"[DEBUG Invite-{row+1}] Error parsing cookie string: {parse_err}") # 添加解析错误日志

        # +++ 调试日志 +++
        print(f"[DEBUG Invite-{row+1}] After extracting biz_magic. Value found: {'Yes' if biz_magic else 'No'}")
        # +++ 结束调试 +++

        if not biz_magic:
             print(f"错误: 无法从 Cookie 中提取 biz_magic。 Cookie: {cookie_input}")
             if self.is_auto_inviting:
                  status_col_index = 3
                  try:
                      status_item = self.table.item(row, status_col_index)
                      if not status_item:
                          status_item = QTableWidgetItem("")
                          self.table.setItem(row, status_col_index, status_item)
                      status_item.setText("biz_magic丢失")
                  except RuntimeError as e:
                      print(f"[biz_magic检查] 设置状态时出错（表格项可能已被删除）: {e}")
             else:
                  QMessageBox.warning(self, "错误", f"未能从店铺 '{self.current_shop_name}' 的 Cookie 中提取 biz_magic。")
             return
        # --- 结束前置检查 ---

        # --- 修改: 更新状态并启动后台线程 ---
        # 更新状态为"邀约中..."
        status_col_index = 3
        try:
            status_item = self.table.item(row, status_col_index)
            if not status_item:
                status_item = QTableWidgetItem("")
                self.table.setItem(row, status_col_index, status_item)
            status_item.setText("邀约中...")
            status_item.setToolTip("") # 清除旧的 tooltip
            QApplication.processEvents() # 立即显示状态
        except RuntimeError as e:
            print(f"[邀约状态更新] 设置状态时出错（表格项可能已被删除）: {e}")
            # 如果无法设置状态，仍然继续邀约流程

        # 准备传递给后台线程的参数
        worker_args = (
            row,
            finder_username,
            nickname,
            item_list,
            contact_info,
            access_token,
            biz_magic,
            cookie_input
        )

        # 创建并启动后台线程
        try:
            invitation_thread = threading.Thread(
                target=self._perform_invitation_worker,
                args=worker_args,
                daemon=True
            )
            invitation_thread.start()
            print(f"[主线程] 已为行 {row+1} 启动后台邀约线程。")
        except Exception as e:
            print(f"[主线程] 启动邀约线程时发生错误: {e}")
            # 如果启动线程失败，需要将状态改回去
            try:
                # 重新获取状态项，确保它仍然存在
                current_status_item = self.table.item(row, status_col_index)
                if current_status_item:
                    current_status_item.setText("线程启动失败")
                    current_status_item.setToolTip(f"错误: {str(e)}")
                else:
                    print(f"[线程启动失败] 警告：行 {row} 的状态项已被删除，无法设置状态")
            except RuntimeError as e2:
                print(f"[线程启动失败] 设置状态时出错（表格项可能已被删除）: {e2}")
            if show_message_box:
                 QMessageBox.critical(self, "错误", f"无法启动邀约处理线程: {e}")

        # --- 结束修改 ---

        # --- 移除原来的 try...except...finally 块 ---
        # try:
        #     # ... 原来的 API 调用和结果处理 ...
        # except Exception as e:
        #     # ... 原来的异常处理 ...
        # finally:
        #     pass

    def _copy_selected_influencer_nickname(self, row):
        """处理"复制达人昵称"菜单项点击"""
        nickname_item = self.table.item(row, 2) # 达人昵称在第3列 (索引2)

        if nickname_item:
            nickname = nickname_item.text()
            clipboard = QApplication.clipboard()
            clipboard.setText(nickname)
            print(f"已复制达人昵称: {nickname}")
            # 可以选择性地显示一个短暂的提示，告知用户已复制成功
            # self.main_window_ref.statusBar().showMessage(f'已复制: {nickname}', 2000) # 需要主窗口引用和状态栏
        else:
            print(f"错误: 无法获取行 {row+1} 的达人昵称")
            # --- 修改: 不再弹框 --- #
            # QMessageBox.warning(self, "错误", f"无法获取行 {row+1} 的达人昵称。")
            # --- 结束修改 --- #
    # +++ 结束新增 +++

    # --- 新增: 商品加载信号 --- #
    product_load_signal = pyqtSignal(bool, list)  # 参数：成功标志，商品ID列表

    # --- 修改: 后台线程等待商品加载 --- #
    def _wait_for_products_worker(self, row_index, spu_id_col_index, loaded_event, result_dict):
        """后台线程：等待商品加载完成 - 使用API返回结果判断成功失败

        Args:
            row_index: 达人表格中的行索引（仅用于日志记录）
            spu_id_col_index: 商品ID列的索引
            loaded_event: 用于通知主线程商品加载完成的事件
            result_dict: 用于存储加载结果的字典
        """
        try:
            print(f"[商品加载线程] 开始加载商品... (达人行索引: {row_index})")

            # 主动触发商品加载（通过信号在主线程中执行）
            print("[商品加载线程] 请求主线程触发商品加载操作")

            # 创建一个事件，用于等待API加载完成
            api_load_complete = threading.Event()

            # 创建一个共享变量，用于存储API加载结果
            api_result = {"success": False, "data": None}

            # 定义一个函数，用于接收API加载完成的通知
            def on_api_load_complete(success, data):
                api_result["success"] = success
                api_result["data"] = data
                api_load_complete.set()

            # 连接批量获取完成信号到临时函数
            self.batch_fetch_complete.connect(lambda results: on_api_load_complete(True, results))

            # 使用QMetaObject.invokeMethod在主线程中调用商品加载方法
            QMetaObject.invokeMethod(self, "on_load_products_clicked", Qt.QueuedConnection)
            print("[商品加载线程] 已请求主线程触发商品加载")

            # 等待API加载完成，最多等待30秒
            api_load_complete.wait(30)

            # 断开信号连接
            try:
                self.batch_fetch_complete.disconnect()
            except TypeError:
                pass  # 信号可能已经断开

            # 检查API加载结果
            if api_result["success"] and api_result["data"]:
                # API加载成功，检查商品列表
                print("[商品加载线程] API加载成功，检查商品列表")

                # 请求主线程检查商品列表并返回结果
                check_result = {"done": False, "spu_ids": []}
                check_lock = threading.Lock()
                check_condition = threading.Condition(check_lock)

                # 定义一个槽函数来接收主线程的检查结果
                def handle_check_result(success_flag, spu_ids):
                    with check_lock:
                        check_result["done"] = True
                        check_result["success"] = success_flag
                        check_result["spu_ids"] = spu_ids
                        check_condition.notify()

                # 连接信号到临时槽函数
                self.product_load_signal.connect(handle_check_result)

                # 在主线程中执行检查
                QMetaObject.invokeMethod(self, "_check_product_list_in_main_thread",
                                        Qt.QueuedConnection,
                                        Q_ARG(int, spu_id_col_index))

                # 等待主线程返回结果
                with check_lock:
                    if not check_result["done"]:
                        check_condition.wait(1.0)  # 最多等待1秒

                # 断开信号连接
                self.product_load_signal.disconnect(handle_check_result)

                # 获取结果
                all_spu_ids = check_result["spu_ids"]

                if all_spu_ids:
                    print(f"[商品加载线程] 商品已加载成功，共 {len(all_spu_ids)} 个商品")
                    # 更新结果
                    result_dict["success"] = True
                    result_dict["spu_ids"] = all_spu_ids
                else:
                    print("[商品加载线程] API加载成功但商品列表为空")
                    result_dict["success"] = True  # 仍然标记为成功，但列表为空
                    result_dict["spu_ids"] = []
            else:
                # API加载失败
                print("[商品加载线程] API加载失败或超时")
                result_dict["success"] = False
                result_dict["spu_ids"] = []

            # 设置事件，通知主线程
            loaded_event.set()

        except Exception as e:
            print(f"[商品加载线程] 发生异常: {e}")
            result_dict["success"] = False
            result_dict["spu_ids"] = []
            # 设置事件，通知主线程
            loaded_event.set()

    # --- 新增: 在主线程中检查商品列表 --- #
    @pyqtSlot(int)
    def _check_product_list_in_main_thread(self, spu_id_col_index):
        """在主线程中检查商品列表，并通过信号返回结果"""
        try:
            all_spu_ids = []
            for r in range(self.selected_products_table.rowCount()):
                spu_id_item = self.selected_products_table.item(r, spu_id_col_index)
                if spu_id_item and spu_id_item.text():
                    all_spu_ids.append(spu_id_item.text())

            # 发送信号，通知后台线程
            self.product_load_signal.emit(True, all_spu_ids)
        except Exception as e:
            print(f"[主线程] 检查商品列表时出错: {e}")
            # 发送空列表
            self.product_load_signal.emit(False, [])

    # --- 新增: 标记已邀约商品的方法 --- #
    def _mark_invited_products(self, invited_spu_ids):
        """根据传入的 spuId 列表，勾选商品表格中对应的复选框"""
        print(f"开始标记 {len(invited_spu_ids)} 个已邀约商品...")
        table = self.selected_products_table
        spu_id_col_index = 1 # 商品ID (spuId) 在索引 1
        checkbox_col_index = 0 # 复选框在索引 0

        # 1. 先取消所有勾选
        for row in range(table.rowCount()):
            cell_widget = table.cellWidget(row, checkbox_col_index)
            if cell_widget:
                # 查找 QCheckBox 子控件
                checkbox = cell_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(False)

        # 2. 勾选已邀约的商品
        marked_count = 0
        invited_set = set(invited_spu_ids) # 使用集合提高查找效率
        for row in range(table.rowCount()):
            spu_id_item = table.item(row, spu_id_col_index)
            if spu_id_item and spu_id_item.text() in invited_set:
                cell_widget = table.cellWidget(row, checkbox_col_index)
                if cell_widget:
                    checkbox = cell_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(True)
                        marked_count += 1

        print(f"标记完成，共勾选 {marked_count} 个商品。")
    # --- 结束新增 --- #

    # +++ 新增: 自动邀约控制方法 +++
    def _start_auto_invitation(self):
        """开始自动邀约流程 - 支持从断点恢复"""
        print(f"[DEBUG AutoInvite] _start_auto_invitation called. Current row index: {self.current_auto_invite_row}")

        # 检查是否正在进行单店邀约，如果是则不允许启动自动邀约
        if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
            print("[自动邀约] 检测到正在进行单店邀约，不允许启动自动邀约")
            QMessageBox.warning(self, "提示", "正在进行单店邀约，请等待完成后再启动自动邀约")
            return

        # 检查是否有选择店铺，如果没有则自动选择第一个可用店铺
        if not self.current_shop_name:
            print("[自动邀约] 当前未选择店铺，尝试自动选择第一个可用店铺")

            # 检查店铺下拉框是否有可用店铺
            if hasattr(self, 'shop_combo') and self.shop_combo.count() > 1:
                # 选择第一个非"当前店铺"的选项
                first_shop = self.shop_combo.itemText(1)  # 索引1是第一个实际店铺
                print(f"[自动邀约] 自动选择店铺: {first_shop}")

                # 更新下拉框选择
                self.shop_combo.setCurrentIndex(1)

                # 手动触发店铺选择变化
                self._on_internal_shop_selection_changed(1)

                print(f"[自动邀约] 已自动选择店铺: {self.current_shop_name}")
            else:
                print("[自动邀约] 没有可用的店铺")
                QMessageBox.warning(self, "提示", "没有可用的店铺，请检查店铺列表")
                return

        # 再次检查是否成功选择了店铺
        if not self.current_shop_name:
            QMessageBox.warning(self, "提示", "无法自动选择店铺，请手动选择一个店铺")
            return

        # 重置已显示所有店铺邀约完成提示的标志
        self.all_shops_completed_notified = False

        # 清除前一天的日志记录
        InviteLogDialog.clear_previous_day_logs()

        # 注意：不在这里检查所有店铺是否完成，因为用户可能想要重新邀约
        # 只有在邀约过程中才检查店铺完成状态

        # 检查当前店铺是否已在日志中有记录
        if self._check_shop_in_log(self.current_shop_name):
            print(f"[邀约日志] 店铺 '{self.current_shop_name}' 已在日志中有记录，自动切换到下一个店铺")
            # 获取下一个未在日志中有记录的店铺
            next_shop = self._get_next_shop_name(self.current_shop_name)
            if next_shop:
                print(f"[邀约日志] 找到下一个未在日志中有记录的店铺: '{next_shop}'，准备切换")

                # 设置UI状态，表明正在切换店铺
                self.start_invite_btn.setEnabled(False)
                self.start_invite_btn.setText("切换店铺中...")
                self.stop_invite_btn.setEnabled(True)

                # 设置标志，表示这是启动时的店铺切换
                self.switch_shop_requested = True
                # 注意：不在这里设置 is_auto_inviting = True，等切换完成后再设置

                # 使用新方法清空达人列表并发送切换店铺信号
                self._clear_table_and_switch_shop(next_shop)
            else:
                print("[邀约日志] 无法找到下一个未在日志中有记录的店铺，检查是否所有店铺都已完成")
                # 检查是否所有店铺都已完成
                if self._check_all_shops_in_log():
                    print("[邀约日志] 所有店铺都已在日志中有记录，邀约完成")
                    self._handle_all_shops_completed("_start_auto_invitation方法-所有店铺已完成")
                else:
                    print("[邀约日志] 还有店铺未完成，但无法找到下一个店铺，可能存在逻辑错误")
                    QMessageBox.warning(self, "提示", "无法找到可以邀约的店铺，请检查店铺状态")
            return

        # 重置当前店铺的邀约状态
        self.current_shop_invite_status = {
            "status": "开始邀约",  # 设置状态为开始邀约
            "count": 0,
            "recorded": False
        }
        print(f"[邀约开始] 已设置店铺 '{self.current_shop_name}' 状态为开始邀约")

        # 更新达人邀约页面内部的店铺下拉框显示
        self._update_internal_shop_combo(self.current_shop_name)

        # --- 检查是否可以恢复邀约 ---
        can_resume = False
        if self.is_auto_inviting:
            print("[DEBUG AutoInvite] 已经在邀约中，忽略重复的开始请求")
            return

        # 检查是否是店铺切换状态
        is_shop_switching = hasattr(self, 'switch_shop_requested') and self.switch_shop_requested

        # 检查是否是新的一天（清除了前一天的日志）
        is_new_day = not hasattr(self, '_last_invite_date') or self._last_invite_date != time.strftime("%Y-%m-%d")

        # 只有在以下条件都满足时才考虑恢复邀约：
        # 1. 有达人列表和商品列表
        # 2. 当前行号大于0且小于总行数
        # 3. 不是店铺切换状态
        # 4. 不是新的一天
        if (self.table.rowCount() > 0 and
            self.selected_products_table.rowCount() > 0 and
            self.current_auto_invite_row > 0 and
            self.current_auto_invite_row < self.table.rowCount() and
            not is_shop_switching and
            not is_new_day):

            print(f"[Resume Check] 检测到可恢复状态：达人列表有 {self.table.rowCount()} 行, 商品列表有 {self.selected_products_table.rowCount()} 行, 下次邀约行: {self.current_auto_invite_row + 1}")
            print(f"[Resume Check] 店铺切换状态: {is_shop_switching}, 新的一天: {is_new_day}")

            # 提示用户是否要从上次停止的位置继续
            reply = QMessageBox.question(self, "继续邀约",
                                         f"检测到上次邀约在第 {self.current_auto_invite_row + 1} 行被中断，是否从该位置继续邀约？\n\n选择\"是\"从中断位置继续，选择\"否\"从头开始。",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)

            if reply == QMessageBox.Yes:
                can_resume = True
                print("[Resume Check] 用户选择从中断位置继续邀约。")
            else:
                # 用户选择从头开始，重置行号
                self.current_auto_invite_row = 0
                print("[Resume Check] 用户选择从头开始邀约，重置行号为0。")
        else:
            print(f"[Resume Check] 未检测到可恢复状态，将执行完整启动流程。")
            print(f"[Resume Check] 条件检查: 达人行数={self.table.rowCount()}, 商品行数={self.selected_products_table.rowCount()}, 当前行号={self.current_auto_invite_row}, 店铺切换={is_shop_switching}, 新的一天={is_new_day}")
            # 重置行号，确保从头开始
            self.current_auto_invite_row = 0

        # 记录当前日期
        self._last_invite_date = time.strftime("%Y-%m-%d")

        # 清空跳过记录
        print("[AutoInvite Start] 清空之前的店铺跳过记录。")
        self.skipped_shops_info.clear()

        # 清空已尝试的粉丝数选项记录
        if hasattr(self, '_tried_fans_options'):
            print("[AutoInvite Start] 清空已尝试的粉丝数选项记录。")
            self._tried_fans_options.clear()

        if can_resume:
            # --- 直接恢复邀约 ---
            print(f"[AutoInvite Resume] 启动后台邀约线程，从行 {self.current_auto_invite_row + 1} 开始...")
            # 设置邀约状态
            self.is_auto_inviting = True
            self.preparing_for_auto_invite = False
            # 更新UI状态
            self.start_invite_btn.setEnabled(False)
            self.start_invite_btn.setText("邀约中...")
            self.stop_invite_btn.setEnabled(True)
            # 启动邀约线程
            self.auto_invitation_thread = threading.Thread(target=self._auto_invitation_worker, daemon=True)
            self.auto_invitation_thread.start()
        else:
            # --- 执行完整的首次启动/重新启动流程 ---
            print("[AutoInvite Start] 开始自动邀约流程...")
            # 设置准备状态
            self.is_auto_inviting = False  # 还没有真正开始邀约
            self.preparing_for_auto_invite = True  # 正在准备中
            # 更新UI状态
            self.start_invite_btn.setEnabled(False)
            self.start_invite_btn.setText("加载商品中...")
            self.stop_invite_btn.setEnabled(True)

            # 设置标志，表示等待商品加载完成后再加载达人
            self.waiting_for_products_then_influencers = True
            print("[AutoInvite Start] 设置waiting_for_products_then_influencers标志为True，开始加载商品...")

            # 先加载商品
            try:
                # 检查是否已经在加载商品，防止重复加载
                if self.product_list_btn.text() == "加载中...":
                    print("[AutoInvite Start] 商品正在加载中，跳过重复加载请求")
                    return
                self.on_load_products_clicked()
            except Exception as e:
                print(f"[AutoInvite Start] 加载商品失败: {e}")
                # 重置状态
                self.preparing_for_auto_invite = False
                self.waiting_for_products_then_influencers = False
                self.start_invite_btn.setEnabled(True)
                self.start_invite_btn.setText("开始邀约")
                self.stop_invite_btn.setEnabled(False)
                QMessageBox.warning(self, "错误", f"加载商品失败: {e}")

            # 商品加载成功后，在_handle_batch_fetch_complete方法中会检查waiting_for_products_then_influencers标志
            # 如果为True，则会加载达人，然后开始自动邀约

    def _stop_auto_invitation(self, finished_naturally=False, reason="邀约中断"): # 修改默认参数，避免误用"手动停止"
        """停止自动邀约流程"""
        print(f"[DEBUG AutoInvite] ===== _stop_auto_invitation called =====")
        print(f"[DEBUG AutoInvite] Finished naturally: {finished_naturally}, Reason: {reason}")
        print(f"[DEBUG AutoInvite] 当前状态: is_auto_inviting={self.is_auto_inviting}, preparing_for_auto_invite={getattr(self, 'preparing_for_auto_invite', False)}")
        print(f"[DEBUG AutoInvite] 店铺切换标志: switch_shop_requested={getattr(self, 'switch_shop_requested', False)}")

        # 如果是手动停止，立即强制停止，不管其他任何条件
        if reason == "手动停止":
            print(f"[DEBUG AutoInvite] ===== 检测到手动停止，立即强制停止所有流程 =====")

            # 强制停止所有状态
            self.is_auto_inviting = False
            self.preparing_for_auto_invite = False
            self.switch_shop_requested = False

            # 强制停止邀约线程
            if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
                print(f"[DEBUG AutoInvite] 强制停止邀约线程...")
                # 设置所有等待事件
                if hasattr(self, 'invitation_wait_event'):
                    self.invitation_wait_event.set()
                if hasattr(self, 'page_load_event'):
                    self.page_load_event.set()
                # 等待线程退出
                self.auto_invitation_thread.join(3.0)
                print(f"[DEBUG AutoInvite] 邀约线程已停止")

            # 强制更新按钮状态
            self.start_invite_btn.setEnabled(True)
            self.start_invite_btn.setText("开始邀约(已停止)")
            self.stop_invite_btn.setEnabled(False)

            # 显示停止消息
            QMessageBox.information(self, "自动邀约已停止", "自动邀约已手动停止。")

            print(f"[DEBUG AutoInvite] ===== 手动停止处理完成 =====")
            return

        # 记录真正的停止原因，用于后续状态判断
        self._last_stop_reason = reason
        print(f"[DEBUG AutoInvite] 记录停止原因: {reason}")

        # 特殊处理：如果是"所有店铺已邀约完成"且已经通知过，直接返回避免重复处理
        if reason == "所有店铺已邀约完成" and hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print(f"[DEBUG AutoInvite] 所有店铺已邀约完成已处理过，忽略重复的停止请求")
            return

        # 检查是否已经停止（避免重复调用）
        if not self.is_auto_inviting and not self.preparing_for_auto_invite:
            print(f"[DEBUG AutoInvite] 已经停止，忽略重复的停止请求 (Reason: {reason})")
            return

        # 检查是否是店铺切换请求
        is_shop_switch = hasattr(self, 'switch_shop_requested') and self.switch_shop_requested
        print(f"[DEBUG AutoInvite] 当前是否为店铺切换请求: {is_shop_switch}")

        # 如果是店铺切换请求且不是真正的手动停止，需要特殊处理
        if is_shop_switch and reason != "手动停止" and not finished_naturally:
            print(f"[DEBUG AutoInvite] 检测到店铺切换请求，记录当前店铺状态但不停止邀约流程")
            # 记录当前店铺的状态，但不停止整个邀约流程
            if self.current_shop_name:
                # 根据不同的停止原因设置不同的状态
                if reason in ["无法获取Cookie或启动加载失败", "商品列表加载失败", "达人列表加载失败"]:
                    status = reason
                elif "达到上限" in reason or "店铺掉线" in reason:
                    status = reason
                else:
                    status = reason

                self.current_shop_invite_status["status"] = status
                self.current_shop_invite_status["recorded"] = False

                # 只有在确实需要记录时才记录
                should_record = False
                if reason in ["无法获取Cookie或启动加载失败", "商品列表加载失败", "达人列表加载失败"]:
                    if self.current_shop_invite_status["count"] > 0:
                        should_record = True
                        print(f"[邀约日志] 店铺 '{self.current_shop_name}' 加载失败但已有邀约记录，将记录到日志")
                    else:
                        print(f"[邀约日志] 店铺 '{self.current_shop_name}' 加载失败且无邀约记录，不记录到日志")
                else:
                    should_record = True

                if should_record:
                    print(f"[邀约日志] 将当前店铺 '{self.current_shop_name}' 的邀约情况写入日志，状态: {status}")
                    InviteLogDialog.add_log_entry(
                        self.current_shop_name,
                        status,
                        self.current_shop_invite_status["count"]
                    )
                    self.current_shop_invite_status["recorded"] = True
            return

        # 如果是手动停止，强制设置标志位
        if reason == "手动停止":
            print(f"[DEBUG AutoInvite] 手动停止请求，强制设置is_auto_inviting=False")
            self.is_auto_inviting = False

        # 如果是因为所有店铺已完成而停止，设置通知标志防止重复处理，并强制停止邀约
        if reason == "所有店铺已邀约完成":
            print(f"[DEBUG AutoInvite] 所有店铺已邀约完成，强制设置is_auto_inviting=False")
            # 注意：不在这里设置 all_shops_completed_notified，因为统一处理方法已经设置了
            self.is_auto_inviting = False  # 强制停止邀约流程
        elif reason != "所有店铺已邀约完成":
            self.all_shops_completed_notified = False

        # 更新当前店铺的邀约状态（只有在非店铺切换情况下才处理）
        if self.current_shop_name and not (is_shop_switch and reason != "手动停止" and not finished_naturally):
            # 根据不同的停止原因设置不同的状态
            if finished_naturally:
                status = "完成邀约"
            elif reason == "所有店铺已邀约完成":
                status = "完成邀约"
            elif reason == "手动停止":
                status = "手动停止"
            elif reason in ["无法获取Cookie或启动加载失败", "商品列表加载失败", "达人列表加载失败"]:
                # 对于加载失败的情况，记录具体的失败原因
                status = reason
            elif "达到上限" in reason or "店铺掉线" in reason:
                status = reason
            else:
                # 其他情况也不使用"停止邀约"，而是记录具体原因
                status = reason

            self.current_shop_invite_status["status"] = status
            self.current_shop_invite_status["recorded"] = False

            # 将当前店铺的邀约情况写入日志，确保最后一个店铺也被记录
            # 这对于没有下一个店铺可切换的情况特别重要
            # 但是对于加载失败的情况，只有在确实需要记录时才记录
            should_record = False
            if reason == "所有店铺已邀约完成":
                should_record = True
            elif not self.current_shop_invite_status["recorded"]:
                # 如果是加载失败且邀约数量为0，说明店铺没有真正开始邀约，不记录
                if reason in ["无法获取Cookie或启动加载失败", "商品列表加载失败", "达人列表加载失败"]:
                    if self.current_shop_invite_status["count"] > 0:
                        should_record = True
                        print(f"[邀约日志] 店铺 '{self.current_shop_name}' 加载失败但已有邀约记录，将记录到日志")
                    else:
                        print(f"[邀约日志] 店铺 '{self.current_shop_name}' 加载失败且无邀约记录，不记录到日志")
                else:
                    should_record = True

            if should_record:
                print(f"[邀约日志] 将当前店铺 '{self.current_shop_name}' 的邀约情况写入日志，状态: {status}")
                InviteLogDialog.add_log_entry(
                    self.current_shop_name,
                    status,
                    self.current_shop_invite_status["count"]
                )
                self.current_shop_invite_status["recorded"] = True

                # 记录日志后，检查所有店铺是否都已完成
                if self._check_all_shops_in_log():
                    print("[邀约日志] 记录店铺后，所有店铺都已在日志中有记录，邀约完成")
                    # 如果还没有处理过所有店铺完成，则处理
                    if not (hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified):
                        self._handle_all_shops_completed("_stop_auto_invitation-记录后检查")

        # --- 修改: 添加 skipped_shops_info 检查和提示 ---
        skipped_message = ""
        if self.skipped_shops_info:
            skipped_message = "\n\n在本次运行中，以下店铺被跳过：\n"
            for shop, cause in self.skipped_shops_info.items():
                 skipped_message += f"- {shop}: {cause}\n"
            # 清空记录，以便下次启动
            print("[AutoInvite Stop] 清空店铺跳过记录。")
            self.skipped_shops_info.clear()

        # 检查是否需要更新按钮状态（无论是否在邀约中都需要处理）
        should_update_buttons = True

        # 特殊处理：如果是所有店铺已邀约完成，强制更新按钮状态并停止邀约流程
        if reason == "所有店铺已邀约完成":
            print(f"[AutoInvite Stop] 所有店铺已邀约完成，强制停止邀约流程并更新按钮状态")
            should_update_buttons = True
            # 强制停止邀约流程
            self.is_auto_inviting = False
            self.preparing_for_auto_invite = False
            # 重置行号
            if finished_naturally:
                print("[AutoInvite Stop] 所有店铺邀约完成，重置行号为 0.")
                self.current_auto_invite_row = 0
        elif self.is_auto_inviting or self.preparing_for_auto_invite:
            # 检查是否是真正的手动停止
            if reason == "手动停止":
                print(f"[AutoInvite Stop] 检测到手动停止，强制停止所有邀约流程")
                # 强制停止所有邀约流程，无论是否有店铺切换标志
                self.is_auto_inviting = False
                self.preparing_for_auto_invite = False
                # 清除店铺切换标志，避免后续恢复
                self.switch_shop_requested = False
                print(f"[AutoInvite Stop] 已清除switch_shop_requested标志，避免后续恢复邀约")
                should_update_buttons = True  # 手动停止时必须更新按钮状态
            # 检查是否是因为店铺切换而停止（非手动停止）
            elif self.switch_shop_requested and not finished_naturally:
                print(f"[AutoInvite Stop] 因店铺切换而停止 (原因: {reason})，保持自动邀约状态")
                # 保持 is_auto_inviting 为 True，只重置准备状态
                self.preparing_for_auto_invite = False
                # 保留当前行号
                print(f"[AutoInvite Stop] 保留当前行号: {self.current_auto_invite_row}")
                should_update_buttons = False  # 店铺切换时不更新按钮状态
            elif reason == "商品列表加载失败" or reason == "达人列表加载失败":
                print(f"[AutoInvite Stop] 因 {reason} 而停止，保持自动邀约状态以便切换店铺后继续")
                # 保持 is_auto_inviting 为 True，只重置准备状态
                self.preparing_for_auto_invite = False
                # 不设置切换店铺标志，避免循环切换
                # self.switch_shop_requested = True  # 注释掉这行，避免循环切换
                # 保留当前行号
                print(f"[AutoInvite Stop] 保留当前行号: {self.current_auto_invite_row}")
            else:
                # 正常停止流程
                self.is_auto_inviting = False
                self.preparing_for_auto_invite = False

                # 强制停止邀约线程
                if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
                    print(f"[AutoInvite Stop] 检测到邀约线程正在运行，强制停止...")

                    # 设置所有等待事件，让线程能够继续执行并退出
                    if hasattr(self, 'invitation_wait_event'):
                        self.invitation_wait_event.set()
                        print(f"[AutoInvite Stop] 已设置invitation_wait_event")
                    if hasattr(self, 'page_load_event'):
                        self.page_load_event.set()
                        print(f"[AutoInvite Stop] 已设置page_load_event")

                    # 等待线程退出
                    print(f"[AutoInvite Stop] 等待邀约线程退出...")
                    self.auto_invitation_thread.join(3.0)  # 最多等待3秒

                    if self.auto_invitation_thread.is_alive():
                        print(f"[AutoInvite Stop] 警告：邀约线程在3秒内未退出")
                    else:
                        print(f"[AutoInvite Stop] 邀约线程已成功退出")

                if finished_naturally:
                    print("[AutoInvite Stop] 邀约自然完成，重置行号为 0.")
                    self.current_auto_invite_row = 0
                else:
                    print(f"[AutoInvite Stop] 邀约未自然完成 (原因: {reason})，保留当前行号: {self.current_auto_invite_row}")

                # 如果是手动停止，清除 original_shop_hit_limit
                if reason == "手动停止":
                    print("[AutoInvite Stop] 手动停止，清除 original_shop_hit_limit")
                    self.original_shop_hit_limit = None

        # 更新按钮状态
        if should_update_buttons:
            self.start_invite_btn.setEnabled(True)
            if finished_naturally or reason == "所有店铺已邀约完成":
                self.start_invite_btn.setText("开始邀约(已完成)")
            elif reason == "手动停止": # 检查是否是手动停止
                self.start_invite_btn.setText("开始邀约(已停止)")
            else: # 其他原因导致的停止 (错误、切换等)
                self.start_invite_btn.setText("开始邀约(已中断)") # 可以用更明确的文本
            self.stop_invite_btn.setEnabled(False)
        else:
            print("[AutoInvite Stop] 因店铺切换而停止，保持按钮状态不变")
            # 确保停止按钮是启用的
            self.stop_invite_btn.setEnabled(True)

        # 停止商品加载和达人加载线程（只有在真正停止时才停止加载线程）
        if should_update_buttons:
            print(f"[AutoInvite Stop] 停止商品加载和达人加载线程...")
            self._stop_product_loading_threads()
            self._stop_influencer_loading_threads()

        # --- 新增: 只有在手动停止时才显示提示框，所有店铺邀约完成时不显示弹窗 ---
        if reason == "手动停止":
             stop_message = "自动邀约已手动停止。"
             if skipped_message:
                 QMessageBox.information(self, "自动邀约已停止", stop_message + skipped_message)
             else:
                 QMessageBox.information(self, "自动邀约已停止", stop_message)
        # 删除这部分代码，因为我们已经在检测到所有店铺邀约完成时显示弹窗了
        elif skipped_message and not finished_naturally:
             # 如果是因为错误自动停止且有跳过信息，也提示一下？或者只日志记录？
             # 暂时只在手动停止时弹框，其他情况日志已记录
             print("[AutoInvite Stop] 自动邀约因错误停止。跳过信息见日志。")

        else:
            print("[DEBUG AutoInvite] _stop_auto_invitation called but not currently inviting or preparing.")
            # --- 新增: 如果不在邀约中，但仍有跳过信息（异常情况），也显示并清除 ---
            if skipped_message:
                 QMessageBox.information(self, "提示", "检测到上次运行中跳过的店铺信息：" + skipped_message)
                 # 确保清除
                 if not self.skipped_shops_info.keys(): # Check if already cleared by main logic
                      print("[AutoInvite Stop] 清空残留的店铺跳过记录。")
                      self.skipped_shops_info.clear()
            # --- 结束新增 ---

    # --- 新增: 邀约等待事件 --- #
    invitation_wait_event = threading.Event()
    invitation_result = {"row": -1, "success": False, "message": "", "error_code": 0}

    def _try_random_fans_filter(self):
        """
        尝试随机选择一个不同的粉丝数筛选条件
        返回 True 表示成功切换并重新加载，False 表示所有选项均已尝试
        """
        print("[随机粉丝数筛选] 开始尝试随机选择粉丝数筛选条件")
        
        # 获取当前选中的粉丝数选项
        current_fans_option = None
        fans_buttons = [
            self.fans_unlimited,
            self.fans_less_1w,
            self.fans_1w_10w,
            self.fans_10w_50w,
            self.fans_more_50w
        ]
        
        for button in fans_buttons:
            if button.isChecked():
                current_fans_option = button.text()
                break
        
        print(f"[随机粉丝数筛选] 当前选中的粉丝数选项: {current_fans_option}")
        
        # 初始化已尝试的粉丝数选项记录（如果不存在）
        if not hasattr(self, '_tried_fans_options'):
            self._tried_fans_options = set()
        
        # 将当前选项加入已尝试列表
        if current_fans_option:
            self._tried_fans_options.add(current_fans_option)
            print(f"[随机粉丝数筛选] 将当前选项 '{current_fans_option}' 加入已尝试列表")
        
        # 获取所有可用的粉丝数选项
        all_fans_options = [button.text() for button in fans_buttons]
        
        # 找出还未尝试的选项
        untried_options = [option for option in all_fans_options if option not in self._tried_fans_options]
        
        print(f"[随机粉丝数筛选] 所有粉丝数选项: {all_fans_options}")
        print(f"[随机粉丝数筛选] 已尝试的选项: {list(self._tried_fans_options)}")
        print(f"[随机粉丝数筛选] 未尝试的选项: {untried_options}")
        
        if not untried_options:
            print("[随机粉丝数筛选] 所有粉丝数选项均已尝试")
            # 清空已尝试记录，为下一轮重置
            self._tried_fans_options.clear()
            return False
        
        # 随机选择一个未尝试的选项
        import random
        selected_option = random.choice(untried_options)
        print(f"[随机粉丝数筛选] 随机选择新的粉丝数选项: {selected_option}")
        
        # 在主线程中切换粉丝数选项
        try:
            # 使用 QMetaObject.invokeMethod 在主线程中执行
            from PyQt5.QtCore import QMetaObject, Qt
            QMetaObject.invokeMethod(self, "_switch_fans_filter_in_main_thread", 
                                   Qt.QueuedConnection,
                                   Q_ARG(str, selected_option))
            
            # 等待切换完成和数据重新加载
            if hasattr(self, 'fans_filter_switch_event'):
                self.fans_filter_switch_event.clear()
            else:
                import threading
                self.fans_filter_switch_event = threading.Event()
            
            print("[随机粉丝数筛选] 等待粉丝数筛选切换完成...")
            
            # 等待最多30秒
            wait_timeout = 30
            if self.fans_filter_switch_event.wait(timeout=wait_timeout):
                print("[随机粉丝数筛选] 粉丝数筛选切换完成")
                return True
            else:
                print("[随机粉丝数筛选] 粉丝数筛选切换超时")
                return False
                
        except Exception as e:
            print(f"[随机粉丝数筛选] 切换粉丝数选项时出错: {e}")
            return False

    def _try_random_fans_filter_for_single_shop(self):
        """
        为单店邀约尝试随机选择一个不同的粉丝数筛选条件
        返回 True 表示成功切换并重新加载，False 表示所有选项均已尝试
        """
        print("[单店邀约随机粉丝数筛选] 开始尝试随机选择粉丝数筛选条件")
        
        # 获取当前选中的粉丝数选项
        current_fans_option = None
        fans_buttons = [
            self.fans_unlimited,
            self.fans_less_1w,
            self.fans_1w_10w,
            self.fans_10w_50w,
            self.fans_more_50w
        ]
        
        for button in fans_buttons:
            if button.isChecked():
                current_fans_option = button.text()
                break
        
        # 为单店邀约使用单独的已尝试选项记录
        if not hasattr(self, '_tried_fans_options_single_shop'):
            self._tried_fans_options_single_shop = set()
        
        # 添加当前选项到已尝试列表
        if current_fans_option:
            self._tried_fans_options_single_shop.add(current_fans_option)
            print(f"[单店邀约随机粉丝数筛选] 当前选项: {current_fans_option}")
        
        # 获取所有可用的粉丝数选项
        all_options = [button.text() for button in fans_buttons]
        
        # 获取未尝试的选项
        untried_options = [option for option in all_options if option not in self._tried_fans_options_single_shop]
        
        print(f"[单店邀约随机粉丝数筛选] 所有选项: {all_options}")
        print(f"[单店邀约随机粉丝数筛选] 已尝试选项: {list(self._tried_fans_options_single_shop)}")
        print(f"[单店邀约随机粉丝数筛选] 未尝试选项: {untried_options}")
        
        if not untried_options:
            print("[单店邀约随机粉丝数筛选] 所有粉丝数选项均已尝试，无法切换")
            return False
        
        # 随机选择一个未尝试的选项
        import random
        selected_option = random.choice(untried_options)
        self._tried_fans_options_single_shop.add(selected_option)
        
        print(f"[单店邀约随机粉丝数筛选] 随机选择了: {selected_option}")
        
        # 找到对应的按钮并切换
        for button in fans_buttons:
            if button.text() == selected_option:
                # 使用主线程安全的方式切换选项
                QMetaObject.invokeMethod(self, "_switch_fans_filter_for_single_shop", Qt.QueuedConnection, Q_ARG(str, selected_option))
                return True
        
        print(f"[单店邀约随机粉丝数筛选] 未找到对应的按钮: {selected_option}")
        return False

    @pyqtSlot(str)
    def _switch_fans_filter_for_single_shop(self, option_text):
        """
        为单店邀约在主线程中切换粉丝数筛选选项
        """
        print(f"[单店邀约主线程-粉丝数筛选] 开始切换到选项: {option_text}")
        
        try:
            # 找到对应的按钮并选中
            fans_buttons = [
                self.fans_unlimited,
                self.fans_less_1w,
                self.fans_1w_10w,
                self.fans_10w_50w,
                self.fans_more_50w
            ]
            
            for button in fans_buttons:
                if button.text() == option_text:
                    print(f"[单店邀约主线程-粉丝数筛选] 选中按钮: {option_text}")
                    button.setChecked(True)
                    break
            
            # 强制处理事件，确保UI更新
            QApplication.processEvents()
            
            # 重新加载达人数据
            print("[单店邀约主线程-粉丝数筛选] 开始重新加载达人数据")
            self._reload_influencers_for_single_shop()
            
        except Exception as e:
            print(f"[单店邀约主线程-粉丝数筛选] 切换粉丝数选项时出错: {e}")

    def _reload_influencers_for_single_shop(self):
        """
        单店邀约粉丝数筛选变更后重新加载达人数据
        """
        print("[单店邀约达人重新加载] 开始重新加载达人数据")
        
        try:
            # 清空当前达人表格
            self.table.setRowCount(0)
            self.current_influencer_page = 0
            
            # 重置页码为第一页
            if hasattr(self, 'current_page'):
                self.current_page = 1
            
            # 重置单店邀约的行索引
            self.current_single_shop_invite_row = 0
            
            # 触发达人数据加载
            print("[单店邀约达人重新加载] 触发达人数据加载")
            self.on_load_influencers_clicked()
            
        except Exception as e:
            print(f"[单店邀约达人重新加载] 重新加载达人数据时出错: {e}")

    @pyqtSlot(str)
    def _switch_fans_filter_in_main_thread(self, option_text):
        """
        在主线程中切换粉丝数筛选选项
        """
        print(f"[主线程-粉丝数筛选] 开始切换到选项: {option_text}")
        
        try:
            # 找到对应的按钮并选中
            fans_buttons = [
                self.fans_unlimited,
                self.fans_less_1w,
                self.fans_1w_10w,
                self.fans_10w_50w,
                self.fans_more_50w
            ]
            
            for button in fans_buttons:
                if button.text() == option_text:
                    print(f"[主线程-粉丝数筛选] 选中按钮: {option_text}")
                    button.setChecked(True)
                    break
            
            # 强制处理事件，确保UI更新
            QApplication.processEvents()
            
            # 重新加载达人数据
            print("[主线程-粉丝数筛选] 开始重新加载达人数据")
            self._reload_influencers_after_filter_change()
            
        except Exception as e:
            print(f"[主线程-粉丝数筛选] 切换粉丝数选项时出错: {e}")
            # 即使出错也要设置事件，避免后台线程卡住
            if hasattr(self, 'fans_filter_switch_event'):
                self.fans_filter_switch_event.set()

    def _reload_influencers_after_filter_change(self):
        """
        粉丝数筛选变更后重新加载达人数据
        """
        print("[达人重新加载] 开始重新加载达人数据")
        
        try:
            # 清空当前达人表格
            self.table.setRowCount(0)
            self.current_influencer_page = 0
            
            # 重置页码为第一页
            if hasattr(self, 'current_page'):
                self.current_page = 1
            
            # 触发达人数据加载
            print("[达人重新加载] 触发达人数据加载")
            self.on_load_influencers_clicked()
            
            # 启动监控线程，等待加载完成
            import threading
            monitor_thread = threading.Thread(target=self._monitor_influencer_reload_completion, daemon=True)
            monitor_thread.start()
            
        except Exception as e:
            print(f"[达人重新加载] 重新加载达人数据时出错: {e}")
            if hasattr(self, 'fans_filter_switch_event'):
                self.fans_filter_switch_event.set()

    def _monitor_influencer_reload_completion(self):
        """
        监控达人重新加载是否完成
        """
        print("[达人重新加载监控] 开始监控达人重新加载进度")
        
        import time
        max_wait_time = 25  # 最多等待25秒
        check_interval = 0.5  # 每0.5秒检查一次
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # 检查是否加载完成（表格有数据且不在加载状态）
            if (self.table.rowCount() > 0 and 
                not getattr(self, 'loading_influencers', False) and
                hasattr(self, 'influencer_list_btn') and
                self.influencer_list_btn.text() != "加载中..."):
                
                print(f"[达人重新加载监控] 达人数据重新加载完成，共 {self.table.rowCount()} 行")
                # 设置完成事件
                if hasattr(self, 'fans_filter_switch_event'):
                    self.fans_filter_switch_event.set()
                return
            
            # 检查是否停止邀约（避免无限等待）
            if not getattr(self, 'is_auto_inviting', False):
                print("[达人重新加载监控] 检测到自动邀约已停止，结束监控")
                if hasattr(self, 'fans_filter_switch_event'):
                    self.fans_filter_switch_event.set()
                return
            
            time.sleep(check_interval)
        
        print("[达人重新加载监控] 达人重新加载超时")
        # 超时也要设置事件，避免后台线程卡住
        if hasattr(self, 'fans_filter_switch_event'):
            self.fans_filter_switch_event.set()

    # +++ 重写: 自动邀约工作线程 +++
    def _auto_invitation_worker(self):
        """后台线程：执行自动邀约循环 (支持自动翻页)"""
        interrupted = False
        processed_in_session = 0  # 本次自动邀约总共处理的数量

        print(f"[自动邀约线程] 开始处理，起始行索引: {self.current_auto_invite_row}")
        print("[自动邀约线程] 开始处理...")

        # 添加线程ID，便于调试
        import threading
        thread_id = threading.get_ident()
        print(f"[自动邀约线程] 线程ID: {thread_id}")

        while self.is_auto_inviting:
            # 1. 检查表格行数
            try:
                current_table_rows = self.table.rowCount()
                if current_table_rows == 0:
                    print("[自动邀约线程] 表格为空，无法继续邀约")
                    interrupted = True
                    break
            except Exception as e:
                print(f"[自动邀约线程] 错误：读取表格行数失败: {e}，中断邀约")
                interrupted = True
                break

            # 2. 检查当前行索引是否超出了当前表格的行数
            if self.current_auto_invite_row >= current_table_rows:
                print(f"[自动邀约线程] 处理完当前页 (共 {current_table_rows} 行)，检查是否需要翻页...")

                # 检查是否可以翻页
                can_go_next = False
                try:
                    # 检查是否有下一页按钮并且是否启用
                    if hasattr(self, 'prev_page_btn'):
                        can_go_next = self.prev_page_btn.isEnabled()
                        print(f"[自动邀约线程] 下一页按钮状态: {'启用' if can_go_next else '禁用'}")
                    else:
                        # 如果没有next_page_btn属性，尝试检查是否有其他翻页按钮
                        if hasattr(self, 'page_input'):
                            # 获取当前页码
                            current_page = int(self.page_input.text()) if self.page_input.text().isdigit() else 0
                            # 假设总是可以翻页，除非当前页码为0
                            can_go_next = current_page > 0
                            print(f"[自动邀约线程] 通过页码输入框判断翻页状态: 当前页码={current_page}, 可翻页={can_go_next}")
                        else:
                            print("[自动邀约线程] 未找到翻页按钮或页码输入框，假设不能翻页")
                except Exception as e:
                    print(f"[自动邀约线程] 错误：检查翻页状态失败: {e}，假设不能翻页")
                    can_go_next = False

                if can_go_next:
                    print("[自动邀约线程] 检测到可以翻页，请求主线程加载下一页...")
                    # 清除事件，准备等待
                    self.page_load_event.clear()
                    # 发送翻页请求
                    try:
                        # 检查是否有翻页信号
                        if hasattr(self, 'request_next_page_signal'):
                            self.request_next_page_signal.emit()
                            print("[自动邀约线程] 已发送翻页请求信号")
                        else:
                            # 如果没有翻页信号，尝试直接调用翻页方法
                            print("[自动邀约线程] 未找到翻页信号，尝试直接调用翻页方法")
                            # 在主线程中执行翻页操作
                            QMetaObject.invokeMethod(self, "on_next_influencer_page_clicked", Qt.QueuedConnection)
                    except Exception as e:
                        print(f"[自动邀约线程] 发送翻页请求时出错: {e}")
                        # 设置页面加载事件，避免线程卡住
                        self.page_load_event.set()

                    # 等待页面加载完成
                    print("[自动邀约线程] 等待下一页加载...")
                    wait_start_time = time.time()
                    max_wait_time = 30  # 最多等待30秒

                    while not self.page_load_event.wait(timeout=0.1):  # 每0.1秒检查一次，以便能更快响应停止请求
                        # 检查是否超时
                        if time.time() - wait_start_time > max_wait_time:
                            print(f"[自动邀约线程] 等待翻页超时 ({max_wait_time}秒)，中断邀约")
                            interrupted = True
                            break

                        # 检查是否被停止
                        if not self.is_auto_inviting:
                            # 检查是否是店铺切换请求
                            if not getattr(self, 'switch_shop_requested', False):
                                interrupted = True
                                print("[自动邀约线程] 在等待翻页时检测到停止请求")
                                break
                            else:
                                print("[自动邀约线程] 在等待翻页时检测到店铺切换请求，不中断邀约")

                    if interrupted:
                        break  # 跳出外层while循环

                    # 检查是否因为随机切换粉丝数属性而重置了页码
                    if self.current_influencer_page == 1:
                        print("[自动邀约线程] 检测到页码已重置为1，重置行索引...")
                        self.current_auto_invite_row = 0  # 重置行索引

                    # 检查翻页后的表格状态
                    try:
                        new_row_count = self.table.rowCount()
                        if new_row_count == 0:
                            print("[自动邀约线程] 翻页后表格为空，结束邀约")
                            break
                    except Exception as e:
                        print(f"[自动邀约线程] 检查翻页后表格状态失败: {e}，中断邀约")
                        interrupted = True
                        break

                    print("[自动邀约线程] 下一页加载完成，重置行索引")
                    self.current_auto_invite_row = 0  # 重置行索引，从新页面的第一行开始
                    continue  # 继续下一次循环，处理新页面的第一行
                else:
                    print("[自动邀约线程] 当前页已处理完毕，且无法翻页，尝试随机切换粉丝数筛选条件")
                    # 尝试随机选择一个不同的粉丝数选项并重新加载达人
                    if self._try_random_fans_filter():
                        print("[自动邀约线程] 已随机切换粉丝数筛选条件，重新从第一页开始")
                        self.current_auto_invite_row = 0  # 重置行索引，从第一行开始
                        continue  # 继续外层循环，处理新加载的达人列表
                    else:
                        print("[自动邀约线程] 所有粉丝数选项均已尝试，邀约完成")
                        break  # 跳出外层while循环

            # --- 修改: 不再检查状态列，避免隔行发送邀约 --- #
            # 注释掉原有代码，不再跳过已有状态的行
            # try:
            #     status_item = self.table.item(self.current_auto_invite_row, 3) # 状态列索引为 3
            #     if status_item and status_item.text().strip(): # 检查item是否存在且文本非空
            #         print(f"[自动邀约线程] 第 {self.current_auto_invite_row + 1} 行状态为 '{status_item.text()}'，跳过。")
            #         self.current_auto_invite_row += 1
            #         continue # 跳到循环的下一次迭代
            # except Exception as e:
            #     print(f"[自动邀约线程] 检查行 {self.current_auto_invite_row + 1} 状态时出错: {e}，将尝试邀约。")
            # --- 结束修改 --- #

            # 3. 处理当前行的达人
            print(f"[自动邀约线程] 准备邀约当前页第 {self.current_auto_invite_row + 1} 行达人...")

            # 获取延时设置
            min_delay = self.interval_min.value()
            max_delay = self.interval_max.value()
            if max_delay < min_delay:
                max_delay = min_delay
            delay_seconds = 0
            if min_delay > 0 or max_delay > 0:
                delay_seconds = random.uniform(min_delay, max_delay)

            # 保存当前要处理的行号
            current_row = self.current_auto_invite_row
            print(f"[自动邀约线程] 发送邀约信号 (行号: {current_row})")

            # 重置等待事件
            self.invitation_wait_event.clear()

            # 发送信号到主线程执行邀约
            self.invite_influencer_signal.emit(current_row)
            processed_in_session += 1

            # 等待邀约结果返回
            print(f"[自动邀约线程] 等待邀约结果返回...")
            wait_timeout = 30  # 最多等待30秒
            wait_start_time = time.time()

            # 使用循环等待，每0.1秒检查一次，以便能更快响应停止请求
            while not self.invitation_wait_event.is_set() and time.time() - wait_start_time < wait_timeout:
                # 短暂等待，避免CPU占用过高，但不要太长，以便能及时响应停止请求
                self.invitation_wait_event.wait(0.1)

                # 检查是否请求停止邀约
                if not self.is_auto_inviting:
                    # 检查是否是店铺切换请求
                    if not getattr(self, 'switch_shop_requested', False):
                        print("[自动邀约线程] 在等待邀约结果期间检测到停止请求")
                        interrupted = True
                        break
                    else:
                        print("[自动邀约线程] 在等待邀约结果期间检测到店铺切换请求，不中断邀约")

                # 检查是否所有店铺都已完成
                if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
                    print("[自动邀约线程] 在等待邀约结果期间检测到所有店铺已完成，立即停止")
                    interrupted = True
                    break

            # 检查是否收到结果
            result_received = self.invitation_wait_event.is_set()
            if not result_received:
                print(f"[自动邀约线程] 等待邀约结果超时 ({wait_timeout}秒)，继续处理下一个达人")
                # 强制设置等待事件，确保不会卡住
                self.invitation_wait_event.set()
            else:
                print(f"[自动邀约线程] 收到邀约结果: 成功={self.invitation_result.get('success', False)}, 消息='{self.invitation_result.get('message', '未知')}'")

                # 检查是否达到上限
                if self.invitation_result.get('error_code') == 13002:
                    print(f"[自动邀约线程] 检测到错误码13002（达到上限），等待主线程处理店铺切换...")
                    # 主线程的_handle_invitation_result方法会处理店铺切换
                    # 这里只需要检查是否设置了切换标志
                    if getattr(self, 'switch_shop_requested', False):
                        print(f"[自动邀约线程] 检测到店铺切换请求，中断当前邀约循环")
                        interrupted = True
                        break

                # 不再检查店铺掉线标志

                # 检查是否是错误码13003（达人不接受邀约）
                if self.invitation_result.get('error_code') == 13003:
                    print(f"[自动邀约线程] 检测到错误码13003（达人不接受邀约），继续处理下一个达人")
                    # 继续处理下一个达人，不中断循环
                    self.current_auto_invite_row += 1
                    processed_in_session += 1

                    # 确保自动邀约标志仍然为True
                    if not self.is_auto_inviting:
                        print("[自动邀约线程] 检测到自动邀约标志为False，但这是错误码13003情况，重新设置为True")
                        self.is_auto_inviting = True

                    continue

            # 执行延时
            if delay_seconds > 0 and not interrupted:
                print(f"[自动邀约线程] 开始延时 {delay_seconds:.2f} 秒...")
                delay_start_time = time.time()

                while time.time() - delay_start_time < delay_seconds:
                    # 检查是否停止邀约
                    if not self.is_auto_inviting:
                        # 检查是否是店铺切换请求
                        if not getattr(self, 'switch_shop_requested', False):
                            interrupted = True
                            print("[自动邀约线程] 在延时期间检测到停止请求")
                            break
                        else:
                            print("[自动邀约线程] 在延时期间检测到店铺切换请求，不中断邀约")

                    # 检查是否所有店铺都已完成
                    if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
                        print("[自动邀约线程] 在延时期间检测到所有店铺已完成，立即停止")
                        interrupted = True
                        break

                    # 短暂休眠避免CPU占用过高，但不要太长，以便能及时响应停止请求
                    time.sleep(0.05)

            # 处理行号增加
            if interrupted:
                print(f"[自动邀约线程] 检测到中断信号，保持当前行号 {self.current_auto_invite_row}，退出循环")
                break  # 跳出外层while循环
            else:
                # 只有在没中断的情况下才增加行号
                print(f"[自动邀约线程] 邀约完成，将行号从 {self.current_auto_invite_row} 增加到 {self.current_auto_invite_row + 1}")
                self.current_auto_invite_row += 1

        # 检查是否可以翻页
        can_go_next = False
        try:
            # 检查"下一页"按钮是否启用
            if hasattr(self, 'prev_page_btn') and self.prev_page_btn.isEnabled():
                can_go_next = True
                print("[自动邀约线程] 检测到'下一页'按钮已启用，可以翻页")
            else:
                print("[自动邀约线程] '下一页'按钮未启用，无法翻页")
        except Exception as e:
            print(f"[自动邀约线程] 检查翻页状态时出错: {e}")
            can_go_next = False

        # 4. 循环结束后的处理
        if not interrupted:
            # 检查是否处理了所有达人
            if self.current_auto_invite_row >= self.table.rowCount() and not can_go_next:
                print(f"[自动邀约线程] 所有页面处理完毕，共处理 {processed_in_session} 个达人")
                # 自然完成时重置行号为0
                print("[自动邀约线程] 自然完成，重置行号为0")
                self.current_auto_invite_row = 0
            else:
                # 如果还有达人未处理，不要重置行号，也不要发送完成信号
                print(f"[自动邀约线程] 当前页面处理完毕，但还有更多达人需要处理，保持自动邀约状态")
                # 不重置行号，继续处理
                # 不发送完成信号，直接返回

                # 检查是否可以翻页
                if can_go_next:
                    print("[自动邀约线程] 检测到可以翻页，将继续处理下一页")
                    # 继续循环，不发送完成信号

                    # 清除事件，准备等待
                    self.page_load_event.clear()

                    # 尝试直接调用翻页方法
                    try:
                        # 检查是否有翻页信号
                        if hasattr(self, 'request_next_page_signal'):
                            self.request_next_page_signal.emit()
                            print("[自动邀约线程] 已发送翻页请求信号")
                        else:
                            # 如果没有翻页信号，尝试直接调用翻页方法
                            print("[自动邀约线程] 未找到翻页信号，尝试直接调用翻页方法")
                            # 在主线程中执行翻页操作
                            QMetaObject.invokeMethod(self, "on_next_influencer_page_clicked", Qt.QueuedConnection)
                    except Exception as e:
                        print(f"[自动邀约线程] 发送翻页请求时出错: {e}")
                        # 设置页面加载事件，避免线程卡住
                        self.page_load_event.set()

                    # 等待页面加载完成
                    print("[自动邀约线程] 等待下一页加载...")
                    wait_start_time = time.time()
                    max_wait_time = 30  # 最多等待30秒

                    while not self.page_load_event.wait(timeout=0.1):  # 每0.1秒检查一次，以便能更快响应停止请求
                        # 检查是否超时
                        if time.time() - wait_start_time > max_wait_time:
                            print(f"[自动邀约线程] 等待翻页超时 ({max_wait_time}秒)，中断邀约")
                            break

                        # 检查是否被停止
                        if not self.is_auto_inviting:
                            # 检查是否是店铺切换请求
                            if not getattr(self, 'switch_shop_requested', False):
                                print("[自动邀约线程] 在等待翻页时检测到停止请求")
                                break
                            else:
                                print("[自动邀约线程] 在等待翻页时检测到店铺切换请求，不中断邀约")

                        # 检查是否所有店铺都已完成
                        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
                            print("[自动邀约线程] 在等待翻页时检测到所有店铺已完成，立即停止")
                            break

                    # 检查是否成功翻页
                    if self.page_load_event.is_set():
                        print("[自动邀约线程] 翻页成功，将继续处理下一页")
                        self.current_auto_invite_row = 0  # 重置行索引
                        return
                    else:
                        print("[自动邀约线程] 翻页失败，继续执行完成流程")

                    return
                else:
                    # 尝试强制翻页
                    print("[自动邀约线程] 通过按钮状态判断无法翻页，但尝试强制翻页")
                    try:
                        # 清除事件，准备等待
                        self.page_load_event.clear()

                        # 尝试直接调用翻页方法
                        QMetaObject.invokeMethod(self, "on_next_influencer_page_clicked", Qt.QueuedConnection)

                        # 等待一小段时间看是否成功
                        wait_start = time.time()
                        max_wait = 10  # 增加等待时间到10秒

                        while not self.page_load_event.wait(timeout=0.1) and time.time() - wait_start < max_wait:
                            # 检查是否被停止
                            if not self.is_auto_inviting:
                                # 检查是否是店铺切换请求
                                if not getattr(self, 'switch_shop_requested', False):
                                    print("[自动邀约线程] 在强制翻页等待期间检测到停止请求")
                                    break
                                else:
                                    print("[自动邀约线程] 在强制翻页等待期间检测到店铺切换请求，不中断邀约")

                            # 检查是否所有店铺都已完成
                            if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
                                print("[自动邀约线程] 在强制翻页等待期间检测到所有店铺已完成，立即停止")
                                break

                        # 检查是否成功翻页
                        if self.page_load_event.is_set():
                            print("[自动邀约线程] 强制翻页成功，将继续处理下一页")
                            self.current_auto_invite_row = 0  # 重置行索引
                            return
                    except Exception as e:
                        print(f"[自动邀约线程] 强制翻页失败: {e}")

                    print("[自动邀约线程] 无法翻页，但行号超出范围，可能是表格状态异常")
                    # 重置行号为0，发送完成信号
                    self.current_auto_invite_row = 0
        else:
            print(f"[自动邀约线程] 自动邀约被中断，共处理 {processed_in_session} 个达人，当前行号: {self.current_auto_invite_row}")
            # 中断时保留行号，不做任何重置

        # 发送完成信号到主线程
        self.auto_invite_finished_signal.emit(interrupted)

    def _handle_invite_request(self, row):
        """主线程槽函数：处理来自后台线程的邀约请求"""
        import time
        current_time = time.time()
        print(f"[主线程] 收到邀约请求，行号: {row + 1}，时间戳: {current_time}")

        # 时间戳防重复检查（防止短时间内重复邀约）
        invite_key = f"row_{row}"
        if hasattr(self, '_last_invite_times') and invite_key in self._last_invite_times:
            time_diff = current_time - self._last_invite_times[invite_key]
            if time_diff < 2.0:  # 2秒内不允许重复邀约同一行
                print(f"[主线程] 检测到短时间内重复邀约请求，行号: {row + 1}，时间差: {time_diff:.2f}秒，忽略此次请求")
                return

        # 记录邀约时间
        if not hasattr(self, '_last_invite_times'):
            self._last_invite_times = {}
        self._last_invite_times[invite_key] = current_time

        # 强化防重复邀约检查
        if hasattr(self, '_current_inviting_row') and self._current_inviting_row is not None:
            print(f"[主线程] 检测到正在邀约行号 {self._current_inviting_row + 1}，新请求行号 {row + 1}")
            if self._current_inviting_row == row:
                print(f"[主线程] 检测到重复邀约请求，行号: {row + 1}，忽略此次请求")
                return
            else:
                print(f"[主线程] 检测到不同行号的邀约请求，但当前正在邀约，忽略新请求")
                return

        # 检查是否有正在进行的邀约（通过API锁检查）
        if hasattr(self, 'invitation_api_lock') and self.invitation_api_lock.locked():
            print(f"[主线程] 检测到API锁被占用，忽略邀约请求行号: {row + 1}")
            return

        # 设置当前正在邀约的行号
        self._current_inviting_row = row
        print(f"[主线程] 开始处理第 {row + 1} 行达人邀约...")

        try:
            # 检查是否是单店邀约模式
            if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
                print(f"[主线程] 单店邀约模式：同步处理第 {row + 1} 行达人邀约")
                # 单店邀约模式：同步处理，确保按顺序邀约
                self._invite_selected_influencer_sync(row)
            else:
                print(f"[主线程] 自动邀约模式：异步处理第 {row + 1} 行达人邀约")
                # 自动邀约模式：异步处理，保持原有逻辑
                self._invite_selected_influencer(row)
        finally:
            # 清除当前邀约行号
            self._current_inviting_row = None
            print(f"[主线程] 第 {row + 1} 行达人邀约处理完成，清除邀约标志")

        QApplication.processEvents() # 确保 UI 更新

    def _invite_selected_influencer_sync(self, row):
        """同步版本的邀约方法，用于单店邀约，确保按顺序处理"""
        print(f"[同步邀约] ========== 开始同步处理第 {row + 1} 行达人邀约 ==========")
        print(f"[同步邀约] 当前表格总行数: {self.table.rowCount()}")
        print(f"[同步邀约] 自动邀约行号: {getattr(self, 'current_auto_invite_row', '未知')}")
        print(f"[同步邀约] 单店邀约行号: {getattr(self, 'current_single_shop_invite_row', '未知')}")
        print(f"[同步邀约] 当前邀约模式: {'单店邀约' if (hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting) else '自动邀约'}")

        # 全局邀约锁检查
        if hasattr(self, '_global_inviting_lock') and self._global_inviting_lock:
            print(f"[同步邀约] 检测到全局邀约锁，跳过邀约请求行号: {row + 1}")
            self.invitation_result = {'error_code': -11, 'message': '全局邀约锁定中'}
            self.invitation_wait_event.set()
            return

        # 设置全局邀约锁
        self._global_inviting_lock = True
        print(f"[同步邀约] 设置全局邀约锁，开始处理第 {row + 1} 行")

        # 重置邀约结果
        self.invitation_result = {}

        # 验证行号有效性
        if row >= self.table.rowCount():
            print(f"[同步邀约] 错误: 行号 {row} 超出表格范围 {self.table.rowCount()}")
            self.invitation_result = {'error_code': -8, 'message': '行号超出范围'}
            self.invitation_wait_event.set()
            return

        # 获取达人信息
        influencer_id_item = self.table.item(row, 1) # 达人ID (finderUsername) 在第2列 (索引1)
        influencer_nickname_item = self.table.item(row, 2) # 达人昵称在第3列 (索引2)

        if not (influencer_id_item and influencer_nickname_item):
            print(f"[同步邀约] 错误: 无法获取行 {row+1} 的达人ID或昵称")
            print(f"[同步邀约] influencer_id_item: {influencer_id_item}")
            print(f"[同步邀约] influencer_nickname_item: {influencer_nickname_item}")
            # 设置邀约结果，让后台线程知道获取达人信息失败
            self.invitation_result = {'error_code': -2, 'message': '无法获取达人信息'}
            # 设置邀约等待事件，让后台线程继续
            self.invitation_wait_event.set()
            return

        finder_username = influencer_id_item.text()
        nickname = influencer_nickname_item.text()
        print(f"[同步邀约] 处理达人: {nickname} (ID={finder_username})")

        # 安全更新状态为"邀约中..."
        status_col_index = 3
        status_item = None
        try:
            # 首先检查行号是否仍然有效
            if row >= self.table.rowCount():
                print(f"[同步邀约] 警告：行号 {row} 已超出当前表格范围 {self.table.rowCount()}，跳过状态更新")
                self.invitation_result = {'error_code': -9, 'message': '表格行已被删除'}
                self.invitation_wait_event.set()
                return

            status_item = self.table.item(row, status_col_index)
            if not status_item:
                status_item = QTableWidgetItem("")
                self.table.setItem(row, status_col_index, status_item)
            status_item.setText("邀约中...")
            status_item.setToolTip("")
            QApplication.processEvents()
        except Exception as e:
            print(f"[同步邀约] 获取状态列时出错: {e}，可能表格已被修改")
            self.invitation_result = {'error_code': -10, 'message': f'获取状态列失败: {str(e)}'}
            self.invitation_wait_event.set()
            return

        try:
            # 获取商品列表
            item_list = []
            spu_id_col_index = 1
            all_spu_ids = []
            for r in range(self.selected_products_table.rowCount()):
                spu_id_item = self.selected_products_table.item(r, spu_id_col_index)
                if spu_id_item and spu_id_item.text():
                    all_spu_ids.append(spu_id_item.text())

            if not all_spu_ids:
                print(f"[同步邀约] 商品列表为空，跳过邀约")
                # 安全设置状态
                try:
                    if status_item:
                        status_item.setText("商品列表为空")
                except RuntimeError:
                    print(f"[同步邀约] 状态项已被删除，跳过状态更新")
                # 设置邀约结果，让后台线程知道商品列表为空
                self.invitation_result = {'error_code': -3, 'message': '商品列表为空'}
                self.invitation_wait_event.set()
                return

            # 获取邀约商品数并选择
            try:
                invite_count = int(self.invite_count_input.text())
                if invite_count <= 0: invite_count = 1
            except ValueError: invite_count = 30
            sample_size = min(len(all_spu_ids), invite_count)
            selected_spu_ids = random.sample(all_spu_ids, sample_size)
            item_list = [{"spuId": spu_id} for spu_id in selected_spu_ids]
            print(f"[同步邀约] 已随机选择 {len(item_list)} 个商品")

            # 标记已邀约商品
            self._mark_invited_products(selected_spu_ids)
            QApplication.processEvents()

            # 获取联系信息
            contact_name = self.contact_input.text().strip()
            contact_wx_id = self.wechat_input.text().strip()
            contact_phone = self.phone_input.text().strip()
            invitation_introduction = self.desc_text.toPlainText().strip()
            if not (contact_name and contact_wx_id and contact_phone and invitation_introduction):
                print(f"[同步邀约] 邀约设置信息不完整")
                # 安全设置状态
                try:
                    if status_item:
                        status_item.setText("邀约设置不完整")
                except RuntimeError:
                    print(f"[同步邀约] 状态项已被删除，跳过状态更新")
                # 设置邀约结果，让后台线程知道邀约设置不完整
                self.invitation_result = {'error_code': -4, 'message': '邀约设置不完整'}
                self.invitation_wait_event.set()
                return

            contact_info = {
                "contactName": contact_name,
                "contactWxId": contact_wx_id,
                "contactPhoneNumber": contact_phone,
                "invitationIntroduction": invitation_introduction,
                "inviteScene": "1"
            }

            # 获取认证信息
            account_info = ShopManager.get_shop_by_name(self.current_shop_name)
            if not account_info:
                print(f"[同步邀约] 错误：未找到店铺 '{self.current_shop_name}' 的信息")
                # 安全设置状态
                try:
                    if status_item:
                        status_item.setText("未找到店铺信息")
                except RuntimeError:
                    print(f"[同步邀约] 状态项已被删除，跳过状态更新")
                # 设置邀约结果，让后台线程知道未找到店铺信息
                self.invitation_result = {'error_code': -5, 'message': '未找到店铺信息'}
                self.invitation_wait_event.set()
                return

            access_token = account_info.get('accesstoken')
            cookie_input = account_info.get('cookies_list', [])
            if not access_token:
                print(f"[同步邀约] 错误：店铺 '{self.current_shop_name}' 缺少access_token")
                # 安全设置状态
                try:
                    if status_item:
                        status_item.setText("缺少access_token")
                except RuntimeError:
                    print(f"[同步邀约] 状态项已被删除，跳过状态更新")
                # 设置邀约结果，让后台线程知道缺少access_token
                self.invitation_result = {'error_code': -6, 'message': '缺少access_token'}
                self.invitation_wait_event.set()
                return

            # 提取biz_magic
            biz_magic = None
            if isinstance(cookie_input, list):
                for cookie in cookie_input:
                    if isinstance(cookie, dict) and cookie.get('name') == 'biz_magic':
                        biz_magic = cookie.get('value')
                        break

            if not biz_magic:
                print(f"[同步邀约] 错误: 无法从 Cookie 中提取 biz_magic")
                # 安全设置状态
                try:
                    if status_item:
                        status_item.setText("biz_magic丢失")
                except RuntimeError:
                    print(f"[同步邀约] 状态项已被删除，跳过状态更新")
                # 设置邀约结果，让后台线程知道biz_magic丢失
                self.invitation_result = {'error_code': -7, 'message': 'biz_magic丢失'}
                self.invitation_wait_event.set()
                return

            # 同步调用API进行邀约
            print(f"[同步邀约] 开始调用API进行邀约...")
            api = WechatAPI()

            # 使用互斥锁确保同一时间只有一个线程调用API
            if not hasattr(self, 'invitation_api_lock'):
                self.invitation_api_lock = threading.Lock()

            # 检查是否已经在邀约同一个达人
            current_inviting_key = f"{finder_username}_{row}"
            if hasattr(self, '_current_api_inviting') and self._current_api_inviting == current_inviting_key:
                print(f"[同步邀约] 检测到正在邀约同一个达人，跳过重复API调用: {finder_username}")
                self.invitation_result = {'error_code': -9, 'message': '重复邀约请求'}
                self.invitation_wait_event.set()
                return

            with self.invitation_api_lock:
                # 设置当前正在邀约的达人标识
                self._current_api_inviting = current_inviting_key
                print(f"[同步邀约] 开始API调用，达人: {finder_username}, 行号: {row}")

                try:
                    result = api.create_promotion_invitation(
                        access_token=access_token,
                        biz_magic=biz_magic,
                        finder_username=finder_username,
                        item_list=item_list,
                        contact_info=contact_info,
                        cookie_input=cookie_input
                    )
                    print(f"[同步邀约] API调用完成，达人: {finder_username}, 结果: {result}")
                finally:
                    # 清除当前邀约标识
                    self._current_api_inviting = None

            # 处理邀约结果
            print(f"[同步邀约] API调用完成，结果: {result}")

            if result.get('success', False):
                # 邀约成功
                # 安全设置状态
                try:
                    if status_item:
                        status_item.setText("邀约成功")
                        status_item.setToolTip(f"邀约成功: {result.get('message', '成功')}")
                except RuntimeError:
                    print(f"[同步邀约] 状态项已被删除，跳过状态更新")

                # 更新当前店铺的邀约状态
                if self.current_shop_name:
                    self.current_shop_invite_status["status"] = "邀约成功"
                    self.current_shop_invite_status["count"] += 1
                    self.current_shop_invite_status["recorded"] = False

                # 设置邀约结果，让后台线程知道邀约成功
                self.invitation_result = {'success': True, 'message': result.get('message', '成功')}
                print(f"[同步邀约] 邀约成功: {result.get('message', '成功')}")
            else:
                # 邀约失败 - 修复错误码获取逻辑
                # API返回的错误码可能在'code'或'error_code'字段中
                error_code = result.get('code', result.get('error_code', 0))
                # 错误信息可能在'msg'或'message'字段中
                message = result.get('msg', result.get('message', '未知错误'))

                print(f"[同步邀约] 邀约失败，错误码: {error_code}, 错误信息: {message}")

                if error_code == 13002:
                    # 达到上限
                    # 安全设置状态
                    try:
                        if status_item:
                            status_item.setText("已达上限")
                            status_item.setToolTip(f"错误码: {error_code}\n错误信息: {message}")
                    except RuntimeError:
                        print(f"[同步邀约] 状态项已被删除，跳过状态更新")
                    print(f"[同步邀约] 检测到达到上限（错误码13002），立即停止单店邀约")

                    # 立即停止单店邀约
                    self.is_single_shop_inviting = False
                    print(f"[同步邀约] 已设置 is_single_shop_inviting = False，强制停止邀约循环")

                    # 更新店铺状态为"已达上限"
                    if self.current_shop_name and hasattr(self, 'current_shop_invite_status'):
                        self.current_shop_invite_status["status"] = "已达上限"
                        print(f"[同步邀约] 设置店铺 '{self.current_shop_name}' 状态为'已达上限'")

                    # 设置邀约结果，让后台线程知道达到上限
                    self.invitation_result = {'error_code': 13002, 'message': message}

                    # 重要：确保不会触发自动邀约流程
                    # 单店邀约达到上限后应该只停止单店邀约，不应该启动自动邀约
                    self.is_auto_inviting = False
                    self.preparing_for_auto_invite = False

                    # 清除所有可能触发自动邀约的标志
                    if hasattr(self, 'auto_adjust_page_for_invitation'):
                        self.auto_adjust_page_for_invitation = False
                    if hasattr(self, 'switch_shop_requested'):
                        self.switch_shop_requested = False
                    if hasattr(self, 'waiting_for_products_then_influencers'):
                        self.waiting_for_products_then_influencers = False
                    if hasattr(self, 'waiting_for_influencers_to_start_invite'):
                        self.waiting_for_influencers_to_start_invite = False

                    # 设置标志，表示按钮状态已经在达到上限时处理过了
                    self._button_state_handled_on_limit = True

                    # 立即更新按钮状态，恢复为正常状态
                    self.auto_invite_btn.setEnabled(True)
                    self.auto_invite_btn.setText("单店邀约")
                    self.stop_invite_btn.setEnabled(False)

                    # 恢复开始邀约按钮状态
                    if hasattr(self, 'start_invite_btn'):
                        self.start_invite_btn.setEnabled(True)
                        self.start_invite_btn.setText("开始邀约")

                    # 恢复加载达人按钮状态
                    if hasattr(self, 'add_to_list_btn'):
                        self.add_to_list_btn.setEnabled(True)
                        self.add_to_list_btn.setText("加载达人")

                    # 恢复商品加载按钮状态
                    if hasattr(self, 'product_list_btn'):
                        self.product_list_btn.setEnabled(True)
                        self.product_list_btn.setText("商品列表")

                    print(f"[同步邀约] 已恢复所有按钮状态为正常")

                    print(f"[同步邀约] 确保所有自动邀约相关标志为False，防止意外触发自动邀约")
                elif error_code == 13003:
                    # 达人不接受邀约
                    # 安全设置状态
                    try:
                        if status_item:
                            status_item.setText("不接受邀约")
                            status_item.setToolTip(f"错误码: {error_code}\n错误信息: {message}")
                    except RuntimeError:
                        print(f"[同步邀约] 状态项已被删除，跳过状态更新")
                    print(f"[同步邀约] 达人不接受邀约，继续下一个")
                    # 设置邀约结果，让后台线程知道达人不接受邀约
                    self.invitation_result = {'error_code': 13003, 'message': message}
                else:
                    # 其他错误
                    status_text = f"邀约失败({error_code})"
                    if message != "未知错误" and message != "请求未返回有效结果" and message != "程序错误":
                        status_text = f"邀约失败: {message[:15]}..."
                    # 安全设置状态
                    try:
                        if status_item:
                            status_item.setText(status_text)
                            status_item.setToolTip(f"错误码: {error_code}\n错误信息: {message}")
                    except RuntimeError:
                        print(f"[同步邀约] 状态项已被删除，跳过状态更新")
                    print(f"[同步邀约] 邀约失败: {message}")
                    # 设置邀约结果，让后台线程知道邀约失败
                    self.invitation_result = {'error_code': error_code, 'message': message}

        except Exception as e:
            print(f"[同步邀约] 邀约过程中发生异常: {e}")
            # 安全设置状态
            try:
                if status_item:
                    status_item.setText("邀约异常")
                    status_item.setToolTip(f"异常: {str(e)}")
            except RuntimeError:
                print(f"[同步邀约] 状态项已被删除，跳过状态更新")
            # 设置邀约结果，让后台线程知道发生异常
            self.invitation_result = {'error_code': -1, 'message': f"异常: {str(e)}"}

        finally:
            # 释放全局邀约锁
            if hasattr(self, '_global_inviting_lock'):
                self._global_inviting_lock = False
                print(f"[同步邀约] 释放全局邀约锁")

            # 确保邀约结果已设置
            if not hasattr(self, 'invitation_result') or not self.invitation_result:
                print(f"[同步邀约] 警告：邀约结果未设置，设置默认失败结果")
                self.invitation_result = {'error_code': -10, 'message': '邀约处理异常'}

            print(f"[同步邀约] ========== 第 {row + 1} 行达人邀约处理完成 ==========")
            print(f"[同步邀约] 邀约结果: {getattr(self, 'invitation_result', {})}")

            # 添加延时确保UI更新完成
            print(f"[同步邀约] 开始UI更新和延时...")
            QApplication.processEvents()
            time.sleep(0.3)  # 增加延时确保状态更新完成
            QApplication.processEvents()  # 再次处理事件

            print(f"[同步邀约] UI更新完成，通知后台线程继续...")
            self.invitation_wait_event.set()

    def _handle_auto_invite_finished(self, interrupted: bool):
        """主线程槽函数：处理后台自动邀约完成信号"""
        print(f"[主线程] 收到自动邀约完成信号 (interrupted={interrupted})")
        print(f"[主线程] 当前自动邀约标志状态: is_auto_inviting={self.is_auto_inviting}, switch_shop_requested={getattr(self, 'switch_shop_requested', False)}")

        # 检查是否有未完成的邀约请求
        if hasattr(self, 'invitation_wait_event') and not self.invitation_wait_event.is_set():
            print("[主线程] 检测到有未完成的邀约请求，设置邀约等待事件")
            self.invitation_wait_event.set()

        # 检查是否有未完成的页面加载请求
        if hasattr(self, 'page_load_event') and not self.page_load_event.is_set():
            print("[主线程] 检测到有未完成的页面加载请求，设置页面加载事件")
            self.page_load_event.set()

        # 检查是否之前请求了切换店铺
        if hasattr(self, 'switch_shop_requested') and self.switch_shop_requested:
            print("[主线程] 邀约完成，检测到切换店铺请求标志")

            # 店铺切换时不在这里记录日志，由_stop_current_shop_invitation_only方法负责记录
            # 避免重复记录和错误的"店铺切换 0"状态
            print(f"[邀约日志] 检测到店铺切换请求，跳过在_handle_auto_invite_finished中记录日志")
            print(f"[邀约日志] 店铺切换的日志记录由_stop_current_shop_invitation_only方法负责")

            # 检查所有店铺是否都已完成
            if self._check_all_shops_in_log():
                print("[邀约日志] 记录当前店铺后，所有店铺都已在日志中有记录，邀约完成")
                # 使用统一方法处理所有店铺完成
                self._handle_all_shops_completed("_handle_auto_invite_finished-切换店铺检查后")
            else:
                print("[主线程] 还有其他店铺需要邀约，保持自动邀约状态，等待店铺切换完成后恢复")
                # 重要：确保自动邀约标志保持为True
                # 这样在店铺切换完成后，可以继续邀约
                self.is_auto_inviting = True
                print("[主线程] 确保is_auto_inviting标志为True，以便在店铺切换后继续邀约")

                # 不更新按钮状态，保持"邀约中..."状态
                # 确保停止按钮是启用的
                self.stop_invite_btn.setEnabled(True)

                # 不要在这里重置switch_shop_requested标志
                # 它将在resume_auto_invite方法中使用，然后再重置
            return
        else:
            # 如果没有请求切换店铺，则正常重置状态
            print("[主线程] 没有检测到店铺切换请求，正常重置自动邀约状态")

            # 检查是否需要记录当前店铺的邀约情况到日志
            if self.current_shop_name and not self.current_shop_invite_status["recorded"]:
                print(f"[邀约日志] 邀约完成，将当前店铺 '{self.current_shop_name}' 的邀约情况写入日志")
                # 根据当前店铺的状态决定记录内容，不使用"停止邀约"
                if self.current_shop_invite_status["status"]:
                    status = self.current_shop_invite_status["status"]
                else:
                    # 只有在真正手动停止时才记录为"手动停止"
                    # 其他中断情况记录为"邀约中断"
                    if not interrupted:
                        status = "完成邀约"
                    else:
                        # 检查真正的停止原因
                        last_stop_reason = getattr(self, '_last_stop_reason', None)
                        if last_stop_reason == "手动停止":
                            status = "手动停止"
                        else:
                            # 不再检查switch_shop_requested，避免错误记录"店铺切换"
                            # 店铺切换的记录由专门的方法负责
                            status = "邀约中断"
                InviteLogDialog.add_log_entry(
                    self.current_shop_name,
                    status,
                    self.current_shop_invite_status["count"]
                )
                self.current_shop_invite_status["recorded"] = True

                # 记录日志后，检查所有店铺是否都已完成
                if self._check_all_shops_in_log():
                    print("[邀约日志] 记录最后一个店铺后，所有店铺都已在日志中有记录，邀约完成")
                    # 使用统一方法处理所有店铺完成
                    self._handle_all_shops_completed("_handle_auto_invite_finished-最后店铺记录后")

            self.is_auto_inviting = False
            self.preparing_for_auto_invite = False  # 确保准备状态也重置
            self.start_invite_btn.setEnabled(True)
            self.start_invite_btn.setText("开始邀约")
            self.stop_invite_btn.setEnabled(False)

        # 恢复加载按钮
        if self.product_list_btn.text() == "加载中...":
            self.product_list_btn.setEnabled(True)
            self.product_list_btn.setText("商品列表")
        if self.add_to_list_btn.text() == "加载中...":
            self.add_to_list_btn.setEnabled(True)
            self.add_to_list_btn.setText("加载达人")

        # --- 原始逻辑，根据是否中断显示不同消息 ---
        print("[主线程] 自动邀约正常完成或被手动停止。")
        if interrupted:
            # 如果是手动停止，也可能需要一个提示，但通常用户知道自己停止了
            print("[主线程] 自动邀约被手动停止。")
            # QMessageBox.information(self, "自动邀约停止", "自动邀约已被手动停止。")  # 可以选择性添加
        elif self.original_shop_hit_limit is not None and not interrupted:
            # 只有在非手动停止且有初始达到上限的店铺时才记录日志，但不显示弹窗
            print(f"[主线程] 自动邀约已遍历完所有店铺。初始达到上限的店铺: {self.original_shop_hit_limit}")
            self.original_shop_hit_limit = None  # Reset after logging
        elif not interrupted and self.current_auto_invite_row == 0:  # 只有在非中断且行号为0（表示自然完成）时才记录日志，但不显示弹窗
            print("[主线程] 所有达人的自动邀约已完成。")
        # +++ 结束新增 +++

    def _reset_auto_invite_state(self):
        """重置自动邀约状态"""
        self.is_auto_inviting = False
        self.preparing_for_auto_invite = False
        self.products_loaded_for_auto_invite = False
        self.influencers_loaded_for_auto_invite = False
        self.switch_shop_requested = False

        # 恢复按钮状态
        self.start_invite_btn.setEnabled(True)
        self.start_invite_btn.setText("开始邀约")
        self.stop_invite_btn.setEnabled(False)

        # 恢复加载按钮
        if self.product_list_btn.text() == "加载中...":
            self.product_list_btn.setEnabled(True)
            self.product_list_btn.setText("商品列表")
        if self.add_to_list_btn.text() == "加载中...":
            self.add_to_list_btn.setEnabled(True)
            self.add_to_list_btn.setText("加载达人")

        print("[自动邀约] 邀约状态已重置。")

    # +++ 新增: 清空达人列表并发送切换店铺信号 +++
    def _clear_table_and_switch_shop(self, shop_name):
        """在发送切换店铺信号前清空达人列表并等待邀约响应"""
        print(f"[店铺切换] ===== 开始切换流程 =====")
        print(f"[店铺切换] 当前店铺: '{self.current_shop_name}' -> 目标店铺: '{shop_name}'")

        # 检查是否是重复切换到同一个店铺
        if self.current_shop_name == shop_name:
            print(f"[店铺切换] 警告：目标店铺与当前店铺相同，跳过切换")
            return

        # 防止重复清空
        if hasattr(self, '_is_clearing_table') and self._is_clearing_table:
            print(f"[店铺切换] 警告：检测到重复的清空表格请求，忽略此次调用")
            return

        # 设置处理标志
        self._is_clearing_table = True

        # 设置店铺切换请求标志，确保切换后能恢复邀约
        self.switch_shop_requested = True
        # 记录自动切换的时间戳，用于判断是否是最近的自动切换
        import time
        self.last_auto_switch_time = time.time()
        print(f"[店铺切换] 设置switch_shop_requested标志为True，记录切换时间戳: {self.last_auto_switch_time}")

        # 重置粉丝量区间记录，新店铺重新开始尝试所有区间
        if hasattr(self, '_original_fans_interval_index'):
            delattr(self, '_original_fans_interval_index')
            print("[店铺切换] 已重置粉丝量区间记录，新店铺将重新开始尝试所有区间")

        # 创建一个后台线程来处理等待邀约响应，避免阻塞主线程
        def wait_for_invitation_response_worker():
            try:
                # 检查是否有未完成的邀约请求
                if hasattr(self, 'invitation_wait_event') and not self.invitation_wait_event.is_set():
                    print("[店铺切换] 检测到有未完成的邀约请求，等待邀约响应...")
                    # 等待邀约响应，最多等待5秒
                    wait_start_time = time.time()
                    max_wait_time = 5  # 最多等待5秒

                    while not self.invitation_wait_event.wait(timeout=0.1) and (time.time() - wait_start_time < max_wait_time):
                        # 短暂等待，避免CPU占用过高
                        pass

                    if self.invitation_wait_event.is_set():
                        print("[店铺切换] 邀约响应已收到，继续切换店铺")
                    else:
                        print(f"[店铺切换] 等待邀约响应超时 ({max_wait_time}秒)，强制设置事件并继续")
                        self.invitation_wait_event.set()  # 强制设置事件，避免线程卡住

                # 在主线程中执行UI操作
                QMetaObject.invokeMethod(self, "_complete_shop_switch", Qt.QueuedConnection,
                                        Q_ARG(str, shop_name))
            except Exception as e:
                print(f"[店铺切换] 等待邀约响应时发生错误: {e}")
                # 确保处理标志被重置
                self._is_clearing_table = False
                # 在主线程中执行UI操作
                QMetaObject.invokeMethod(self, "_complete_shop_switch", Qt.QueuedConnection,
                                        Q_ARG(str, shop_name))

        # 启动后台线程
        wait_thread = threading.Thread(target=wait_for_invitation_response_worker, daemon=True)
        wait_thread.start()

    # 在主线程中完成店铺切换的UI操作（已弃用，现在直接切换）
    @pyqtSlot(str)
    def _complete_shop_switch(self, shop_name):
        """在主线程中完成店铺切换的UI操作（已弃用，现在使用_switch_shop_directly）"""
        print(f"[店铺切换] _complete_shop_switch被调用，但现在使用直接切换方式")
        try:
            # 直接调用新的切换方法
            self._switch_shop_directly(shop_name)
        finally:
            # 无论如何都要重置处理标志
            self._is_clearing_table = False

    def _switch_shop_directly(self, shop_name):
        """直接切换店铺，不依赖主窗口的复杂逻辑

        Args:
            shop_name (str): 要切换到的店铺名称
        """
        print(f"[直接切换] 开始切换到店铺: '{shop_name}'")

        # 重新读取店铺信息和最新cookie
        print(f"[直接切换] 🔄 重新从config\\账号列表.json读取店铺 '{shop_name}' 的最新信息...")
        shop_info = ShopManager.get_shop_by_name(shop_name)
        if not shop_info:
            print(f"[直接切换] ❌ 错误：未找到店铺 '{shop_name}' 的信息")
            QMessageBox.warning(self, "错误", f"未找到店铺 '{shop_name}' 的信息")
            return False

        # 验证cookie信息
        cookies_list = shop_info.get('cookies_list', [])
        if not cookies_list:
            print(f"[直接切换] ⚠️ 警告：店铺 '{shop_name}' 没有Cookie信息")
        else:
            cookie_dict = {c['name']: c['value'] for c in cookies_list}
            biz_magic = cookie_dict.get('biz_magic', '')
            biz_token = cookie_dict.get('biz_token', '')
            print(f"[直接切换] 🍪 Cookie验证结果:")
            print(f"[直接切换]   - biz_magic: {'✅ 有效' if biz_magic else '❌ 缺失'}")
            print(f"[直接切换]   - biz_token: {'✅ 有效' if biz_token else '❌ 缺失'}")

        # 更新当前店铺名称
        old_shop = self.current_shop_name
        self.current_shop_name = shop_name
        print(f"[直接切换] 店铺已从 '{old_shop}' 切换到 '{shop_name}'")

        # 清空达人列表
        self.table.setRowCount(0)
        print("[直接切换] 已清空达人列表")

        # 重置页码
        self.current_page = 0
        # 直接使用setText，因为这些都是QLineEdit对象
        self.start_page_input.setText("0")
        self.page_input.setText("0")
        self.prev_page_btn.setEnabled(False)
        print("[直接切换] 已重置页码")

        # 更新达人邀约页面内部的店铺下拉框显示
        self._update_internal_shop_combo(shop_name)

        # 在切换店铺时记录当前店铺的状态
        if old_shop and old_shop != shop_name and hasattr(self, 'current_shop_invite_status'):
            current_status = self.current_shop_invite_status.get("status", "")
            current_count = self.current_shop_invite_status.get("count", 0)

            # 如果当前店铺有状态，就记录它
            if current_status:
                print(f"[切换店铺] 记录当前店铺 '{old_shop}' 的状态")
                InviteLogDialog.add_log_entry(old_shop, current_status, current_count)
                print(f"[切换店铺] 已记录店铺 '{old_shop}' 状态: {current_status}, 次数: {current_count}")
            else:
                print(f"[切换店铺] 当前店铺 '{old_shop}' 无状态，跳过记录")

        # 重置新店铺的邀约状态
        self.current_shop_invite_status = {
            "status": "开始邀约",
            "count": 0,
            "recorded": False
        }
        print(f"[切换店铺] 已重置新店铺 '{shop_name}' 的邀约状态为开始邀约")

        # 清空商品列表
        self.selected_products_table.setRowCount(0)
        product_headers = ["序号", "商品ID", "商品标题", "价格", "佣金", "店铺"]
        self.selected_products_table.setHorizontalHeaderLabels(product_headers)
        print(f"[直接切换] 已清空商品列表")

        # 更新邀约设置
        self.update_invitation_settings(shop_name)
        print(f"[直接切换] 已更新邀约设置")

        # 检查是否需要恢复邀约流程
        should_resume_invite = (
            self.is_auto_inviting or  # 当前正在自动邀约
            hasattr(self, 'auto_adjust_page_for_invitation') and self.auto_adjust_page_for_invitation or  # 自动调整页面标志
            hasattr(self, 'switch_shop_requested') and self.switch_shop_requested  # 店铺切换请求标志
        )

        if should_resume_invite:
            print(f"[直接切换] 检测到需要恢复邀约流程，将延迟恢复")
            print(f"[直接切换] 状态检查: is_auto_inviting={self.is_auto_inviting}, auto_adjust_page_for_invitation={getattr(self, 'auto_adjust_page_for_invitation', False)}, switch_shop_requested={getattr(self, 'switch_shop_requested', False)}")
            # 使用定时器延迟恢复，确保UI更新完成
            QTimer.singleShot(2000, lambda: self._resume_auto_invite_after_switch(shop_name))
        else:
            print(f"[直接切换] 无需恢复邀约流程")

        print(f"[直接切换] 店铺切换完成: '{shop_name}'")
        return True

    def _resume_auto_invite_after_switch(self, shop_name):
        """店铺切换后恢复自动邀约流程

        Args:
            shop_name (str): 切换后的店铺名称
        """
        print(f"[切换恢复] 延迟2秒后，检查是否需要恢复店铺 '{shop_name}' 的自动邀约流程")

        # 检查是否已经被手动停止
        if not self.is_auto_inviting:
            print(f"[切换恢复] 检测到邀约已被停止，但这可能是店铺切换导致的")
            print(f"[切换恢复] 检查是否有店铺切换标志: switch_shop_requested={getattr(self, 'switch_shop_requested', False)}")

            # 如果有店铺切换标志，说明应该恢复邀约
            if hasattr(self, 'switch_shop_requested') and self.switch_shop_requested:
                print(f"[切换恢复] 检测到店铺切换标志，恢复 is_auto_inviting 为 True")
                self.is_auto_inviting = True
            else:
                print(f"[切换恢复] 确实是手动停止，取消恢复流程")
                self.switch_shop_requested = False
                return

        # 检查是否所有店铺都已完成
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print(f"[切换恢复] 检测到所有店铺已完成，取消恢复流程")
            # 重置切换标志
            self.switch_shop_requested = False
            return

        print(f"[切换恢复] 确认需要恢复邀约，开始恢复店铺 '{shop_name}' 的自动邀约流程")

        # 重置切换标志（延迟重置，确保下拉框变化事件处理完成）
        print(f"[切换恢复] 延迟重置switch_shop_requested标志，避免与下拉框变化事件冲突")
        QTimer.singleShot(1000, lambda: setattr(self, 'switch_shop_requested', False))

        # 设置自动调整页面标志
        print(f"[切换恢复] 设置auto_adjust_page_for_invitation标志为True，加载完成后将直接开始邀约")
        self.auto_adjust_page_for_invitation = True

        # 注意：不在这里再次检查店铺是否在日志中，因为切换逻辑已经确保了店铺是需要处理的
        # 如果在这里再次检查，会导致重复切换，跳过店铺
        print(f"[切换恢复] 店铺 '{shop_name}' 切换完成，开始有序邀约流程")

        # 重置行号，新店铺从头开始邀约
        self.current_auto_invite_row = 0
        print(f"[切换恢复] 重置行号为0，新店铺从头开始邀约")

        # 开始有序的邀约流程：商品 → 达人 → 邀约
        self._start_ordered_invitation_process(shop_name)

    def _start_ordered_invitation_process(self, shop_name):
        """开始有序的邀约流程：商品加载 → 判断成功 → 达人加载 → 开始邀约"""
        print(f"[有序邀约] 开始店铺 '{shop_name}' 的有序邀约流程")

        # 检查是否已经被手动停止
        if not self.is_auto_inviting:
            print(f"[有序邀约] 检测到邀约已被停止，取消有序邀约流程")
            return

        # 检查是否所有店铺都已完成
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print(f"[有序邀约] 检测到所有店铺已完成，取消有序邀约流程")
            return

        # 设置当前处理的店铺
        self.current_processing_shop = shop_name

        # 重置邀约状态
        self.current_shop_invite_status = {
            "status": "",
            "count": 0,
            "recorded": False
        }

        # 加载店铺的邀约记录
        print(f"[有序邀约] 加载店铺 '{shop_name}' 的邀约记录...")
        self._load_shop_invitation_record(shop_name)

        # 第一步：开始加载商品
        print(f"[有序邀约] 第一步：开始加载商品...")
        self._start_product_loading_step(shop_name)

    def _start_product_loading_step(self, shop_name):
        """第一步：加载商品并等待结果"""
        print(f"[商品加载步骤] 开始为店铺 '{shop_name}' 加载商品...")

        # 设置标志，表示正在为自动邀约加载商品
        self.loading_products_for_auto_invite = True
        self.product_loading_shop = shop_name

        try:
            # 调用商品加载方法
            self.on_load_products_clicked()
            print(f"[商品加载步骤] 商品加载已启动，等待结果...")
        except Exception as e:
            print(f"[商品加载步骤] 商品加载启动失败: {e}")
            self._handle_product_loading_failed(shop_name, f"启动失败: {e}")

    def _handle_product_loading_success(self, shop_name):
        """商品加载成功，继续第二步：加载达人"""
        print(f"[商品加载步骤] 店铺 '{shop_name}' 商品加载成功，继续加载达人...")

        # 重置商品加载标志
        self.loading_products_for_auto_invite = False
        self.product_loading_shop = None

        # 第二步：开始加载达人
        self._start_influencer_loading_step(shop_name)

    def _handle_product_loading_failed(self, shop_name, reason):
        """商品加载失败，记录原因并切换下一个店铺"""
        print(f"[商品加载步骤] 店铺 '{shop_name}' 商品加载失败: {reason}")

        # 重置商品加载标志
        self.loading_products_for_auto_invite = False
        self.product_loading_shop = None

        # 根据失败原因确定日志记录的状态
        if "API调用失败" in reason:
            # 如果是API调用失败，日志记录为"邀约关闭"
            log_status = "邀约关闭"
            skip_reason = f"商品加载失败: {reason}"
        else:
            # 其他失败原因保持原有逻辑
            log_status = f"商品加载失败: {reason}"
            skip_reason = log_status

        self._record_shop_failure_and_switch(shop_name, skip_reason, log_status)

    def _start_influencer_loading_step(self, shop_name):
        """第二步：加载达人并等待结果"""
        print(f"[达人加载步骤] 开始为店铺 '{shop_name}' 加载达人...")

        # 设置标志，表示正在为自动邀约加载达人
        self.loading_influencers_for_auto_invite = True
        self.influencer_loading_shop = shop_name

        try:
            # 调用达人加载方法
            self._prepare_and_load_influencers()
            print(f"[达人加载步骤] 达人加载已启动，等待结果...")
        except Exception as e:
            print(f"[达人加载步骤] 达人加载启动失败: {e}")
            self._handle_influencer_loading_failed(shop_name, f"启动失败: {e}")

    def _handle_influencer_loading_success(self, shop_name):
        """达人加载成功，开始第三步：邀约流程"""
        print(f"[达人加载步骤] 店铺 '{shop_name}' 达人加载成功，开始邀约流程...")

        # 重置达人加载标志
        self.loading_influencers_for_auto_invite = False
        self.influencer_loading_shop = None

        # 第三步：开始邀约
        self._start_invitation_step(shop_name)

    def _handle_influencer_loading_failed(self, shop_name, reason):
        """达人加载失败，先尝试切换粉丝量区间，如果所有区间都试过了再记录原因并切换下一个店铺"""
        print(f"[达人加载步骤] 店铺 '{shop_name}' 达人加载失败: {reason}")

        # 重置达人加载标志
        self.loading_influencers_for_auto_invite = False
        self.influencer_loading_shop = None

        # 检查是否在自动邀约或单店邀约过程中，如果是则先尝试切换粉丝量区间
        if (hasattr(self, 'is_auto_inviting') and self.is_auto_inviting) or (hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting):
            print(f"[达人加载步骤] 检测到邀约过程中达人加载失败，先尝试切换粉丝量区间")

            # 先尝试切换粉丝量区间重新加载
            if self._try_switch_fans_interval_and_reload():
                print(f"[达人加载步骤] 已切换粉丝量区间，重新加载达人数据")
                # 重新设置达人加载标志，准备重新加载
                self.loading_influencers_for_auto_invite = True
                self.influencer_loading_shop = shop_name
                return

            # 如果无法切换粉丝量区间，则继续原有的失败处理逻辑
            print(f"[达人加载步骤] 所有粉丝量区间都已尝试，继续失败处理流程")

        # 记录失败原因
        skip_reason = f"达人加载失败: {reason}"
        self._record_shop_failure_and_switch(shop_name, skip_reason)

    def _start_invitation_step(self, shop_name):
        """第三步：开始邀约流程"""
        print(f"[邀约步骤] 开始店铺 '{shop_name}' 的邀约流程...")

        # 设置邀约状态
        self.is_auto_inviting = True
        self.preparing_for_auto_invite = False

        # 更新UI状态
        self.start_invite_btn.setEnabled(False)
        self.start_invite_btn.setText("邀约中...")
        self.stop_invite_btn.setEnabled(True)

        # 启动邀约线程
        self.auto_invitation_thread = threading.Thread(target=self._auto_invitation_worker, daemon=True)
        self.auto_invitation_thread.start()

        print(f"[邀约步骤] 店铺 '{shop_name}' 邀约线程已启动")

    def _record_shop_failure_and_switch(self, shop_name, reason, log_status=None):
        """记录店铺失败原因并切换到下一个店铺

        Args:
            shop_name: 店铺名称
            reason: 失败原因（用于内部记录）
            log_status: 日志状态（用于邀约日志显示，如果为None则使用reason）
        """
        print(f"[店铺失败处理] 店铺 '{shop_name}' 失败，原因: {reason}")

        # 记录跳过信息
        if shop_name:
            self.skipped_shops_info[shop_name] = reason
            print(f"[店铺失败处理] 已记录店铺 '{shop_name}' 跳过，原因: {reason}")

            # 更新邀约日志，使用指定的日志状态或默认的失败原因
            display_status = log_status if log_status is not None else reason
            print(f"[店铺失败处理] 邀约日志将记录状态: {display_status}")
            InviteLogDialog.add_log_entry(shop_name, display_status, 0)

        # 停止当前店铺的邀约线程（如果有）
        if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
            print(f"[店铺失败处理] 停止当前邀约线程...")
            self._stop_current_shop_invitation_only(reason)

        # 寻找下一个店铺
        next_shop = self._get_next_shop_name(shop_name)
        if next_shop:
            print(f"[店铺失败处理] 找到下一个店铺: '{next_shop}'，准备切换")
            self._clear_table_and_switch_shop(next_shop)
        else:
            print(f"[店铺失败处理] 没有更多店铺，强制记录当前店铺并停止邀约")
            self._force_record_current_shop_and_complete("店铺失败处理-无更多店铺")

    # +++ 新增: 处理请求下一页的槽函数 +++
    def _handle_request_next_page(self):
        """主线程槽函数：处理来自后台线程的翻页请求"""
        if not self.is_auto_inviting:
            print("[主线程] 收到翻页请求，但自动邀约已停止。")
            # 设置页面加载事件，避免后台线程卡住
            if hasattr(self, 'page_load_event'):
                self.page_load_event.set()
            return

        print("[主线程] 收到翻页请求，尝试翻页...")

        # 直接调用on_next_influencer_page_clicked方法
        try:
            self.on_next_influencer_page_clicked()
            print("[主线程] 已调用on_next_influencer_page_clicked方法")
            # 不设置page_load_event，让on_next_influencer_page_clicked方法中的加载完成回调来设置
        except Exception as e:
            print(f"[主线程] 调用on_next_influencer_page_clicked方法失败: {e}")
            # 设置页面加载事件，避免后台线程卡住
            if hasattr(self, 'page_load_event'):
                self.page_load_event.set()

    def _load_settings(self):
        """(修改) 加载 config/config.json 中 'yaoyue' 部分的设置，包括筛选器"""
        print("[DEBUG Inv] _load_settings called!")
        # --- 新增: 设置加载标志 ---
        self._loading_settings = True
        print("[DEBUG Inv] Loading flag SET")
        # --- 结束新增 ---

        default_invitation_settings = {
            "interval_min": 1,
            "interval_max": 3,
            "auto_select_products": True,
            "invite_count": 30,
            "categories": [],
            "fans_filter": "不限",
            "sales_filter": "不限",
            "price_filter": "不限",
            "avg_sales_filter": "不限",
            "age_filter": "不限",
            "gender_filter": "不限",
            "has_recommendation": False,
            "has_certification": False,
            "timer_enabled": False,
            "timer_hour": "9",
            "timer_minute": "0",
        }
        invitation_settings = default_invitation_settings.copy()

        try:
            abs_path = os.path.abspath(self.settings_file)
            print(f"[DEBUG Inv] Settings file path: {abs_path}")

            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    all_config = json.load(f)
                    print("[DEBUG Inv] Existing config read successfully.")
                    loaded_inv_settings = all_config.get("yaoyue")
                    if loaded_inv_settings and isinstance(loaded_inv_settings, dict):
                        print(f"[DEBUG Inv] Found 'yaoyue' section: {loaded_inv_settings}")
                        invitation_settings.update(loaded_inv_settings)
                    else:
                        print("[DEBUG Inv] 'yaoyue' section not found or invalid. Using defaults.")
            else:
                print(f"[DEBUG Inv] Config file does not exist. Using defaults.")

        except Exception as e:
            print(f"[DEBUG Inv] Error or exception during settings load: {e}. Using defaults.")
            invitation_settings = default_invitation_settings.copy()

        # --- 修改: 应用设置 (放在 try...finally 中确保标志位重置) ---
        try:
            print(f"[DEBUG Inv] Applying settings to UI: {invitation_settings}")
            # 1. Interval & Count
            self.interval_min.setValue(int(invitation_settings.get("interval_min", 1)))
            self.interval_max.setValue(int(invitation_settings.get("interval_max", 3)))
            self.auto_invite_checkbox.setChecked(bool(invitation_settings.get("auto_select_products", True)))
            self.invite_count_input.setText(str(invitation_settings.get("invite_count", 30)))
            # 定时邀约设置
            self.timer_checkbox.setChecked(bool(invitation_settings.get("timer_enabled", False)))
            self.timer_hour_input.setText(str(invitation_settings.get("timer_hour", "9")))
            self.timer_minute_input.setText(str(invitation_settings.get("timer_minute", "0")))
            # 2. Categories
            saved_categories = invitation_settings.get("categories", [])
            if hasattr(self, 'category_checkboxes'):
                print(f"[DEBUG Inv] Applying categories: {saved_categories}")
                for checkbox in self.category_checkboxes:
                    checkbox.setChecked(checkbox.text() in saved_categories)
            # 3. Radio Buttons
            print("[DEBUG Inv] Applying radio button filters...")
            self._set_checked_radio_button("fans", invitation_settings.get("fans_filter", "不限"))
            self._set_checked_radio_button("sales", invitation_settings.get("sales_filter", "不限"))
            self._set_checked_radio_button("price", invitation_settings.get("price_filter", "不限"))
            self._set_checked_radio_button("avg_sales", invitation_settings.get("avg_sales_filter", "不限"))
            self._set_checked_radio_button("age", invitation_settings.get("age_filter", "不限"))
            # 4. Gender ComboBox
            saved_gender = invitation_settings.get("gender_filter", "不限")
            print(f"[DEBUG Inv] Applying gender filter: {saved_gender}")
            index = self.gender_combo.findText(saved_gender)
            if index >= 0:
                self.gender_combo.setCurrentIndex(index)
            else:
                self.gender_combo.setCurrentIndex(0)
            # 5. Other Checkboxes
            self.has_recommendation.setChecked(bool(invitation_settings.get("has_recommendation", False)))
            print(f"[DEBUG Inv] Applying 'has_recommendation': {self.has_recommendation.isChecked()}")
            self.has_certification.setChecked(bool(invitation_settings.get("has_certification", False)))
            print(f"[DEBUG Inv] Applying 'has_certification': {self.has_certification.isChecked()}")

            print("[DEBUG Inv] All settings applied to UI successfully.")
        except Exception as ui_err:
             print(f"[DEBUG Inv] Error applying settings to UI: {ui_err}")
        finally:
            # --- 新增: 重置加载标志 ---
            self._loading_settings = False
            print("[DEBUG Inv] Loading flag UNSET")
            # --- 结束新增 ---
    # --- 结束修改 ---

    # --- 新增: 设置单选按钮组状态的辅助函数 ---
    def _set_checked_radio_button(self, group_prefix, text_to_check):
        """根据文本查找并选中单选按钮组中的对应项"""
        suffixes = {
            'fans': ['unlimited', 'less_1w', '1w_10w', '10w_50w', 'more_50w'],
            'sales': ['unlimited', 'less_10w', '10w_50w', '50w_100w', 'more_100w'],
            'price': ['unlimited', 'less_100', '100_200', 'more_200'],
            'avg_sales': ['unlimited', 'less_1w', '1w_10w', 'more_10w'],
            'age': ['unlimited', 'less_18', '18_24', '25_39', '40_49', 'more_50'],
        }
        found = False
        if group_prefix in suffixes:
            for suffix in suffixes[group_prefix]:
                radio_button = getattr(self, f"{group_prefix}_{suffix}", None)
                if radio_button and radio_button.text() == text_to_check:
                    radio_button.setChecked(True)
                    print(f"  - Radio Group '{group_prefix}': Set to '{text_to_check}'")
                    found = True
                    break
        if not found:
            # 默认选中 "不限"
            unlimited_radio = getattr(self, f"{group_prefix}_unlimited", None)
            if unlimited_radio:
                unlimited_radio.setChecked(True)
                print(f"  - Radio Group '{group_prefix}': Text '{text_to_check}' not found, defaulted to '不限'")
            else:
                 print(f"  - Radio Group '{group_prefix}': Error! Cannot find text '{text_to_check}' or default 'unlimited' button.")

    # --- 新增: 获取单选按钮组选中项文本的辅助函数 ---
    def _get_checked_radio_text(self, group_prefix):
        """获取单选按钮组中当前选中项的文本"""
        suffixes = {
            'fans': ['unlimited', 'less_1w', '1w_10w', '10w_50w', 'more_50w'],
            'sales': ['unlimited', 'less_10w', '10w_50w', '50w_100w', 'more_100w'],
            'price': ['unlimited', 'less_100', '100_200', 'more_200'],
            'avg_sales': ['unlimited', 'less_1w', '1w_10w', 'more_10w'],
            'age': ['unlimited', 'less_18', '18_24', '25_39', '40_49', 'more_50'],
        }
        if group_prefix in suffixes:
            for suffix in suffixes[group_prefix]:
                radio_button = getattr(self, f"{group_prefix}_{suffix}", None)
                if radio_button and radio_button.isChecked():
                    return radio_button.text()
        return "不限" # Default if none found or group invalid
    # --- 结束新增 ---


    # --- 修改: 保存设置函数 (检查标志位) ---
    def _save_settings(self):
        """(修改) 将当前邀约设置（包括筛选器）保存到 config/config.json 的 'yaoyue' 部分"""
        # --- 新增: 检查加载标志 ---
        if self._loading_settings:
            print("[DEBUG Inv] _save_settings called during load, ignoring.")
            return
        # --- 结束新增 ---

        print("[DEBUG Inv] _save_settings called!")

        # (Rest of the saving logic remains the same)
        current_invitation_settings = {}
        try:
            # (Get current settings from UI)
            # ...
            # 1. Interval & Count (保持不变)
            invite_count_text = self.invite_count_input.text()
            invite_count_value = int(invite_count_text) if invite_count_text.isdigit() else 30

            current_invitation_settings = {
                "interval_min": self.interval_min.value(),
                "interval_max": self.interval_max.value(),
                "auto_select_products": self.auto_invite_checkbox.isChecked(),
                "invite_count": invite_count_value,
                "timer_enabled": self.timer_checkbox.isChecked(),
                "timer_hour": self.timer_hour_input.text(),
                "timer_minute": self.timer_minute_input.text()
            }

            # --- 新增: 保存筛选器状态 ---
            # 2. Categories
            selected_categories = []
            if hasattr(self, 'category_checkboxes'):
                for checkbox in self.category_checkboxes:
                    if checkbox.isChecked():
                        selected_categories.append(checkbox.text())
            current_invitation_settings["categories"] = selected_categories
            print(f"[DEBUG Inv] Saving categories: {selected_categories}")

            # 3. Radio Buttons (使用辅助函数)
            print("[DEBUG Inv] Saving radio button filters...")
            current_invitation_settings["fans_filter"] = self._get_checked_radio_text("fans")
            current_invitation_settings["sales_filter"] = self._get_checked_radio_text("sales")
            current_invitation_settings["price_filter"] = self._get_checked_radio_text("price")
            current_invitation_settings["avg_sales_filter"] = self._get_checked_radio_text("avg_sales")
            current_invitation_settings["age_filter"] = self._get_checked_radio_text("age")
            print(f"  - Fans: {current_invitation_settings['fans_filter']}")
            # ... (可以添加其他 radio group 的打印)

            # 4. Gender ComboBox
            current_invitation_settings["gender_filter"] = self.gender_combo.currentText()
            print(f"[DEBUG Inv] Saving gender filter: {current_invitation_settings['gender_filter']}")

            # 5. Other Checkboxes
            current_invitation_settings["has_recommendation"] = self.has_recommendation.isChecked()
            print(f"[DEBUG Inv] Saving 'has_recommendation': {current_invitation_settings['has_recommendation']}")
            current_invitation_settings["has_certification"] = self.has_certification.isChecked()
            print(f"[DEBUG Inv] Saving 'has_certification': {current_invitation_settings['has_certification']}")
            # --- 结束新增 ---

            print(f"[DEBUG Inv] All current invitation settings prepared: {current_invitation_settings}")
        except Exception as e:
            # (Error handling for preparing settings)
            print(f"[DEBUG Inv] Error preparing settings: {e}")
            return

        # (Reading existing config, updating 'yaoyue', writing back logic remains the same)
        all_config = {}
        try:
            # ... (Read existing config) ...
            abs_path = os.path.abspath(self.settings_file)
            print(f"[DEBUG Inv] Settings file path: {abs_path}")

            if os.path.exists(self.settings_file):
                try:
                    with open(self.settings_file, 'r', encoding='utf-8') as f:
                        all_config = json.load(f)
                        print("[DEBUG Inv] Existing config read successfully.")
                except (json.JSONDecodeError, TypeError) as read_err:
                    print(f"[DEBUG Inv] Error reading existing config file ({read_err}). Will create/overwrite 'yaoyue' section.")
                    all_config = {} # 读取失败，从空配置开始
            else:
                print("[DEBUG Inv] Config file does not exist. Starting fresh.")

            # Update 'yaoyue' section
            yaoyue_section = all_config.get("yaoyue", {})
            if not isinstance(yaoyue_section, dict): yaoyue_section = {}
            yaoyue_section.update(current_invitation_settings)
            all_config["yaoyue"] = yaoyue_section

            print(f"[DEBUG Inv] Final config to write: {all_config}")

            # (Ensure directory exists and write back)
            self._ensure_config_dir_exists()
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(all_config, f, indent=4, ensure_ascii=False)
            print(f"[DEBUG Inv] Successfully wrote to {self.settings_file}")

        except Exception as e: # (Error handling for writing)
            print(f"[DEBUG Inv] Error during save process: {e}")
    # --- 结束修改 ---

    # +++ 新增: 执行自动邀约循环的方法 +++
    def _execute_auto_invitation_loop(self):
        """检查通过后，实际启动自动邀约的后台线程"""
        # 检查是否所有店铺都已邀约完成，如果是则不启动邀约
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print("[DEBUG AutoInvite Loop] 检测到所有店铺已邀约完成，跳过启动邀约循环")
            # 确保按钮状态正确
            if self.start_invite_btn.text() != "开始邀约(已完成)":
                print("[DEBUG AutoInvite Loop] 修正按钮状态为'开始邀约(已完成)'")
                self.start_invite_btn.setEnabled(True)
                self.start_invite_btn.setText("开始邀约(已完成)")
                self.stop_invite_btn.setEnabled(False)
            return

        # 检查是否是自动调整页码后的加载
        force_start = hasattr(self, 'auto_adjust_page_for_invitation') and self.auto_adjust_page_for_invitation

        # 检查是否已有邀约线程在运行
        thread_running = hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive()

        if thread_running:
            print("检测到已有邀约线程在运行...")

            # 如果是强制启动或者当前没有设置自动邀约标志，则停止当前线程并启动新线程
            if force_start or not self.is_auto_inviting:
                print("强制停止当前邀约线程并启动新线程...")
                # 停止当前邀约线程
                self.is_auto_inviting = False # 设置标志，让线程自行退出
                # 等待线程退出
                self.auto_invitation_thread.join(1.0) # 最多等待1秒

                # 重置标志
                if hasattr(self, 'auto_adjust_page_for_invitation'):
                    self.auto_adjust_page_for_invitation = False

                # 继续执行，不返回
            else:
                print("警告：尝试启动邀约循环，但已在进行中。")
                # 检查是否所有店铺都已邀约完成，如果是则不设置邀约中状态
                if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
                    print("检测到所有店铺已邀约完成，不设置邀约中状态")
                    # 确保按钮状态正确
                    if self.start_invite_btn.text() != "开始邀约(已完成)":
                        self.start_invite_btn.setEnabled(True)
                        self.start_invite_btn.setText("开始邀约(已完成)")
                        self.stop_invite_btn.setEnabled(False)
                else:
                    # 确保按钮状态正确
                    self.start_invite_btn.setEnabled(False) # 确保开始按钮禁用
                    self.start_invite_btn.setText("邀约中...") # 更新按钮文本
                    self.stop_invite_btn.setEnabled(True) # 确保停止按钮启用
                return

        print("准备工作完成，开始执行自动邀约循环...")

        # 再次检查是否所有店铺都已邀约完成，如果是则不启动邀约
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print("在启动邀约循环前检测到所有店铺已邀约完成，取消启动")
            # 确保按钮状态正确
            if self.start_invite_btn.text() != "开始邀约(已完成)":
                self.start_invite_btn.setEnabled(True)
                self.start_invite_btn.setText("开始邀约(已完成)")
                self.stop_invite_btn.setEnabled(False)
            return

        self.is_auto_inviting = True
        self.start_invite_btn.setEnabled(False) # 确保开始按钮禁用
        self.start_invite_btn.setText("邀约中...") # 更新按钮文本
        self.stop_invite_btn.setEnabled(True) # 启用停止按钮

        # 启动后台线程
        self.auto_invitation_thread = threading.Thread(target=self._auto_invitation_worker, daemon=True)
        self.auto_invitation_thread.start()
    # +++ 结束新增 +++

    # +++ 新增: 重置准备状态的方法 +++
    def _reset_auto_invite_preparation(self, error=False):
        """重置自动邀约的准备状态，恢复按钮等"""
        print(f"重置自动邀约准备状态 (发生错误: {error})")
        self.preparing_for_auto_invite = False
        self.products_loaded_for_auto_invite = False
        self.influencers_loaded_for_auto_invite = False

        # 只有在非邀约进行中时才恢复按钮
        if not self.is_auto_inviting:
            self.start_invite_btn.setEnabled(True)
            self.start_invite_btn.setText("开始邀约")
            self.stop_invite_btn.setEnabled(False)

            # 也需要确保加载按钮是可用的
            if self.product_list_btn.text() == "加载中...":
                 self.product_list_btn.setEnabled(True)
                 self.product_list_btn.setText("商品列表")
            if self.add_to_list_btn.text() == "加载中...":
                 self.add_to_list_btn.setEnabled(True)
                 self.add_to_list_btn.setText("加载达人")

        if error:
            QMessageBox.warning(self, "准备失败", "自动加载列表失败，无法开始自动邀约。请检查错误信息或手动加载后重试。")
    # +++ 结束新增 +++

    # +++ 新增: 检查列表是否都加载完成并启动邀约 +++
    def _check_and_execute_auto_invite(self):
        """检查商品和达人列表是否都为自动邀约准备就绪"""
        # +++ 调试日志 +++
        print("[DEBUG AutoInvite Prep] Entering _check_and_execute_auto_invite")
        print(f"  - preparing_for_auto_invite: {self.preparing_for_auto_invite}")
        print(f"  - products_loaded_for_auto_invite: {self.products_loaded_for_auto_invite}")
        print(f"  - influencers_loaded_for_auto_invite: {self.influencers_loaded_for_auto_invite}")
        try:
            product_rows = self.selected_products_table.rowCount()
            influencer_rows = self.table.rowCount()
            print(f"  - Product Table Rows: {product_rows}")
            print(f"  - Influencer Table Rows: {influencer_rows}")
        except Exception as e:
            print(f"  - Error getting row counts: {e}")
            product_rows = -1 # Indicate error
            influencer_rows = -1
        # +++ 结束调试 +++

        if not self.preparing_for_auto_invite:
            # +++ 调试日志 +++
            print("[DEBUG AutoInvite Prep] Check failed: Not in preparation phase.")
            # +++ 结束调试 +++
            return # 如果不是在准备阶段，则忽略

        if self.products_loaded_for_auto_invite and self.influencers_loaded_for_auto_invite:
            if product_rows > 0 and influencer_rows > 0:
                 # +++ 调试日志 +++
                 print("[DEBUG AutoInvite Prep] Check SUCCESS: Both lists loaded and non-empty. Starting loop...")
                 # +++ 结束调试 +++
                 print("[AutoInvite Prep] 商品和达人列表均已成功加载，准备启动邀约循环。")
                 self.preparing_for_auto_invite = False # 结束准备阶段
                 self._execute_auto_invitation_loop() # 启动实际的邀约循环
            else:
                 # +++ 调试日志 +++
                 print("[DEBUG AutoInvite Prep] Check failed: Lists loaded but one or both are empty.")
                 # +++ 结束调试 +++
                 print("[AutoInvite Prep] 列表加载完成，但其中一个或两个列表为空，无法开始邀约。")
                 self._reset_auto_invite_preparation(error=True) # 视为错误，重置状态
                 QMessageBox.warning(self, "列表为空", "自动加载完成后，商品列表或达人列表为空，无法开始邀约。请检查筛选条件或手动加载。")
        else:
            # +++ 调试日志 +++
            print("[DEBUG AutoInvite Prep] Check failed: One or both loaded flags are False.")
            # +++ 结束调试 +++
            print("[AutoInvite Prep] 检查时发现列表尚未完全准备好。")
    # +++ 结束新增 +++

    # +++ 重写: 处理邀约结果的槽函数 +++
    def _handle_invitation_result(self, row, success, message, error_code):
        """主线程槽函数：处理后台邀约线程返回的结果"""
        print(f"[主线程] 收到邀约结果: 行={row+1}, 成功={success}, 消息='{message}', 代码={error_code}")
        print(f"[主线程] 当前状态: is_auto_inviting={self.is_auto_inviting}, switch_shop_requested={getattr(self, 'switch_shop_requested', False)}")

        # 保存当前的状态，以便在处理完成后恢复
        original_auto_inviting = self.is_auto_inviting
        original_switch_requested = getattr(self, 'switch_shop_requested', False)

        # 更新邀约结果，以便自动邀约线程可以获取
        self.invitation_result["row"] = row
        self.invitation_result["success"] = success
        self.invitation_result["message"] = message
        self.invitation_result["error_code"] = error_code

        # 检查行号是否有效 (防止表格在结果返回前被清空或更改)
        if row < 0 or row >= self.table.rowCount():
            print(f"[主线程] 警告: 收到邀约结果，但行号 {row+1} 已失效")
            # 即使行号无效，也要通知等待的线程
            self.invitation_wait_event.set()
            return

        # 准备更新表格状态
        status_col_index = 3
        status_item = self.table.item(row, status_col_index)
        if not status_item:
            status_item = QTableWidgetItem("")
            self.table.setItem(row, status_col_index, status_item)

        # 处理邀约成功的情况
        if success:
            # 1. 更新表格状态
            status_item.setText("邀约成功")
            status_item.setToolTip(f"邀约成功: {message}")

            # 2. 更新当前店铺的邀约状态
            if self.current_shop_name:
                self.current_shop_invite_status["status"] = "邀约成功"
                self.current_shop_invite_status["count"] += 1
                self.current_shop_invite_status["recorded"] = False

            # 通知等待的邀约线程
            self.invitation_wait_event.set()
        else:
            # 处理邀约失败的情况

            # 1. 处理达到上限错误(13002)
            if error_code == 13002:
                skip_reason = "今日邀约已达上限(13002)"
                print(f"[店铺切换] 检测到店铺 '{self.current_shop_name}' {skip_reason}")

                # 更新当前店铺状态
                if self.current_shop_name:
                    self.current_shop_invite_status["status"] = "已达上限"
                    self.current_shop_invite_status["count"] = 200
                    self.current_shop_invite_status["recorded"] = False

                # 记录到跳过店铺信息
                if self.current_shop_name:
                    if not hasattr(self, 'skipped_shops_info'):
                        self.skipped_shops_info = {}
                    self.skipped_shops_info[self.current_shop_name] = skip_reason
                    print(f"[Skip Record] 已记录店铺 '{self.current_shop_name}' 跳过，原因: {skip_reason}")

                # 立即更新当前店铺状态到邀约日志，覆盖之前的"开始邀约"状态
                if self.current_shop_name:
                    # 无论是否已记录，都更新为"已达上限"状态
                    InviteLogDialog.update_log_entry(
                        self.current_shop_name,
                        self.current_shop_invite_status["status"],
                        self.current_shop_invite_status["count"]
                    )
                    self.current_shop_invite_status["recorded"] = True
                    print(f"[邀约日志] 已更新店铺 '{self.current_shop_name}' 的状态: {self.current_shop_invite_status['status']}")

                # 只有在自动邀约模式下才检查所有店铺是否完成
                # 单店邀约模式下不应该触发"所有店铺完成"的检查
                if self.is_auto_inviting and not (hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting):
                    # 检查所有店铺是否都已经在日志中有记录
                    if self._check_all_shops_in_log():
                        print("[邀约日志] 所有店铺都已经在日志中有记录，邀约完成，停止邀约过程")
                        # 使用统一方法处理所有店铺完成
                        if self._handle_all_shops_completed("邀约结果处理-达到上限"):
                            # 通知等待的邀约线程
                            self.invitation_wait_event.set()
                            return
                else:
                    print("[邀约日志] 单店邀约模式，跳过所有店铺完成检查")

                # 更新表格状态
                status_item.setText("达到上限")
                status_item.setToolTip(f"错误码: {error_code}\n错误信息: {message}")
                self._record_limit_reached_state() # 调用记录状态方法

                # 检查是单店邀约还是自动邀约模式
                if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
                    # 单店邀约模式：达到上限时直接停止邀约，不显示弹窗（避免重复弹窗）
                    print(f"[单店邀约] 检测到单店邀约达到上限，直接停止邀约")
                    self._stop_single_shop_invitation("达到上限", show_message=False)
                elif self.is_auto_inviting:
                    # 自动邀约模式：达到上限时切换店铺
                    if self.original_shop_hit_limit is None:
                        self.original_shop_hit_limit = self.current_shop_name

                    # 先记录当前店铺达到上限的状态
                    print(f"[达到上限] 先记录当前店铺 '{self.current_shop_name}' 达到上限状态")
                    current_count = self.current_shop_invite_status.get("count", 0) if hasattr(self, 'current_shop_invite_status') else 0
                    self._record_current_shop_status("达到上限", current_count)

                    # 然后获取下一个未在日志中有记录的店铺
                    next_shop = self._get_next_shop_name(self.current_shop_name)
                    if next_shop:
                        print(f"[邀约日志] 找到下一个未在日志中有记录的店铺: '{next_shop}'，准备切换")
                        # 设置切换标志
                        print("[店铺切换] 设置切换标志并发送切换信号...")
                        self.switch_shop_requested = True

                        # 使用新方法清空达人列表并发送切换店铺信号
                        self._clear_table_and_switch_shop(next_shop)
                        print("[店铺切换] 已发送切换信号，等待店铺切换完成后恢复邀约...")

                        # 停止当前店铺的自动邀约，但保持is_auto_inviting为True
                        print("[店铺切换] 停止当前店铺的邀约线程，但保持自动邀约状态...")
                        self._stop_current_shop_invitation_only(skip_reason)
                    else:
                        print("[邀约日志] 无法找到下一个未在日志中有记录的店铺，所有店铺已完成")
                        # 所有店铺已完成
                        self._force_record_current_shop_and_complete("邀约结果处理-达到上限-所有店铺已完成")
                else:
                    # 既不是单店邀约也不是自动邀约，这种情况不应该发生
                    print(f"[警告] 检测到达到上限，但当前既不是单店邀约也不是自动邀约模式")
                    print(f"[警告] is_single_shop_inviting: {getattr(self, 'is_single_shop_inviting', False)}")
                    print(f"[警告] is_auto_inviting: {self.is_auto_inviting}")
                    # 默认停止邀约
                    if hasattr(self, 'is_single_shop_inviting') and hasattr(self, '_stop_single_shop_invitation'):
                        self._stop_single_shop_invitation("达到上限", show_message=False)
                    else:
                        self._stop_auto_invitation(finished_naturally=False, reason="达到上限")

                # 通知等待的邀约线程
                self.invitation_wait_event.set()
                return # 直接返回，不执行后续的失败处理
            # --- 结束新增 --- #

            # 2. 处理店铺掉线情况
            elif "店铺掉线" in message or "cookie失效" in message.lower() or "cookie 失效" in message.lower():
                skip_reason = "店铺掉线"
                print(f"[店铺切换] 检测到店铺 '{self.current_shop_name}' {skip_reason}")

                # 更新表格状态
                status_item.setText("店铺掉线")
                status_item.setToolTip(f"错误码: {error_code}\n错误信息: {message}")

                # 更新当前店铺状态
                if self.current_shop_name:
                    self.current_shop_invite_status["status"] = "店铺掉线"
                    self.current_shop_invite_status["recorded"] = False

                # 记录到跳过店铺信息
                if self.current_shop_name:
                    if not hasattr(self, 'skipped_shops_info'):
                        self.skipped_shops_info = {}
                    self.skipped_shops_info[self.current_shop_name] = skip_reason
                    print(f"[Skip Record] 已记录店铺 '{self.current_shop_name}' 跳过，原因: {skip_reason}")

                # 立即更新当前店铺状态到邀约日志，覆盖之前的"开始邀约"状态
                if self.current_shop_name:
                    # 无论是否已记录，都更新为"店铺掉线"状态
                    InviteLogDialog.update_log_entry(
                        self.current_shop_name,
                        self.current_shop_invite_status["status"],
                        self.current_shop_invite_status["count"]
                    )
                    self.current_shop_invite_status["recorded"] = True
                    print(f"[邀约日志] 已更新店铺 '{self.current_shop_name}' 的状态: {self.current_shop_invite_status['status']}")

                # 检查是单店邀约还是自动邀约模式
                if hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting:
                    # 单店邀约模式：店铺掉线时直接停止邀约，不显示弹窗（避免重复弹窗）
                    print(f"[单店邀约] 检测到单店邀约店铺掉线，直接停止邀约")
                    self._stop_single_shop_invitation("店铺掉线", show_message=False)
                elif self.is_auto_inviting:
                    # 自动邀约模式：店铺掉线时切换店铺
                    # 获取下一个未在日志中有记录的店铺
                    next_shop = self._get_next_shop_name(self.current_shop_name)
                    if next_shop:
                        print(f"[店铺切换] 找到下一个店铺: '{next_shop}'，准备切换")
                        # 设置切换标志
                        self.switch_shop_requested = True

                        # 使用新方法清空达人列表并发送切换店铺信号
                        self._clear_table_and_switch_shop(next_shop)
                        print("[店铺切换] 已发送切换信号，等待店铺切换完成后恢复邀约...")

                        # 停止当前店铺的自动邀约
                        print("[店铺切换] 停止当前店铺的邀约线程，但保持自动邀约状态...")
                        self._stop_current_shop_invitation_only(skip_reason)
                    else:
                        print("[店铺切换] 无法找到下一个店铺，强制记录当前店铺并停止邀约")
                        # 强制记录当前店铺并完成邀约
                        self._force_record_current_shop_and_complete("邀约结果处理-店铺掉线-无下一个店铺")
                else:
                    # 既不是单店邀约也不是自动邀约，这种情况不应该发生
                    print(f"[警告] 检测到店铺掉线，但当前既不是单店邀约也不是自动邀约模式")
                    print(f"[警告] is_single_shop_inviting: {getattr(self, 'is_single_shop_inviting', False)}")
                    print(f"[警告] is_auto_inviting: {self.is_auto_inviting}")
                    # 默认停止邀约
                    if hasattr(self, 'is_single_shop_inviting') and hasattr(self, '_stop_single_shop_invitation'):
                        self._stop_single_shop_invitation("店铺掉线", show_message=False)
                    else:
                        self._stop_auto_invitation(finished_naturally=False, reason="店铺掉线")

                # 通知等待的邀约线程
                self.invitation_wait_event.set()
                return # 直接返回，不执行后续的失败处理

            # 3. 处理错误码13003（达人不接受邀约）
            elif error_code == 13003:
                # 只在表格中更新状态，不记录到日志中
                status_text = "不接受邀约"
                tooltip_text = f"错误码: {error_code}\n错误信息: {message}"
                status_item.setText(status_text)
                status_item.setToolTip(tooltip_text)

                print(f"[主线程] 检测到错误码13003（达人不接受邀约），继续处理下一个达人")

                # 确保自动邀约状态不被中断
                if not self.is_auto_inviting:
                    print("[主线程] 检测到自动邀约标志为False，但这是错误码13003情况，重新设置为True")
                    self.is_auto_inviting = True

                # 通知等待的邀约线程继续处理下一个达人
                self.invitation_wait_event.set()

                print("[主线程] 保持自动邀约状态，继续处理下一个达人")

                return # 直接返回，避免执行后面的代码

            # 4. 处理其他错误
            else:
                status_text = f"邀约失败({error_code})"
                tooltip_text = f"错误码: {error_code}\n错误信息: {message}"
                if message != "未知错误" and message != "请求未返回有效结果" and message != "程序错误":
                    status_text = f"邀约失败: {message[:15]}..."

                status_item.setText(status_text)
                status_item.setToolTip(tooltip_text)

                # 通知等待的邀约线程
                self.invitation_wait_event.set()

        # 检查是否有未完成的邀约请求
        if hasattr(self, 'invitation_wait_event') and not self.invitation_wait_event.is_set():
            print("[主线程] 检测到有未完成的邀约请求，设置邀约等待事件")
            self.invitation_wait_event.set()

        # 检查是否有未完成的页面加载请求
        if hasattr(self, 'page_load_event') and not self.page_load_event.is_set():
            print("[主线程] 检测到有未完成的页面加载请求，设置页面加载事件")
            self.page_load_event.set()

        # 恢复自动邀约状态（如果被改变）
        if original_auto_inviting != self.is_auto_inviting:
            print(f"[主线程] 警告: 邀约结果处理过程中自动邀约状态被改变 ({original_auto_inviting} -> {self.is_auto_inviting})，恢复为原始状态")
            self.is_auto_inviting = original_auto_inviting

        # 恢复切换请求状态（如果被改变）
        if original_switch_requested != getattr(self, 'switch_shop_requested', False):
            print(f"[主线程] 警告: 邀约结果处理过程中切换请求状态被改变 ({original_switch_requested} -> {getattr(self, 'switch_shop_requested', False)})，恢复为原始状态")
            self.switch_shop_requested = original_switch_requested

    # +++ 结束新增 +++

    # +++ 新增: 实际执行邀约的后台工作函数 +++
    def _perform_invitation_worker(self, row, finder_username, nickname, item_list, contact_info, access_token, biz_magic, cookie_input):
        """后台线程：实际调用API发送邀约"""
        print(f"[邀约Worker-Row {row+1}] 开始执行对 {nickname} ({finder_username}) 的邀约。")
        success = False
        message = "未知错误"
        error_code = -1

        # 检查是否已经发送过邀约结果信号，避免重复发送
        result_sent = False

        # 创建一个唯一的请求ID，用于防止重复请求
        import uuid
        request_id = str(uuid.uuid4())
        print(f"[邀约Worker-Row {row+1}] 生成唯一请求ID: {request_id}")

        try:
            # 构造邀约数据 (仅用于日志记录)
            print(f"[邀约Worker-Row {row+1}] 准备邀约数据: finder_username={finder_username}, items={len(item_list)}个, contact_info={contact_info}")

            # 创建WechatAPI实例并调用正确的方法
            api = WechatAPI()

            # 使用互斥锁确保同一时间只有一个线程调用API
            # 这样可以避免多个线程同时发送请求导致的重复邀约问题
            if not hasattr(self, 'invitation_api_lock'):
                self.invitation_api_lock = threading.Lock()

            with self.invitation_api_lock:
                # 不再传递request_id参数，因为API不支持
                result = api.create_promotion_invitation(
                    access_token=access_token,
                    biz_magic=biz_magic,
                    finder_username=finder_username,
                    item_list=item_list,
                    contact_info=contact_info,
                    cookie_input=cookie_input
                )

            print(f"[邀约Worker-Row {row+1}] API返回: {result}")

            # 处理API返回结果
            if isinstance(result, dict):
                error_code = result.get('code', -1)
                message = result.get('msg', 'API未返回消息')

                # 特殊处理错误码13003（已邀约过该达人）
                if error_code == 13003:
                    message = "你已经邀请过该达人，7天内不可再次发送带货邀约"
                    print(f"[邀约Worker-Row {row+1}] 检测到错误码13003: {message}")

                if error_code == 0:
                    success = True
                    message = "邀约请求已发送成功"
                else:
                    success = False
                    # 错误消息已在上面获取
            elif isinstance(result, str):
                success = False
                message = f"请求失败，可能登录失效或服务器错误: {result[:100]}..."
                error_code = -2
            else:
                success = False
                message = f"请求未返回有效结果 (类型: {type(result)})"
                error_code = -3

            # 只发送一次结果信号
            if not result_sent:
                print(f"[邀约Worker-Row {row+1}] 准备发送邀约结果信号: success={success}, msg='{message}', code={error_code}")
                self.invitation_result_signal.emit(row, success, message, error_code)
                result_sent = True

                # 如果是错误码13003，立即返回，不再发送重复请求
                # 但不需要特殊处理，因为已经发送了结果信号，主线程会继续处理下一个达人
                if error_code == 13003:
                    return

        except Exception as e:
            print(f"[邀约Worker-Row {row+1}] 执行邀约时发生异常: {e}")
            success = False
            message = f"程序错误: {str(e)}"
            error_code = -4

            # 如果还没有发送过结果信号，则发送
            if not result_sent:
                print(f"[邀约Worker-Row {row+1}] 准备发送邀约结果信号: success={success}, msg='{message}', code={error_code}")
                self.invitation_result_signal.emit(row, success, message, error_code)
                result_sent = True

    # +++ 结束新增 +++

    # +++ 重写: 在店铺切换后恢复自动邀约流程 +++
    def resume_auto_invite(self):
        """在店铺切换后恢复自动邀约流程"""
        print("[达人邀约] 接收到恢复自动邀约请求，准备在新店铺继续邀约...")
        print(f"[达人邀约] 当前状态: is_auto_inviting={self.is_auto_inviting}, switch_shop_requested={getattr(self, 'switch_shop_requested', False)}")

        # 检查是否所有店铺都已邀约完成，如果是则不恢复邀约
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print("[达人邀约] 检测到所有店铺已邀约完成，跳过恢复邀约")
            return

        # 防止循环调用
        if hasattr(self, '_resuming_auto_invite') and self._resuming_auto_invite:
            print(f"[达人邀约] 警告：检测到重复的恢复邀约请求，忽略此次调用")
            return

        # 设置处理标志
        self._resuming_auto_invite = True

        try:
            # 表格已经在_clear_table_and_switch_shop方法中清空，这里不需要重复操作
            print("[达人邀约] 表格已在切换店铺时清空")

            # 确保停止之前的邀约线程
            if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
                print("[达人邀约] 检测到正在运行的邀约线程，先停止它...")
                # 临时保存自动邀约标志
                original_auto_inviting = self.is_auto_inviting
                # 设置标志让线程自行退出
                self.is_auto_inviting = False
                # 确保invitation_wait_event被设置，让邀约线程能够继续执行
                if hasattr(self, 'invitation_wait_event'):
                    self.invitation_wait_event.set()
                    print("[达人邀约] 已设置invitation_wait_event，让邀约线程能够继续执行")
                # 等待线程退出
                self.auto_invitation_thread.join(2.0)  # 最多等待2秒
                # 恢复自动邀约标志
                self.is_auto_inviting = original_auto_inviting
                print("[达人邀约] 已停止之前的邀约线程")

            # 更新当前店铺的邀约状态，但不记录到日志
            if self.current_shop_name:
                # 更新当前店铺的邀约状态
                self.current_shop_invite_status = {
                    "status": "开始邀约",
                    "count": 0,
                    "recorded": True  # 设置为True，防止在开始邀约时记录日志
                }
                print(f"[邀约日志] 店铺切换后设置店铺 '{self.current_shop_name}' 的邀约状态，但不记录到日志")

            # 2. 重置状态标志
            self.is_auto_inviting = True
            self.preparing_for_auto_invite = False
            self.switch_shop_requested = False  # 重置切换标志，避免循环切换
            self.current_auto_invite_row = 0  # 重置行号，从头开始邀约

            # 3. 更新UI状态
            self.start_invite_btn.setEnabled(False)
            self.start_invite_btn.setText("邀约中...")
            self.stop_invite_btn.setEnabled(True)

            # 4. 清空商品表格，达人表格已在_clear_table_and_switch_shop方法中清空
            self.selected_products_table.setRowCount(0)  # 清空商品表格
            print("[达人邀约] 已清空商品表格，准备加载新店铺的商品和达人数据")

            # 5. 从数据库加载新店铺的邀约记录并应用筛选条件
            self._load_shop_invitation_record(self.current_shop_name)

            # 6. 设置等待标志并启动数据加载流程
            self.waiting_for_products_then_influencers = True
            print("[达人邀约] 设置waiting_for_products_then_influencers标志，准备加载商品和达人数据")

            # 7. 加载商品数据
            # 商品加载完成后会自动触发达人加载，然后启动邀约线程
            # 流程: refresh_product_list -> _handle_batch_fetch_complete ->
            #       _prepare_and_load_influencers -> on_load_influencers_clicked ->
            #       _handle_batch_influencer_load_result -> _execute_auto_invitation_loop
            self.refresh_product_list()
            print("[达人邀约] 商品列表加载已启动，等待加载完成后自动触发达人加载...")
        finally:
            # 无论如何都要重置处理标志
            self._resuming_auto_invite = False
    # +++ 结束重写 +++

    # +++ 新增: 加载店铺邀约记录的辅助方法 +++
    def _load_shop_invitation_record(self, shop_name):
        """从数据库加载店铺的邀约记录并应用到UI"""
        if not shop_name:
            print("[邀约记录] 错误: 店铺名称为空，无法加载邀约记录")
            return

        print(f"[邀约记录] 开始加载店铺 '{shop_name}' 的邀约记录...")

        try:
            # 使用 /advanced_search 查询特定店铺
            shop_identifier_field = "店铺名称"
            api_url = f"{get_server_url()}/advanced_search?table=视频号邀约账号表"
            payload = {
                shop_identifier_field: {"operator": "=", "value": shop_name},
                "page_size": 1
            }

            print(f"[邀约记录] 发送请求获取店铺 '{shop_name}' 的邀约记录...")
            response = requests.post(api_url, json=payload, timeout=15)
            response.raise_for_status()
            result = response.json()

            if result.get("status") == "success" and result.get("data"):
                # 获取邀约记录
                shop_data = result["data"][0]
                saved_state_string = shop_data.get("邀约记录")
                print(f"[邀约记录] 从数据库获取到邀约记录: '{saved_state_string}'")

                if saved_state_string:
                    # 解析字符串
                    parsed_settings = self._parse_filter_state_string(saved_state_string)
                    print(f"[Parse State] 解析结果: {parsed_settings}")
                    if parsed_settings:
                        # 应用筛选条件到UI
                        print("[邀约记录] 应用店铺邀约记录中的筛选条件...")
                        self._apply_filter_state(parsed_settings)
                        print("已更新邀约设置，使用店铺 '{shop_name}' 的数据")
                    else:
                        print("[邀约记录] 邀约记录解析失败，将使用默认筛选条件")
                else:
                    print("[邀约记录] 邀约记录为空，将使用默认筛选条件")
            else:
                print(f"[邀约记录] 未找到店铺 '{shop_name}' 的记录或API查询失败")
        except Exception as e:
            print(f"[邀约记录] 加载店铺邀约记录时出错: {e}")
            # 出错时继续使用当前筛选条件
    # +++ 结束新增 +++

    # +++ 重命名并修改: 启动新店铺的自动邀约准备 +++
    def start_auto_invite_preparation_for_new_shop(self):
        """在店铺切换后，开始为新店铺准备自动邀约（加载列表）"""
        print("[达人邀约] 接收到启动新店铺自动邀约准备的请求...")

        # --- 移除: 不再检查是否已在进行中，由调用方保证状态已重置 --- #
        # if self.is_auto_inviting or self.preparing_for_auto_invite:
        #      print("[达人邀约] 警告：尝试启动新店铺准备，但当前仍在邀约或准备中。将先停止。")
        #      self._stop_auto_invitation(interrupted=True)
        # --- 结束移除 --- #

        # 1. 强制设置/重置状态标志
        print("[达人邀约] 强制设置准备状态: is_auto_inviting=False, preparing_for_auto_invite=True")
        self.is_auto_inviting = False # 确保邀约状态是 False
        self.preparing_for_auto_invite = True
        self.products_loaded_for_auto_invite = False # 确保重置
        self.influencers_loaded_for_auto_invite = False # 确保重置
        print("[达人邀约] 自动邀约准备标志已设置。")

        # 2. 更新UI
        self.start_invite_btn.setEnabled(False)
        self.start_invite_btn.setText("准备中(新店)...")
        self.stop_invite_btn.setEnabled(True)
        if not self.product_list_btn.isEnabled():
            self.product_list_btn.setEnabled(True)
            self.product_list_btn.setText("商品列表")
        if not self.add_to_list_btn.isEnabled():
             self.add_to_list_btn.setEnabled(True)
             self.add_to_list_btn.setText("加载达人")
        print("[达人邀约] UI状态已更新为准备中。")

        # 3. 设置链式加载标志并尝试触发商品列表加载
        print("[达人邀约] 设置 waiting_for_products_then_influencers = True")
        self.waiting_for_products_then_influencers = True # <--- 确保这行执行
        print("[达人邀约] 开始加载新店铺的商品列表 (调用 refresh_product_list)...")

        # --- 修改: 检查 refresh_product_list 的返回值 ---
        if not self.refresh_product_list():
            # 如果 refresh_product_list 失败 (例如无法获取 Cookie)，则触发店铺切换
            skip_reason = "无法获取Cookie或启动加载失败" # 定义原因
            print(f"[店铺切换] refresh_product_list 失败 ({skip_reason}) for shop '{self.current_shop_name}'. 准备切换店铺...")

            # --- 新增: 记录跳过信息 ---
            if self.current_shop_name:
                self.skipped_shops_info[self.current_shop_name] = skip_reason
                print(f"[Skip Record] 已记录店铺 '{self.current_shop_name}' 跳过，原因: {skip_reason}")
            # --- 结束新增 ---

            # 记录失败的店铺 (如果尚未记录) - 这行现在可以移除或保留用于日志
            if self.original_shop_hit_limit is None:
                self.original_shop_hit_limit = self.current_shop_name
                # print(f"[店铺切换] 记录初始失败/达到上限店铺: {self.original_shop_hit_limit}") # Log only

            # 设置切换标志并发送信号
            print("[店铺切换] 设置切换标志并发送切换信号...")
            self.switch_shop_requested = True

            # 获取下一个未在日志中有记录的店铺
            next_shop = self._get_next_shop_name(self.current_shop_name)
            if next_shop:
                print(f"[邀约日志] 找到下一个未在日志中有记录的店铺: '{next_shop}'，准备切换")
                # 使用新方法清空达人列表并发送切换店铺信号
                self._clear_table_and_switch_shop(next_shop)
            else:
                print("[邀约日志] 无法找到下一个未在日志中有记录的店铺，强制记录当前店铺并停止邀约")
                # 强制记录当前店铺并完成邀约
                self._force_record_current_shop_and_complete("其他错误处理-无下一个店铺")

            # 停止当前店铺的自动邀约/准备流程
            print("[店铺切换] 停止当前店铺的邀约线程，但保持自动邀约状态...")
            self._stop_current_shop_invitation_only(skip_reason) # 传递原因

        # --- 结束修改 ---

    def _stop_current_shop_invitation_only(self, reason="店铺切换"):
        """只停止当前店铺的邀约线程，但保持整个自动邀约流程状态

        这个方法专门用于店铺切换时，避免调用复杂的 _stop_auto_invitation 方法
        """
        print(f"[DEBUG 店铺切换停止] ===== _stop_current_shop_invitation_only 被调用 =====")
        print(f"[DEBUG 店铺切换停止] 停止当前店铺邀约，原因: {reason}")
        print(f"[DEBUG 店铺切换停止] 当前店铺名称: '{self.current_shop_name}'")
        print(f"[DEBUG 店铺切换停止] 当前状态: {self.current_shop_invite_status}")

        # 记录当前店铺的邀约状态到日志
        if self.current_shop_name:
            print(f"[DEBUG 店铺切换停止] 开始记录店铺 '{self.current_shop_name}' 的邀约状态")

            # 根据原因设置状态
            if "达到上限" in reason:
                status = "达到上限"
                count = self.current_shop_invite_status.get("count", 0)
            elif "店铺掉线" in reason:
                status = "店铺掉线"
                count = self.current_shop_invite_status.get("count", 0)
            elif "商品加载失败" in reason or "达人加载失败" in reason:
                status = reason
                count = 0
            else:
                # 如果当前店铺有邀约状态，使用当前状态；否则使用传入的原因
                if self.current_shop_invite_status.get("status"):
                    status = self.current_shop_invite_status.get("status")
                    count = self.current_shop_invite_status.get("count", 0)
                else:
                    status = reason
                    count = self.current_shop_invite_status.get("count", 0)

            # 检查是否已经记录过相同的状态
            already_recorded = self.current_shop_invite_status.get("recorded", False)
            if already_recorded:
                print(f"[店铺切换停止] 店铺 '{self.current_shop_name}' 已记录过，检查是否需要更新状态")
                # 如果状态发生变化，仍然需要记录
                current_status = self.current_shop_invite_status.get("status", "")
                if current_status != status:
                    print(f"[店铺切换停止] 状态发生变化: '{current_status}' -> '{status}'，需要更新记录")
                    already_recorded = False

            if not already_recorded:
                # 添加到邀约日志
                print(f"[店铺切换停止] 记录店铺 '{self.current_shop_name}' 状态: {status}, 次数: {count}")
                InviteLogDialog.add_log_entry(self.current_shop_name, status, count)
                self.current_shop_invite_status["recorded"] = True
                self.current_shop_invite_status["status"] = status
                self.current_shop_invite_status["count"] = count
                print(f"[店铺切换停止] 已记录店铺 '{self.current_shop_name}' 状态: {status}, 次数: {count}")
            else:
                print(f"[店铺切换停止] 店铺 '{self.current_shop_name}' 状态未变化，跳过重复记录")
        else:
            print(f"[DEBUG 店铺切换停止] 警告：current_shop_name 为空，无法记录日志")

        # 停止邀约线程但保持自动邀约状态
        if hasattr(self, 'auto_invitation_thread') and self.auto_invitation_thread and self.auto_invitation_thread.is_alive():
            print(f"[DEBUG 店铺切换停止] 检测到邀约线程正在运行，需要停止")
            print(f"[DEBUG 店铺切换停止] 停止前状态: is_auto_inviting={self.is_auto_inviting}")
            # 临时设置标志让线程退出
            original_auto_inviting = self.is_auto_inviting
            self.is_auto_inviting = False

            # 设置事件让线程继续执行并退出
            if hasattr(self, 'invitation_wait_event'):
                self.invitation_wait_event.set()

            # 等待线程退出
            self.auto_invitation_thread.join(2.0)

            # 恢复自动邀约状态
            self.is_auto_inviting = original_auto_inviting
            print(f"[DEBUG 店铺切换停止] 邀约线程已停止，自动邀约状态已恢复: {self.is_auto_inviting}")
        else:
            print(f"[DEBUG 店铺切换停止] 没有检测到运行中的邀约线程")
            print(f"[DEBUG 店铺切换停止] 线程状态: hasattr={hasattr(self, 'auto_invitation_thread')}, thread={getattr(self, 'auto_invitation_thread', None)}, is_alive={getattr(self, 'auto_invitation_thread', None) and self.auto_invitation_thread.is_alive() if hasattr(self, 'auto_invitation_thread') else False}")
            print(f"[DEBUG 店铺切换停止] 当前 is_auto_inviting 状态: {self.is_auto_inviting}")

            # 如果没有邀约线程但是 is_auto_inviting 为 False，这可能是问题所在
            # 在店铺切换时，我们应该保持 is_auto_inviting 为 True
            if not self.is_auto_inviting:
                print(f"[DEBUG 店铺切换停止] 警告：is_auto_inviting 为 False，但这是店铺切换，应该保持为 True")
                # 检查是否应该恢复 is_auto_inviting 状态
                if hasattr(self, 'switch_shop_requested') and self.switch_shop_requested:
                    print(f"[DEBUG 店铺切换停止] 检测到店铺切换请求，恢复 is_auto_inviting 为 True")
                    self.is_auto_inviting = True

        # 重置准备状态
        self.preparing_for_auto_invite = False

        # 重置当前行号为0，新店铺从头开始
        self.current_auto_invite_row = 0
        print(f"[店铺切换停止] 重置行号为0，新店铺将从头开始邀约")

        # 不更新按钮状态，保持邀约中的状态
        print(f"[店铺切换停止] 保持按钮状态不变，继续显示邀约中...")

    # +++ 结束修改 +++

    # +++ 移除旧的 resume_auto_invite 方法 +++
    # def resume_auto_invite(self):
    #     ... (旧代码) ...
    # +++ 结束移除 +++

    def refresh_product_list(self):
        print("[达人邀约] refresh_product_list: 尝试刷新商品列表...") # 添加日志
        # --- 添加检查 --- #
        if not self.current_shop_name:
            print("[达人邀约] refresh_product_list 错误：当前店铺未设置。")
            return False

        # 尝试获取Cookie，但不在这里处理所有Cookie逻辑，交给 on_load_products_clicked
        cookie_data = self.main_window_ref.get_cookies_for_shop(self.current_shop_name) if self.main_window_ref else None
        if not cookie_data:
            print(f"[达人邀约] refresh_product_list 错误：无法获取店铺 '{self.current_shop_name}' 的Cookie。")
            return False # <--- 确保在获取Cookie失败时返回 False
        # --- 结束检查 --- #

        # 清空表格
        # self.selected_products_table.clear() # <-- 错误的方法，会清除表头
        self.selected_products_table.setRowCount(0) # 使用 setRowCount(0) 只清除数据行
        print("[达人邀约] refresh_product_list: 商品表格数据已清空 (使用 setRowCount)。") # 修改日志

        # 调用加载方法
        print("[达人邀约] refresh_product_list: 调用 on_load_products_clicked 以启动加载...")
        # 检查是否已经在加载商品，防止重复加载
        if self.product_list_btn.text() == "加载中...":
            print("[达人邀约] refresh_product_list: 商品正在加载中，跳过重复加载请求")
            return True  # 返回True表示已经在加载中，不是失败
        self.on_load_products_clicked() # 直接调用加载商品的方法

        # 只要能成功调用加载方法，就认为启动成功
        print("[达人邀约] refresh_product_list: 加载已启动，返回 True。") # 添加日志
        return True

    # +++ 新增: 获取当前筛选状态的方法 +++
    def _get_current_filter_state(self):
        """获取当前UI界面上的筛选条件，并格式化为字符串。"""
        state = {}

        # 1. 类目
        selected_categories = []
        if hasattr(self, 'category_checkboxes'):
            for checkbox in self.category_checkboxes:
                if checkbox.isChecked():
                    selected_categories.append(checkbox.text())
        state['类目'] = ', '.join(selected_categories) if selected_categories else '不限'

        # 2. 页数 (当前达人列表的页码)
        state['页数'] = self.current_influencer_page if hasattr(self, 'current_influencer_page') else '未知'

        # 辅助函数获取单选按钮组选中的文本
        def get_checked_radio_text(group_attribute_prefix):
            for suffix in ['unlimited', 'less_1w', '1w_10w', '10w_50w', '50w_100w', 'more_50w', 'more_100w', 'less_100', '100_200', 'more_200', '1w_10w', 'more_10w', 'less_18', '18_24', '25_39', '40_49', 'more_50']:
                radio_button = getattr(self, f"{group_attribute_prefix}_{suffix}", None)
                if radio_button and radio_button.isChecked():
                    return radio_button.text()
            return '未知'

        # 3. 粉丝
        state['粉丝数'] = get_checked_radio_text('fans')

        # 4. 销售额
        state['销售额'] = get_checked_radio_text('sales')

        # 5. 客单价
        state['客单价'] = get_checked_radio_text('price')

        # 6. 粉丝年龄
        state['粉丝年龄'] = get_checked_radio_text('age')

        # 7. 场均销售额
        state['场均销售额'] = get_checked_radio_text('avg_sales')

        # 8. 其他勾选项 (如果需要可以添加)
        state['有推荐方式'] = '是' if self.has_recommendation.isChecked() else '否'
        state['有视频号认证'] = '是' if self.has_certification.isChecked() else '否'
        state['达人性别'] = self.gender_combo.currentText()

        # 格式化为字符串
        state_string = '; '.join([f"{k}: {v}" for k, v in state.items()])
        print(f"[Filter State] 获取到的状态: {state_string}")
        return state_string
    # +++ 结束新增 +++

    # +++ 新增: 解析状态字符串为字典 +++
    def _parse_filter_state_string(self, state_string):
        """将数据库中存储的状态字符串解析回字典。"""
        settings = {}
        if not state_string or not isinstance(state_string, str):
            return settings # 返回空字典如果输入无效
        try:
            parts = state_string.split('; ')
            for part in parts:
                if ': ' in part:
                    key, value = part.split(': ', 1)
                    settings[key.strip()] = value.strip()
        except Exception as e:
            print(f"[Parse State] 解析状态字符串 '{state_string[:50]}...' 时出错: {e}")
            return {} # 出错时返回空字典
        print(f"[Parse State] 解析结果: {settings}")
        return settings
    # +++ 结束新增 +++

    # +++ 新增: 记录邀约上限状态到数据库的方法 +++
    def _record_limit_reached_state(self):
        """获取当前筛选状态并启动后台线程更新数据库。"""
        print("[DB Record] 准备记录邀约上限状态...")
        if not self.current_shop_name:
            print("[DB Record] 错误: current_shop_name 未设置，无法记录。")
            return

        # 1. 获取状态字符串
        filter_state_str = self._get_current_filter_state()

        # 2. 准备 API 请求数据
        # !!! 重要: 请确认 '视频号邀约账号表' 中用于匹配店铺的准确字段名 !!!
        shop_identifier_field = "店铺名称" # <--- 假设字段名为 店铺名称
        api_url = "http://150.158.14.242:8000/database?action=update&table=视频号邀约账号表"
        payload = {
            "conditions": {shop_identifier_field: self.current_shop_name},
            "values": {"邀约记录": filter_state_str}
        }

        # 3. 定义后台工作函数
        def _update_db_worker(url, data):
            print(f"[DB Worker] 尝试更新店铺 '{self.current_shop_name}' 的邀约记录...")
            try:
                response = requests.put(url, json=data, timeout=10)
                response.raise_for_status() # 检查 HTTP 错误
                result = response.json()
                if result.get("status") == "success":
                    print(f"[DB Worker] 店铺 '{self.current_shop_name}' 邀约记录更新成功。 受影响行数: {result.get('affected_rows')}")
                else:
                    print(f"[DB Worker] 店铺 '{self.current_shop_name}' 邀约记录更新失败: {result.get('message')}")
            except requests.exceptions.RequestException as e:
                print(f"[DB Worker] 更新邀约记录时请求错误: {e}")
            except json.JSONDecodeError as e:
                print(f"[DB Worker] 解析数据库API响应时出错: {e} - 响应内容: {response.text[:200]}...")
            except Exception as e:
                print(f"[DB Worker] 更新邀约记录时发生未知错误: {e}")

        # 4. 启动后台线程
        try:
            db_thread = threading.Thread(target=_update_db_worker, args=(api_url, payload), daemon=True)
            db_thread.start()
            print("[DB Record] 后台数据库更新线程已启动。")
        except Exception as e:
            print(f"[DB Record] 启动数据库更新线程时出错: {e}")
    # +++ 结束新增 +++

    # +++ 新增: 后台线程加载数据库状态 +++
    def _load_db_state_worker(self):
        """后台线程：从数据库获取指定店铺的邀约记录并解析。"""
        print(f"[DB Load Worker] 开始为店铺 '{self.current_shop_name}' 加载邀约记录...")
        if not self.current_shop_name:
            print("[DB Load Worker] 错误: current_shop_name 未设置。")
            self.db_state_load_failed_signal.emit()
            return

        # 使用 /advanced_search 查询特定店铺
        # !!! 重要: 再次确认 '视频号邀约账号表' 中用于匹配店铺的准确字段名 !!!
        shop_identifier_field = "店铺名称" # <--- 假设字段名为 店铺名称
        api_url = f"{get_server_url()}/advanced_search?table=视频号邀约账号表"
        payload = {
            # --- 修改: 移除 conditions 结构，直接放条件 --- #
            shop_identifier_field: {"operator": "=", "value": self.current_shop_name},
            # --- 结束修改 --- #
            "page_size": 1 # 只需要一条记录
        }
        print(f"[DB Load Worker] 发送请求到 {api_url}，payload: {payload}") # 调试日志

        try:
            response = requests.post(api_url, json=payload, timeout=15) # 增加超时
            response.raise_for_status()
            result = response.json()
            print(f"[DB Load Worker] API 响应: {result}") # 调试日志

            if result.get("status") == "success" and result.get("data"):
                # 假设店铺名称是唯一的，取第一条记录
                shop_data = result["data"][0]
                saved_state_string = shop_data.get("邀约记录")
                print(f"[DB Load Worker] 从数据库获取到邀约记录: '{saved_state_string[:100]}...'")

                if saved_state_string:
                    # 解析字符串
                    parsed_settings = self._parse_filter_state_string(saved_state_string)
                    if parsed_settings: # 确保解析成功
                        self.apply_db_state_signal.emit(parsed_settings)
                        return # 成功，结束 worker
                    else:
                        print("[DB Load Worker] 邀约记录解析失败。")
                else:
                    print("[DB Load Worker] 邀约记录为空。")
            else:
                print(f"[DB Load Worker] 未找到店铺 '{self.current_shop_name}' 的记录或API查询失败: {result.get('message')}")

        except requests.exceptions.Timeout:
             print(f"[DB Load Worker] 加载邀约记录时请求超时。") # 超时错误
        except requests.exceptions.RequestException as e:
            print(f"[DB Load Worker] 加载邀约记录时请求错误: {e}")
        except json.JSONDecodeError as e:
             # 尝试打印原始响应文本以帮助调试
             raw_response = "<无法获取响应文本>"
             try:
                 raw_response = response.text
             except Exception:
                 pass
             print(f"[DB Load Worker] 解析数据库API响应时出错: {e} - 响应内容: {raw_response[:200]}...")
        except Exception as e:
            print(f"[DB Load Worker] 加载邀约记录时发生未知错误: {e}")

        # 如果任何步骤失败或未找到记录，则发送失败信号
        print("[DB Load Worker] 发送加载失败信号。")
        self.db_state_load_failed_signal.emit()
    # +++ 结束新增 +++

    # +++ 新增: 数据库状态加载成功/失败的槽函数 +++
    def _on_apply_db_state(self, settings_dict):
        """槽函数：处理加载到的数据库状态。"""
        print("[Main Thread] 收到加载成功的数据库状态，准备应用...")
        # 1. 应用状态到UI (使用新方法名)
        self._apply_filter_state(settings_dict)

        # 2. 设置链式加载标志并触发商品加载
        print("[Main Thread] 应用状态后，设置标志并触发商品加载...")
        # 检查是否已经在加载商品，防止重复加载
        if self.product_list_btn.text() == "加载中...":
            print("[Main Thread] 商品正在加载中，跳过重复加载请求")
            return
        self.waiting_for_products_then_influencers = True
        self.on_load_products_clicked() # 先加载商品
        # 后续流程: _handle_batch_fetch_complete -> _prepare_and_load_influencers -> on_load_influencers_clicked -> _handle_batch_influencer_load_result -> _execute_auto_invitation_loop

    def _on_db_state_load_failed(self):
        """槽函数：处理数据库状态加载失败。"""
        print("[Main Thread] 加载数据库状态失败或未找到记录，将使用当前UI设置。")
        # 同样设置链式加载标志并触发商品加载（使用当前UI设置）
        print("[Main Thread] 加载失败后，设置标志并触发商品加载...")
        # 检查是否已经在加载商品，防止重复加载
        if self.product_list_btn.text() == "加载中...":
            print("[Main Thread] 商品正在加载中，跳过重复加载请求")
            return
        self.waiting_for_products_then_influencers = True
        self.on_load_products_clicked() # 先加载商品
        # 后续流程同上

    def reload_shop_list(self):
        """重新加载店铺列表和店铺分类"""
        print("[店铺下拉框] 重新加载店铺列表和分类")

        # 设置标志，防止触发信号处理
        self._updating_shop = True
        self._updating_category = True

        # 保存当前选中的店铺和分类
        current_shop = self.shop_combo.currentText() if self.shop_combo.count() > 0 else ""
        current_category = self.shop_category_combo.currentText() if self.shop_category_combo.count() > 0 else ""

        # 清空下拉框
        self.shop_combo.clear()
        self.shop_combo.addItem("当前店铺")  # 默认选项

        self.shop_category_combo.clear()
        self.shop_category_combo.addItem("全部分类")  # 默认选项

        # 使用ShopManager获取店铺列表
        print("[店铺下拉框] 使用ShopManager获取店铺列表")

        try:
            shop_list = ShopManager.get_shop_list()
            print(f"[店铺下拉框] 获取到 {len(shop_list)} 个店铺")

            # 收集所有店铺分类
            categories = set()

            # 遍历并添加店铺
            shop_count = 0
            for shop in shop_list:
                # 添加店铺到下拉框
                shop_name = shop.get("店铺名称", "")
                if shop_name:
                    self.shop_combo.addItem(shop_name)
                    shop_count += 1
                    print(f"[店铺下拉框] 添加店铺: {shop_name}")

                # 收集店铺分类
                shop_category = shop.get("店铺分类", "")
                if shop_category:
                    categories.add(shop_category)
                    print(f"[店铺下拉框] 收集到分类: {shop_category}")

            print(f"[店铺下拉框] 总共添加了 {shop_count} 个店铺")
            
            # 更新自动补全器的数据模型
            if hasattr(self, 'shop_combo') and self.shop_combo.completer():
                # 获取所有店铺名称用于自动补全
                shop_names = []
                for i in range(self.shop_combo.count()):
                    shop_names.append(self.shop_combo.itemText(i))
                
                # 更新补全器的模型
                completer_model = QStringListModel(shop_names)
                self.shop_combo.completer().setModel(completer_model)
                print(f"[店铺下拉框] 已更新自动补全器数据，包含 {len(shop_names)} 个店铺")

            # 添加所有分类到分类下拉框
            category_count = 0
            for category in sorted(categories):
                self.shop_category_combo.addItem(category)
                category_count += 1
                print(f"[店铺下拉框] 添加分类: {category}")

            print(f"[店铺下拉框] 总共添加了 {category_count} 个分类")

            # 恢复之前选中的店铺
            if current_shop and current_shop != "当前店铺":
                index = self.shop_combo.findText(current_shop)
                if index >= 0:
                    self.shop_combo.blockSignals(True)
                    self.shop_combo.setCurrentIndex(index)
                    self.shop_combo.blockSignals(False)
                    print(f"[店铺下拉框] 恢复选中店铺: {current_shop}")

            # 恢复之前选中的分类
            if current_category and current_category != "全部分类":
                index = self.shop_category_combo.findText(current_category)
                if index >= 0:
                    self.shop_category_combo.blockSignals(True)
                    self.shop_category_combo.setCurrentIndex(index)
                    self.shop_category_combo.blockSignals(False)
                    print(f"[店铺下拉框] 恢复选中分类: {current_category}")

        except Exception as e:
            print(f"[店铺下拉框] 获取店铺列表时出错: {str(e)}")
            import traceback
            traceback.print_exc()

        # 重置标志，允许信号处理
        self._updating_shop = False
        self._updating_category = False

    def _on_save_button_clicked(self):
        """处理保存按钮点击事件，保存达人属性和类目信息到账号管理的邀约记录字段"""
        # 获取当前选中的店铺
        selected_shop = self.shop_combo.currentText()
        # 获取当前选中的店铺分类
        selected_category = self.shop_category_combo.currentText()

        # 检查是否选择了店铺或分类
        if (not selected_shop or selected_shop == "当前店铺") and (not selected_category or selected_category == "全部分类"):
            # 如果既没有选择店铺也没有选择分类，则提示用户
            QMessageBox.warning(self, "提示", "请选择一个店铺或一个店铺分类")
            return

        # 如果选择了店铺但没有选择分类，则只保存选中店铺的邀约记录
        save_single_shop = (selected_shop and selected_shop != "当前店铺" and (not selected_category or selected_category == "全部分类"))

        if save_single_shop:
            print(f"[保存按钮] 准备保存达人属性和类目信息到单个店铺: {selected_shop}")
        else:
            print(f"[保存按钮] 准备保存达人属性和类目信息到分类: {selected_category}")

        # 导入随机模块
        import random

        # 定义粉丝数选项，后面会为每个店铺单独随机选择
        fans_options = ["不限", "1万以下", "1万-10万", "10万-50万", "50万以上"]
        print("[保存按钮] 将为每个店铺随机生成页数和粉丝数")

        # 收集达人属性和类目信息
        print("[保存按钮] 开始收集达人属性和类目信息")

        # 1. 达人性别
        gender = self.gender_combo.currentText()
        print(f"[保存按钮] 达人性别: {gender}")

        # 2. 收集单选按钮组的选中值
        # 粉丝数 - 将在每个店铺的循环中随机选择

        # 销售额
        sales = self._get_checked_radio_text("sales")
        print(f"[保存按钮] 销售额: {sales}")

        # 客单价
        price = self._get_checked_radio_text("price")
        print(f"[保存按钮] 客单价: {price}")

        # 粉丝年龄
        fans_age = self._get_checked_radio_text("age")
        print(f"[保存按钮] 粉丝年龄: {fans_age}")

        # 场均销售额
        avg_sales = self._get_checked_radio_text("avg_sales")
        print(f"[保存按钮] 场均销售额: {avg_sales}")

        # 3. 收集复选框的选中状态
        has_recommendation = "是" if self.has_recommendation.isChecked() else "否"
        has_certification = "是" if self.has_certification.isChecked() else "否"
        print(f"[保存按钮] 有推荐方式: {has_recommendation}")
        print(f"[保存按钮] 有视频号认证: {has_certification}")

        # 4. 收集类目复选框的选中状态
        selected_categories = []
        if hasattr(self, 'category_checkboxes'):
            print(f"[保存按钮] 类目复选框数量: {len(self.category_checkboxes)}")
            for i, checkbox in enumerate(self.category_checkboxes):
                print(f"[保存按钮] 类目 {i+1}: {checkbox.text()} - 选中状态: {checkbox.isChecked()}")
                if checkbox.isChecked():
                    selected_categories.append(checkbox.text())
        else:
            print("[保存按钮] 警告: 类目复选框不存在")

        # 如果没有选择任何类目，则使用"不限"
        if not selected_categories:
            category = "不限"
            print("[保存按钮] 未选择任何类目，使用默认值: 不限")
        else:
            category = ", ".join(selected_categories)
            print(f"[保存按钮] 选中的类目: {category}")

        # 收集基本信息，随机部分将在每个店铺的循环中生成
        base_info = {
            "category": category,
            "sales": sales,
            "price": price,
            "fans_age": fans_age,
            "avg_sales": avg_sales,
            "has_recommendation": has_recommendation,
            "has_certification": has_certification,
            "gender": gender
        }

        print(f"[保存按钮] 收集的基本信息: {base_info}")

        # 使用ShopManager保存邀约记录字段
        try:
            # 获取所有店铺信息
            all_shops = ShopManager.get_shop_list()

            # 根据保存模式选择要保存的店铺
            shops_to_save = []

            if save_single_shop:
                # 只保存选中的单个店铺
                for shop in all_shops:
                    if shop.get("店铺名称") == selected_shop:
                        shops_to_save.append(shop)
                        break

                if not shops_to_save:
                    print(f"[保存按钮] 未找到店铺 '{selected_shop}'")
                    QMessageBox.warning(self, "保存失败", f"未找到店铺 '{selected_shop}'")
                    return

                print(f"[保存按钮] 找到店铺 '{selected_shop}'，准备保存")
            else:
                # 保存分类下的所有店铺
                for shop in all_shops:
                    shop_category = shop.get("店铺分类", "")
                    if shop_category == selected_category:
                        shops_to_save.append(shop)

                if not shops_to_save:
                    print(f"[保存按钮] 未找到属于分类 '{selected_category}' 的店铺")
                    QMessageBox.warning(self, "保存失败", f"未找到属于分类 '{selected_category}' 的店铺")
                    return

                print(f"[保存按钮] 找到 {len(shops_to_save)} 个属于分类 '{selected_category}' 的店铺")

            # 如果主窗口引用存在，使用主窗口的服务器URL
            if self.main_window_ref and hasattr(self.main_window_ref, "server_url"):
                server_url = self.main_window_ref.server_url

                # 构建更新URL
                update_url = f"{server_url}/database"
                update_params = {'action': 'update', 'table': '视频号邀约账号表'}

                # 记录成功和失败的店铺
                success_shops = []
                failed_shops = []

                # 遍历所有需要保存的店铺，更新邀约记录
                for shop in shops_to_save:
                    shop_name = shop.get("店铺名称", "未知店铺")
                    shop_id = shop.get("ID")

                    if not shop_id:
                        print(f"[保存按钮] 店铺 '{shop_name}' 缺少ID，跳过")
                        failed_shops.append(f"{shop_name}(缺少ID)")
                        continue

                    try:
                        # 根据保存模式决定是否随机生成粉丝数
                        if save_single_shop:
                            # 单个店铺保存时，使用用户勾选的粉丝数值
                            fans_value = self._get_checked_radio_text("fans")
                            page_num = 1  # 单个店铺保存时，页数默认为1
                            print(f"[保存按钮] 单个店铺保存模式，使用勾选的粉丝数: '{fans_value}', 页数: {page_num}")
                        else:
                            # 分类保存时，随机生成页数和粉丝数
                            page_num = random.randint(1, 50)
                            fans_value = random.choice(fans_options)
                            print(f"[保存按钮] 分类保存模式，随机生成粉丝数: '{fans_value}', 页数: {page_num}")

                        # 为店铺生成邀约记录
                        invitation_record = (
                            f"类目: {base_info['category']}; "
                            f"页数: {page_num}; "
                            f"粉丝数: {fans_value}; "
                            f"销售额: {base_info['sales']}; "
                            f"客单价: {base_info['price']}; "
                            f"粉丝年龄: {base_info['fans_age']}; "
                            f"场均销售额: {base_info['avg_sales']}; "
                            f"有推荐方式: {base_info['has_recommendation']}; "
                            f"有视频号认证: {base_info['has_certification']}; "
                            f"达人性别: {base_info['gender']}"
                        )

                        print(f"[保存按钮] 店铺 '{shop_name}' 的页数: {page_num}, 粉丝数: {fans_value}")

                        # 构建更新条件和值
                        update_conditions = {"ID": shop_id}
                        update_values = {"邀约记录": invitation_record}

                        # 构建请求体
                        update_payload = {
                            'conditions': update_conditions,
                            'values': update_values
                        }

                        # 发送更新请求
                        import requests
                        response = requests.put(
                            update_url,
                            params=update_params,
                            json=update_payload,
                            timeout=10
                        )

                        # 检查响应
                        if response.status_code == 200:
                            result = response.json()
                            if result.get("status") == "success":
                                print(f"[保存按钮] 成功更新店铺 '{shop_name}' 的邀约记录")
                                success_shops.append(shop_name)
                            else:
                                error_msg = result.get("message", "未知错误")
                                print(f"[保存按钮] 更新店铺 '{shop_name}' 的邀约记录失败: {error_msg}")
                                failed_shops.append(f"{shop_name}({error_msg})")
                        else:
                            print(f"[保存按钮] 更新店铺 '{shop_name}' 的邀约记录失败，状态码: {response.status_code}")
                            failed_shops.append(f"{shop_name}(状态码:{response.status_code})")
                    except Exception as e:
                        print(f"[保存按钮] 更新店铺 '{shop_name}' 的邀约记录时发生异常: {str(e)}")
                        failed_shops.append(f"{shop_name}({str(e)})")

                # 显示结果
                if success_shops:
                    success_msg = f"成功更新 {len(success_shops)} 个店铺的邀约记录"
                    if len(success_shops) <= 5:
                        success_msg += f"：\n{', '.join(success_shops)}"

                    if failed_shops:
                        fail_msg = f"\n\n失败 {len(failed_shops)} 个店铺"
                        if len(failed_shops) <= 5:
                            fail_msg += f"：\n{', '.join(failed_shops)}"
                        success_msg += fail_msg

                    QMessageBox.information(self, "保存结果", success_msg)
                else:
                    fail_msg = f"所有 {len(failed_shops)} 个店铺更新失败"
                    if len(failed_shops) <= 5:
                        fail_msg += f"：\n{', '.join(failed_shops)}"
                    QMessageBox.warning(self, "保存失败", fail_msg)
            else:
                print("[保存按钮] 无法获取服务器URL")
                QMessageBox.warning(self, "保存失败", "无法获取服务器URL")
        except Exception as e:
            print(f"[保存按钮] 保存邀约记录时发生错误: {e}")
            QMessageBox.warning(self, "保存失败", f"保存邀约记录时发生错误: {e}")

    def _on_category_selection_changed(self, index):
        """处理店铺分类下拉框的变化"""
        # 防止递归调用
        if hasattr(self, '_updating_category') and self._updating_category:
            print("[分类选择] 正在更新分类，忽略此次调用")
            return

        self._updating_category = True

        try:
            if index == 0:  # "全部分类"选项
                # 重新加载所有店铺
                print("[分类选择] 用户选择了全部分类，重新加载所有店铺")
                # 不要调用reload_shop_list()，它会重置分类选择
                # 而是直接加载所有店铺

                # 保存当前选中的店铺
                current_shop = self.shop_combo.currentText() if self.shop_combo.count() > 0 else ""

                # 清空店铺下拉框
                self.shop_combo.clear()
                self.shop_combo.addItem("当前店铺")  # 默认选项

                # 使用ShopManager获取所有店铺
                try:
                    shop_list = ShopManager.get_shop_list()

                    # 遍历并添加所有店铺
                    shop_count = 0
                    for shop in shop_list:
                        shop_name = shop.get("店铺名称", "")
                        if shop_name:
                            self.shop_combo.addItem(shop_name)
                            shop_count += 1

                    print(f"[分类选择] 总共添加了 {shop_count} 个店铺")
                    
                    # 更新自动补全器的数据模型
                    if hasattr(self, 'shop_combo') and self.shop_combo.completer():
                        # 获取所有店铺名称用于自动补全
                        shop_names = []
                        for i in range(self.shop_combo.count()):
                            shop_names.append(self.shop_combo.itemText(i))
                        
                        # 更新补全器的模型
                        completer_model = QStringListModel(shop_names)
                        self.shop_combo.completer().setModel(completer_model)
                        print(f"[分类选择] 已更新自动补全器数据，包含 {len(shop_names)} 个店铺")

                    # 尝试恢复之前选中的店铺
                    if current_shop and current_shop != "当前店铺":
                        index = self.shop_combo.findText(current_shop)
                        if index >= 0:
                            self.shop_combo.blockSignals(True)
                            self.shop_combo.setCurrentIndex(index)
                            self.shop_combo.blockSignals(False)
                            print(f"[分类选择] 恢复选中店铺: {current_shop}")
                except Exception as e:
                    print(f"[分类选择] 加载所有店铺时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                return

            # 获取选中的分类
            selected_category = self.shop_category_combo.currentText()
            if not selected_category or selected_category == "全部分类":
                return

            print(f"[分类选择] 用户选择了分类: {selected_category}")

            # 保存当前选中的店铺
            current_shop = self.shop_combo.currentText() if self.shop_combo.count() > 0 else ""

            # 清空店铺下拉框
            self.shop_combo.clear()
            self.shop_combo.addItem("当前店铺")  # 默认选项

            # 使用ShopManager获取店铺列表并按分类筛选
            try:
                shop_list = ShopManager.get_shop_list()

                # 遍历并添加符合分类的店铺
                shop_count = 0
                for shop in shop_list:
                    shop_category = shop.get("店铺分类", "")
                    if shop_category == selected_category:
                        shop_name = shop.get("店铺名称", "")
                        if shop_name:
                            self.shop_combo.addItem(shop_name)
                            shop_count += 1
                            print(f"[分类选择] 添加符合分类 '{selected_category}' 的店铺: {shop_name}")

                print(f"[分类选择] 总共添加了 {shop_count} 个符合分类 '{selected_category}' 的店铺")
                
                # 更新自动补全器的数据模型
                if hasattr(self, 'shop_combo') and self.shop_combo.completer():
                    # 获取所有店铺名称用于自动补全
                    shop_names = []
                    for i in range(self.shop_combo.count()):
                        shop_names.append(self.shop_combo.itemText(i))
                    
                    # 更新补全器的模型
                    completer_model = QStringListModel(shop_names)
                    self.shop_combo.completer().setModel(completer_model)
                    print(f"[分类选择] 已更新自动补全器数据，包含 {len(shop_names)} 个店铺")

                # 尝试恢复之前选中的店铺（如果它符合当前分类）
                if current_shop and current_shop != "当前店铺":
                    index = self.shop_combo.findText(current_shop)
                    if index >= 0:
                        self.shop_combo.blockSignals(True)
                        self.shop_combo.setCurrentIndex(index)
                        self.shop_combo.blockSignals(False)
                        print(f"[分类选择] 恢复选中店铺: {current_shop}")
            except Exception as e:
                print(f"[分类选择] 筛选店铺时出错: {str(e)}")
                import traceback
                traceback.print_exc()
        finally:
            # 无论如何都要重置标志
            self._updating_category = False

    def _on_shop_selection_changed(self, index):
        """处理店铺选择下拉框的变化（已禁用，达人邀约功能现在独立）"""
        print("[店铺选择] 达人邀约功能已独立，不响应主窗口店铺选择变化")
        return

    def _on_internal_shop_selection_changed(self, index):
        """处理达人邀约页面内部的店铺选择下拉框变化"""
        # 防止递归调用
        if hasattr(self, '_updating_internal_shop') and self._updating_internal_shop:
            print("[内部店铺选择] 正在更新店铺，忽略此次调用")
            return

        self._updating_internal_shop = True

        try:
            if index == 0:  # "当前店铺"选项
                print("[内部店铺选择] 选择了'当前店铺'选项，保持当前状态")
                return

            # 获取选中的店铺名称
            selected_shop = self.shop_combo.currentText()
            if not selected_shop or selected_shop == "当前店铺":
                return

            print(f"[内部店铺选择] 用户在达人邀约页面选择了店铺: {selected_shop}")

            # 重新读取店铺信息和最新cookie
            print(f"[内部店铺选择] 🔄 重新从config\\账号列表.json读取店铺 '{selected_shop}' 的最新信息...")
            shop_info = ShopManager.get_shop_by_name(selected_shop)
            if not shop_info:
                print(f"[内部店铺选择] ❌ 错误：未找到店铺 '{selected_shop}' 的信息")
                QMessageBox.warning(self, "错误", f"未找到店铺 '{selected_shop}' 的信息")
                return

            # 验证cookie信息
            cookies_list = shop_info.get('cookies_list', [])
            if not cookies_list:
                print(f"[内部店铺选择] ⚠️ 警告：店铺 '{selected_shop}' 没有Cookie信息")
            else:
                cookie_dict = {c['name']: c['value'] for c in cookies_list}
                biz_magic = cookie_dict.get('biz_magic', '')
                biz_token = cookie_dict.get('biz_token', '')
                print(f"[内部店铺选择] 🍪 Cookie验证结果:")
                print(f"[内部店铺选择]   - biz_magic: {'✅ 有效' if biz_magic else '❌ 缺失'}")
                print(f"[内部店铺选择]   - biz_token: {'✅ 有效' if biz_token else '❌ 缺失'}")

            # 更新当前店铺名称
            old_shop = self.current_shop_name
            self.current_shop_name = selected_shop
            print(f"[内部店铺选择] 店铺已从 '{old_shop}' 切换到 '{selected_shop}'")

            # 更新邀约设置
            self.update_invitation_settings(selected_shop)
            print(f"[内部店铺选择] 已更新邀约设置")

            # 清空商品列表
            self.selected_products_table.setRowCount(0)
            product_headers = ["序号", "商品ID", "商品标题", "价格", "佣金", "店铺"]
            self.selected_products_table.setHorizontalHeaderLabels(product_headers)

            # 清空达人列表
            self.table.setRowCount(0)

            # 重置页码
            self.page_input.setText("0")
            self.prev_page_btn.setEnabled(False)

            # 重置邀约状态
            self.current_shop_invite_status = {
                "status": "",
                "count": 0,
                "recorded": False
            }

            print(f"[内部店铺选择] 已切换到店铺: {selected_shop}，界面已重置")

        except Exception as e:
            print(f"[内部店铺选择] 处理店铺选择变化时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # 无论如何都要重置标志
            self._updating_internal_shop = False

    def _update_internal_shop_combo(self, shop_name):
        """更新达人邀约页面内部的店铺下拉框显示当前店铺"""
        if not hasattr(self, 'shop_combo'):
            return

        # 阻止信号触发，避免循环调用
        self.shop_combo.blockSignals(True)

        try:
            # 查找匹配的店铺索引
            found_index = -1
            for i in range(self.shop_combo.count()):
                if self.shop_combo.itemText(i) == shop_name:
                    found_index = i
                    break

            # 如果找到匹配项，选中它
            if found_index >= 0:
                self.shop_combo.setCurrentIndex(found_index)
                print(f"[内部下拉框] 店铺下拉框已更新为: {shop_name} (索引: {found_index})")
            else:
                # 如果没有找到匹配项，选择"当前店铺"选项
                self.shop_combo.setCurrentIndex(0)
                print(f"[内部下拉框] 未在下拉框中找到店铺 {shop_name}，已选择'当前店铺'选项")

        except Exception as e:
            print(f"[内部下拉框] 更新店铺下拉框时出错: {str(e)}")
        finally:
            # 恢复信号
            self.shop_combo.blockSignals(False)

    def _check_timer_invitation(self):
        """检查是否到达定时邀约时间"""
        try:
            # 检查定时邀约是否启用
            if not hasattr(self, 'timer_checkbox'):
                # 静默返回，不打印消息
                return

            # 只有在复选框勾选时才进行检查，未勾选时静默返回
            if not self.timer_checkbox.isChecked():
                return

            # 检查是否已经在邀约中（包括单店邀约）
            if self.is_auto_inviting or (hasattr(self, 'is_single_shop_inviting') and self.is_single_shop_inviting):
                print(f"[定时邀约] 已在邀约中（自动邀约: {self.is_auto_inviting}, 单店邀约: {getattr(self, 'is_single_shop_inviting', False)}），跳过定时检查")
                return

            # 检查是否正在准备邀约
            if hasattr(self, 'preparing_for_auto_invite') and self.preparing_for_auto_invite:
                print(f"[定时邀约] 正在准备邀约中，跳过定时检查")
                return

            # 获取当前时间
            current_time = QDateTime.currentDateTime()
            current_hour = current_time.time().hour()
            current_minute = current_time.time().minute()

            # 获取设置的邀约时间
            timer_hour_text = self.timer_hour_input.text()
            timer_minute_text = self.timer_minute_input.text()

            print(f"[定时邀约] 当前时间: {current_hour:02d}:{current_minute:02d}, 设定时间: {timer_hour_text}:{timer_minute_text}")

            if not timer_hour_text.isdigit() or not timer_minute_text.isdigit():
                print(f"[定时邀约] 时间设置无效: 小时='{timer_hour_text}', 分钟='{timer_minute_text}'")
                return

            target_hour = int(timer_hour_text)
            target_minute = int(timer_minute_text)

            # 检查是否到达邀约时间（精确到分钟）
            if current_hour == target_hour and current_minute == target_minute:
                # 防止重复触发（同一分钟内只触发一次）
                if not self.timer_invitation_triggered:
                    print(f"[定时邀约] ✅ 到达设定时间 {target_hour:02d}:{target_minute:02d}，开始自动邀约")
                    self.timer_invitation_triggered = True

                    # 开始自动邀约
                    self._start_auto_invitation()
                else:
                    print(f"[定时邀约] 已在目标时间内触发过，防止重复触发")
            else:
                # 重置触发标志（不在目标时间时重置）
                if self.timer_invitation_triggered:
                    print(f"[定时邀约] 离开目标时间，重置触发标志")
                    self.timer_invitation_triggered = False

        except Exception as e:
            print(f"[定时邀约] 检查定时邀约时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _load_shop_invitation_record(self, shop_name):
        """从数据库加载指定店铺的邀约记录并应用筛选条件"""
        print(f"[邀约记录] 开始加载店铺 '{shop_name}' 的邀约记录...")

        try:
            # 使用 /advanced_search 查询特定店铺
            shop_identifier_field = "店铺名称"
            api_url = f"{get_server_url()}/advanced_search?table=视频号邀约账号表"
            payload = {
                shop_identifier_field: {"operator": "=", "value": shop_name},
                "page_size": 1
            }

            print(f"[邀约记录] 发送请求获取店铺 '{shop_name}' 的邀约记录...")
            response = requests.post(api_url, json=payload, timeout=15)
            response.raise_for_status()
            result = response.json()

            if result.get("status") == "success" and result.get("data"):
                # 获取邀约记录
                shop_data = result["data"][0]
                saved_state_string = shop_data.get("邀约记录")
                print(f"[邀约记录] 从数据库获取到邀约记录: '{saved_state_string[:100]}...'")

                if saved_state_string:
                    # 解析字符串
                    parsed_settings = self._parse_filter_state_string(saved_state_string)
                    if parsed_settings:
                        # 应用筛选条件到UI
                        print("[邀约记录] 应用店铺邀约记录中的筛选条件...")
                        self._apply_filter_state(parsed_settings)
                    else:
                        print("[邀约记录] 邀约记录解析失败，将使用默认筛选条件")
                else:
                    print("[邀约记录] 邀约记录为空，将使用默认筛选条件")
            else:
                print(f"[邀约记录] 未找到店铺 '{shop_name}' 的记录或API查询失败")
        except Exception as e:
            print(f"[邀约记录] 加载店铺邀约记录时出错: {e}")
            # 出错时继续使用当前筛选条件
    # +++ 结束新增 +++

    # +++ 新增: 准备并加载达人列表以启动邀约 +++
    def _prepare_and_load_influencers(self):
        """设置标志位并触发达人列表加载，为后续启动邀约做准备。"""
        print(f"[Prepare Load Start] 函数被调用. 当前 is_auto_inviting: {self.is_auto_inviting}, switch_shop_requested: {self.switch_shop_requested}, auto_adjust_page_for_invitation: {self.auto_adjust_page_for_invitation}") # <--- 添加入口日志和状态

        # 检查是否所有店铺都已邀约完成，如果是则不启动邀约
        if hasattr(self, 'all_shops_completed_notified') and self.all_shops_completed_notified:
            print("[Prepare Load] 检测到所有店铺已邀约完成，跳过准备和加载达人")
            # 确保按钮状态正确
            if self.start_invite_btn.text() != "开始邀约(已完成)":
                print("[Prepare Load] 修正按钮状态为'开始邀约(已完成)'")
                self.start_invite_btn.setEnabled(True)
                self.start_invite_btn.setText("开始邀约(已完成)")
                self.stop_invite_btn.setEnabled(False)
            return

        # 检查是否已经在邀约中，避免重复启动
        if self.is_auto_inviting and not self.switch_shop_requested and not self.auto_adjust_page_for_invitation:
             print("[Prepare Load] 警告：已经在邀约中 (is_auto_inviting=True)，但不是店铺切换或自动调整页码，取消本次加载和启动。") # <--- 修改日志
             # 可能需要恢复按钮状态？
             if not self.start_invite_btn.isEnabled():
                  self.start_invite_btn.setEnabled(True)
                  self.start_invite_btn.setText("开始邀约")
             if self.stop_invite_btn.isEnabled():
                  self.stop_invite_btn.setEnabled(False)
             return

        # 如果是店铺切换或自动调整页码，则继续加载达人
        if self.is_auto_inviting and (self.switch_shop_requested or self.auto_adjust_page_for_invitation):
            print("[Prepare Load] 检测到店铺切换或自动调整页码，继续加载达人...")
            # 更新UI状态
            self.start_invite_btn.setEnabled(False)
            self.start_invite_btn.setText("邀约中...")
            self.stop_invite_btn.setEnabled(True)

        self.waiting_for_influencers_to_start_invite = True
        print(f"[Prepare Load] waiting_for_influencers_to_start_invite 标志已设置为 True.") # <--- 添加标志位设置日志

        # 设置自动邀约标志，确保在达人加载完成后能启动邀约
        self.is_auto_inviting = True
        print(f"[Prepare Load] 设置 is_auto_inviting 标志为 True，确保在达人加载完成后能启动邀约")
        # 更新按钮状态，明确告知用户正在加载数据
        self.start_invite_btn.setText("加载达人...")
        self.start_invite_btn.setEnabled(False) # 禁用开始按钮，直到加载完成
        self.stop_invite_btn.setEnabled(True) # 允许停止
        print("[Prepare Load] 按钮状态已更新为 '加载达人...' (禁用开始, 启用停止)." ) # <--- 添加按钮状态日志

        print("[Prepare Load] 准备调用 on_load_influencers_clicked...") # <--- 添加调用前日志
        self.on_load_influencers_clicked() # 触发加载
        print("[Prepare Load End] on_load_influencers_clicked 调用完成。") # <--- 添加调用后日志
    # +++ 结束新增 +++\

    # +++ 新增: 应用状态字典到UI的方法 +++
    def _apply_filter_state(self, settings):
        """将设置字典应用到UI筛选器控件上。"""
        print(f"[Apply State] 开始应用状态到UI: {settings}")
        if not settings:
            print("[Apply State] 警告: 传入的设置字典为空，无法应用。")
            return

        # 辅助函数：根据文本查找并选中单选按钮
        def set_radio_button_by_text(group_attribute_prefix, text):
            print(f"[DEBUG] 尝试为 {group_attribute_prefix} 设置值: '{text}'")

            # 创建映射关系，处理不同格式的文本
            text_mapping = {
                # 粉丝数映射
                "1万以下": "1w以下",
                "1万-10万": "1w-10w",
                "10万-50万": "10w-50w",
                "50万以上": "50w以上",
                # 销售额映射
                "10万以下": "10w以下",
                "10万-50万": "10w-50w",
                "50万-100万": "50w-100w",
                "100万以上": "100w以上",
                # 场均销售额映射
                "1万以下": "1w以下",
                "1万-10万": "1w-10w",
                "10万以上": "10w以上"
            }

            # 如果文本在映射中，使用映射后的文本
            mapped_text = text_mapping.get(text, text)
            if mapped_text != text:
                print(f"[DEBUG] 文本映射: '{text}' -> '{mapped_text}'")

            # 直接映射到属性名称
            direct_mapping = {
                # 粉丝数
                "不限": f"{group_attribute_prefix}_unlimited",
                "1w以下": f"{group_attribute_prefix}_less_1w",
                "1万以下": f"{group_attribute_prefix}_less_1w",
                "1w-10w": f"{group_attribute_prefix}_1w_10w",
                "1万-10万": f"{group_attribute_prefix}_1w_10w",
                "10w-50w": f"{group_attribute_prefix}_10w_50w",
                "10万-50万": f"{group_attribute_prefix}_10w_50w",
                "50w以上": f"{group_attribute_prefix}_more_50w",
                "50万以上": f"{group_attribute_prefix}_more_50w",

                # 销售额
                "10w以下": f"{group_attribute_prefix}_less_10w",
                "10万以下": f"{group_attribute_prefix}_less_10w",
                "10w-50w": f"{group_attribute_prefix}_10w_50w",
                "10万-50万": f"{group_attribute_prefix}_10w_50w",
                "50w-100w": f"{group_attribute_prefix}_50w_100w",
                "50万-100万": f"{group_attribute_prefix}_50w_100w",
                "100w以上": f"{group_attribute_prefix}_more_100w",
                "100万以上": f"{group_attribute_prefix}_more_100w",

                # 客单价
                "100元下": f"{group_attribute_prefix}_less_100",
                "100-200元": f"{group_attribute_prefix}_100_200",
                "200元以上": f"{group_attribute_prefix}_more_200",

                # 场均销售额
                "1w以下": f"{group_attribute_prefix}_less_1w",
                "1万以下": f"{group_attribute_prefix}_less_1w",
                "1w-10w": f"{group_attribute_prefix}_1w_10w",
                "1万-10万": f"{group_attribute_prefix}_1w_10w",
                "10w以上": f"{group_attribute_prefix}_more_10w",
                "10万以上": f"{group_attribute_prefix}_more_10w",

                # 粉丝年龄
                "18岁以下": f"{group_attribute_prefix}_less_18",
                "18-24岁": f"{group_attribute_prefix}_18_24",
                "25-39岁": f"{group_attribute_prefix}_25_39",
                "40-49岁": f"{group_attribute_prefix}_40_49",
                "50岁以上": f"{group_attribute_prefix}_more_50"
            }

            # 1. 尝试直接映射到属性名称
            attr_name = direct_mapping.get(text) or direct_mapping.get(mapped_text)

            # 如果找到了属性名称，直接设置
            if attr_name and hasattr(self, attr_name):
                radio_button = getattr(self, attr_name)
                if hasattr(radio_button, 'setChecked'):
                    radio_button.setChecked(True)
                    print(f"[DEBUG] 直接映射成功! 设置 {attr_name} 为选中状态")
                    return
                else:
                    print(f"[DEBUG] 警告: {attr_name} 不是一个单选按钮")

            # 2. 如果直接映射失败，尝试遍历所有单选按钮
            print(f"[DEBUG] 直接映射失败，尝试遍历所有单选按钮...")

            found = False
            for suffix in ['unlimited', 'less_1w', '1w_10w', '10w_50w', '50w_100w', 'more_50w', 'more_100w', 'less_100', '100_200', 'more_200', 'more_10w', 'less_18', '18_24', '25_39', '40_49', 'more_50']:
                radio_button = getattr(self, f"{group_attribute_prefix}_{suffix}", None)
                if radio_button:
                    button_text = radio_button.text()
                    print(f"[DEBUG] 检查 {group_attribute_prefix}_{suffix}: 文本='{button_text}'")

                    # 2.1 尝试精确匹配
                    if button_text == mapped_text:
                        radio_button.setChecked(True)
                        print(f"[DEBUG] 精确匹配成功! {group_attribute_prefix}_{suffix}: 设置为 '{mapped_text}'")
                        found = True
                        break

                    # 2.2 尝试包含匹配
                    if mapped_text in button_text or button_text in mapped_text:
                        radio_button.setChecked(True)
                        print(f"[DEBUG] 包含匹配成功! {group_attribute_prefix}_{suffix}: 设置为 '{button_text}'")
                        found = True
                        break

            # 3. 如果所有匹配都失败，设置为"不限"
            if not found:
                unlimited_radio = getattr(self, f"{group_attribute_prefix}_unlimited", None)
                if unlimited_radio:
                    unlimited_radio.setChecked(True)
                    print(f"[DEBUG] 未找到匹配，设置为默认值 '不限'")
                else:
                    print(f"[DEBUG] 错误! 无法找到任何匹配且找不到不限选项。")

        # 1. 类目 (需要特殊处理，因为是复选框)
        category_text = settings.get('类目', '不限')
        print(f"  - 应用类目: '{category_text}'")
        if hasattr(self, 'category_checkboxes'):
            selected_categories = set(cat.strip() for cat in category_text.split(',') if cat.strip() and category_text != '不限')
            for checkbox in self.category_checkboxes:
                checkbox.setChecked(checkbox.text() in selected_categories)
                if checkbox.isChecked():
                    print(f"    - Checkbox: 已勾选 '{checkbox.text()}'")

        # 2. 页数 (应用到UI和内部状态)
        page_num_str = settings.get('页数', '1') # 默认从第1页开始
        try:
            page_num = int(page_num_str)
            if page_num <= 0: page_num = 1 # 页码至少为1

            # 更新UI输入框
            if hasattr(self, 'start_page_input'):
                self.start_page_input.setText(str(page_num))
                print(f"  - UI 开始页: 设置为 {page_num}")
            else:
                 print("  - UI 开始页: 错误! 找不到 start_page_input 控件。")

            # 更新页码输入框
            if hasattr(self, 'page_input'):
                self.page_input.setText(str(page_num))
                print(f"  - UI 当前页: 设置为 {page_num}")
            else:
                print("  - UI 当前页: 错误! 找不到 page_input 控件。")

            # 更新内部状态 (供后续逻辑使用)
            self.current_influencer_page = page_num
            print(f"  - 页数: 内部状态更新为 {self.current_influencer_page}")
        except ValueError:
             page_num = 1 # 解析失败则重置为1
             if hasattr(self, 'start_page_input'):
                 self.start_page_input.setText('1')
             if hasattr(self, 'page_input'):
                 self.page_input.setText('1')
             self.current_influencer_page = 1
             print(f"  - 页数: 解析 '{page_num_str}' 失败，UI和内部状态设为 1")

        # 3. 粉丝
        fans_text = settings.get('粉丝数', '不限')
        set_radio_button_by_text('fans', fans_text)

        # 4. 销售额
        sales_text = settings.get('销售额', '不限')
        set_radio_button_by_text('sales', sales_text)

        # 5. 客单价
        price_text = settings.get('客单价', '不限')
        set_radio_button_by_text('price', price_text)

        # 6. 粉丝年龄
        age_text = settings.get('粉丝年龄', '不限')
        set_radio_button_by_text('age', age_text)

        # 7. 场均销售额
        avg_sales_text = settings.get('场均销售额', '不限')
        set_radio_button_by_text('avg_sales', avg_sales_text)

        # 8. 其他勾选项
        recommendation_text = settings.get('有推荐方式', '否')
        self.has_recommendation.setChecked(recommendation_text == '是')
        print(f"  - Checkbox 有推荐方式: 设置为 {self.has_recommendation.isChecked()} (来自 '{recommendation_text}')")

        certification_text = settings.get('有视频号认证', '否')
        self.has_certification.setChecked(certification_text == '是')
        print(f"  - Checkbox 有视频号认证: 设置为 {self.has_certification.isChecked()} (来自 '{certification_text}')")

        # 9. 达人性别 (下拉框)
        gender_text = settings.get('达人性别', '不限')
        index = self.gender_combo.findText(gender_text)
        if index >= 0:
            self.gender_combo.setCurrentIndex(index)
            print(f"  - ComboBox 达人性别: 设置为 '{gender_text}' (索引 {index})")
        else:
            self.gender_combo.setCurrentIndex(0) # 默认不限
            print(f"  - ComboBox 达人性别: 未找到 '{gender_text}', 默认设为 '{self.gender_combo.itemText(0)}'")

        print("[Apply State] 状态应用完成。")
    # +++ 结束新增 +++

    # --- 修改: 设置单选按钮组状态的辅助函数 (添加详细调试) ---
    def _set_checked_radio_button(self, group_prefix, text_to_check):
        """根据文本查找并选中单选按钮组中的对应项"""
        suffixes = {
            'fans': ['unlimited', 'less_1w', '1w_10w', '10w_50w', 'more_50w'],
            'sales': ['unlimited', 'less_10w', '10w_50w', '50w_100w', 'more_100w'],
            'price': ['unlimited', 'less_100', '100_200', 'more_200'],
            'avg_sales': ['unlimited', 'less_1w', '1w_10w', 'more_10w'],
            'age': ['unlimited', 'less_18', '18_24', '25_39', '40_49', 'more_50'],
        }
        found = False
        if group_prefix in suffixes:
            print(f"  - Processing group '{group_prefix}', looking for saved text: '{text_to_check}'") # 打印要查找的文本
            for suffix in suffixes[group_prefix]:
                radio_button = getattr(self, f"{group_prefix}_{suffix}", None)
                if radio_button:
                    rb_text = radio_button.text()
                    # --- 新增: 打印对比的两个文本 ---
                    print(f"    - Comparing: Button text='{rb_text}' (len={len(rb_text)}) vs Saved text='{text_to_check}' (len={len(text_to_check)})")
                    # --- 结束新增 ---
                    if rb_text == text_to_check: # 直接比较
                        radio_button.setChecked(True)
                        print(f"      -> MATCH FOUND! Setting '{group_prefix}_{suffix}' to checked.")
                        found = True
                        break
                    # else: # 不需要显式打印 no match 了，上面的打印包含了信息
                    #      print(f"      -> No match.")
                else:
                     print(f"    - Radio button '{group_prefix}_{suffix}' not found!")
        if not found:
            # (回退到选中 "不限" 的逻辑保持不变)
            unlimited_radio = getattr(self, f"{group_prefix}_unlimited", None)
            if unlimited_radio:
                unlimited_radio.setChecked(True)
                print(f"  - Radio Group '{group_prefix}': Text '{text_to_check}' not found or no match, defaulted to '不限'")
            else:
                 print(f"  - Radio Group '{group_prefix}': Error! Cannot find text '{text_to_check}' or default 'unlimited' button.")
        # print(f"  - Finished processing group '{group_prefix}'") # 可以暂时注释掉，减少日志量
    # --- 结束修改 ---


# ============================================================================================
# 独立运行模式支持
# ============================================================================================

def create_mock_classes():
    """创建模拟类，用于独立运行模式"""
    
    class MockAccountManager:
        """模拟账户管理器"""
        def __init__(self):
            self.current_account = "测试账户"
            
        def get_current_account_info(self):
            return {"name": "测试账户", "id": "test_account"}
    
    class MockMainWindow:
        """模拟主窗口引用"""
        def __init__(self):
            # 根据是否有真实的微信API模块创建不同的实例
            if HAS_WECHAT_API:
                try:
                    # 尝试创建真实的微信API实例
                    from 微信api import WechatAPI
                    self.wechat_api = WechatAPI()
                    print("[独立运行] 使用真实的微信API")
                except Exception as e:
                    print(f"[独立运行] 创建真实微信API失败: {e}，使用模拟API")
                    self.wechat_api = WechatAPI()
            else:
                self.wechat_api = WechatAPI()
                print("[独立运行] 使用模拟微信API")
            
        def show_status_message(self, message):
            print(f"[状态消息] {message}")
            
        def update_status_bar(self, message):
            print(f"[状态栏更新] {message}")
            
        def switch_shop(self, shop_name):
            print(f"[店铺切换] 切换到店铺: {shop_name}")
            return True
    
    return MockAccountManager, MockMainWindow


if __name__ == "__main__":
    print("=" * 60)
    print("达人邀约系统 - 独立运行模式")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"运行目录: {os.getcwd()}")
    print(f"脚本路径: {os.path.abspath(__file__)}")
    print("=" * 60)
    
    # 检查依赖状态
    print("依赖检查:")
    print(f"- 微信API模块: {'✅ 可用' if HAS_WECHAT_API else '❌ 不可用 (使用模拟模式)'}")
    print(f"- 配置工具模块: {'✅ 可用' if HAS_CONFIG_UTILS else '❌ 不可用 (使用默认配置)'}")
    print("=" * 60)
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("达人邀约系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("快手小店管理")
    
    # 设置应用图标和样式
    app.setStyle('Fusion')  # 使用现代化的Fusion样式
    
    try:
        # 创建模拟的依赖对象
        MockAccountManager, MockMainWindow = create_mock_classes()
        account_manager = MockAccountManager()
        main_window_ref = MockMainWindow()
        
        print("[独立运行] 正在创建达人邀约界面...")
        
        # 创建达人邀约界面
        window = InfluencerInvitationUI(account_manager, main_window_ref)
        
        # 设置窗口属性
        window.setWindowTitle("达人邀约系统 - 独立运行模式")
        window.resize(1490, 705)  # 用户指定的默认界面大小
        
        # 尝试设置窗口图标
        try:
            from PyQt5.QtGui import QIcon
            icon_path = get_config_file_path(os.path.join("config", "icons", "主窗口.ico"))
            if os.path.exists(icon_path):
                window.setWindowIcon(QIcon(icon_path))
                print(f"[独立运行] 成功设置窗口图标: {icon_path}")
        except Exception as e:
            print(f"[独立运行] 设置窗口图标失败: {e}")
        
        # 居中显示窗口
        try:
            from PyQt5.QtWidgets import QDesktopWidget
            screen = QDesktopWidget().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
            print("[独立运行] 窗口已居中显示")
        except Exception as e:
            print(f"[独立运行] 窗口居中失败: {e}")
        
        # 显示窗口
        window.show()
        
        print("[独立运行] ✅ 达人邀约系统启动成功!")
        print("[独立运行] 📢 注意事项:")
        print("[独立运行]   - 这是独立运行模式，部分功能可能受限")
        print("[独立运行]   - 如需完整功能，请在主系统中运行")
        print("[独立运行]   - 配置文件保存在config目录下")
        print("[独立运行]   - 日志信息会在控制台显示")
        print("=" * 60)
        
        # 运行应用
        exit_code = app.exec_()
        print(f"[独立运行] 应用退出，退出代码: {exit_code}")
        sys.exit(exit_code)
        
    except Exception as e:
        error_msg = f"启动失败: {str(e)}"
        print(f"[错误] {error_msg}")
        print("详细错误信息:")
        traceback.print_exc()
        
        # 尝试显示错误对话框
        try:
            error_dialog = QMessageBox()
            error_dialog.setIcon(QMessageBox.Critical)
            error_dialog.setWindowTitle("达人邀约系统 - 启动错误")
            error_dialog.setText(f"达人邀约系统启动失败:\n\n{error_msg}")
            error_dialog.setDetailedText(traceback.format_exc())
            error_dialog.setStandardButtons(QMessageBox.Ok)
            error_dialog.exec_()
        except Exception as dialog_error:
            print(f"[错误] 无法显示错误对话框: {dialog_error}")
        
        sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n[独立运行] 用户手动中断程序")
        sys.exit(0)

