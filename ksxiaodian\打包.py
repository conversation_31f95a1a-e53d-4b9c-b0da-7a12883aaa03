import os
import sys
import shutil
import subprocess
import PyQt5.QtWidgets as QtWidgets
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PIL import Image

class PackageThread(QThread):
    """打包线程类"""
    # 定义信号
    log_signal = pyqtSignal(str)  # 日志信息信号
    finished_signal = pyqtSignal(bool, str)  # 完成信号(成功/失败, 消息)
    progress_signal = pyqtSignal(int, str)  # 进度信号(百分比, 状态文本)

    def __init__(self, package_modes, show_console, compress_level, output_dir="dist", copy_resources=False):
        super().__init__()
        self.package_modes = package_modes
        self.show_console = show_console
        self.compress_level = compress_level
        self.output_dir = output_dir  # 自定义输出目录
        self.copy_resources = copy_resources  # 是否复制资源文件

    def run(self):
        """线程执行的主要方法"""
        try:
            self.do_packaging()
            self.finished_signal.emit(True, f"打包完成！请在{self.output_dir}目录中查看打包结果")
        except Exception as e:
            self.finished_signal.emit(False, f"打包过程出错: {str(e)}")

    def log_message(self, message):
        """发送日志信息"""
        self.log_signal.emit(message)

    def prepare_icon_file(self):
        """准备图标文件，智能查找图标路径"""
        # 可能的图标路径（使用相对路径）
        possible_icon_paths = [
            "ksxiaodian\\config\\imges\\logo.ico",  # 主要目标
            "ksxiaodian/config/imges/logo.ico",     # 兼容正斜杠
            "config\\imges\\logo.ico",
            "config/imges/logo.ico",
            "logo.ico"
        ]

        for logo_ico_path in possible_icon_paths:
            self.log_message(f"🔍 检查图标文件: {logo_ico_path}")

            # 检查logo.ico是否存在
            if os.path.exists(logo_ico_path):
                file_size = os.path.getsize(logo_ico_path)
                self.log_message(f"✅ 图标文件存在，大小: {file_size} 字节")

                # 验证图标文件大小
                if file_size < 1024:  # 小于1KB
                    self.log_message("⚠️ 警告: 图标文件过小，可能导致显示问题")
                    continue  # 尝试下一个路径

                try:
                    icon = Image.open(logo_ico_path)
                    self.log_message(f"✅ 图标格式: {icon.format}")
                    self.log_message(f"✅ 图标尺寸: {icon.size}")
                    icon.close()
                    # 使用绝对路径解决PyInstaller图标路径问题
                    abs_icon_path = os.path.abspath(logo_ico_path)
                    self.log_message(f"✅ 使用图标文件: {abs_icon_path}")
                    return abs_icon_path  # 返回绝对路径
                except Exception as e:
                    self.log_message(f"❌ 图标文件格式验证失败: {str(e)}")
                    continue  # 尝试下一个路径
            else:
                self.log_message(f"❌ 图标文件不存在: {logo_ico_path}")

        self.log_message("⚠️ 未找到可用的图标文件，将使用默认图标")
        return None

    def cleanup_old_files(self):
        """清理旧的构建文件和临时文件"""
        try:
            # 清理build目录
            if os.path.exists("build"):
                self.log_message("🧹 清理build目录...")
                shutil.rmtree("build")
                self.log_message("✅ build目录清理完成")

            # 清理旧的.spec文件（在ksxiaodian目录）
            spec_patterns = ["ksxiaodian/*.spec", "*.spec"]
            for pattern in spec_patterns:
                import glob
                spec_files = glob.glob(pattern)
                for spec_file in spec_files:
                    try:
                        os.remove(spec_file)
                        self.log_message(f"🧹 清理旧的spec文件: {spec_file}")
                    except Exception as e:
                        self.log_message(f"⚠️ 清理spec文件失败 {spec_file}: {str(e)}")

            # 清理__pycache__目录
            self.cleanup_pycache_dirs()

        except Exception as e:
            self.log_message(f"❌ 清理旧文件时出错: {str(e)}")

    def cleanup_pycache_dirs(self):
        """递归清理__pycache__目录"""
        try:
            import glob
            pycache_dirs = glob.glob("**/__pycache__", recursive=True)
            for pycache_dir in pycache_dirs:
                try:
                    shutil.rmtree(pycache_dir)
                    self.log_message(f"🧹 清理缓存目录: {pycache_dir}")
                except Exception as e:
                    self.log_message(f"⚠️ 清理缓存目录失败 {pycache_dir}: {str(e)}")
        except Exception as e:
            self.log_message(f"⚠️ 清理缓存目录时出错: {str(e)}")

    def do_packaging(self):
        """执行实际的打包过程"""
        # 初始化进度
        self.progress_signal.emit(0, "开始打包...")

        # 检查主文件是否存在 - 智能查找
        main_file = "快手小店管理系统.py"
        found_main = False

        # 可能的主文件路径（使用相对路径）
        possible_main_files = [
            "ksxiaodian\\快手小店管理系统.py",  # 主要目标
            "ksxiaodian/快手小店管理系统.py",   # 兼容正斜杠
            "ksxiaodian\\达人邀约.py",
            "快手小店管理系统.py",
            "达人邀约.py",
            "main.py",
            "app.py"
        ]

        self.log_message(f"🔍 开始查找主文件...")
        self.log_message(f"📂 当前工作目录: {os.getcwd()}")

        # 直接使用相对路径查找
        for file_path in possible_main_files:
            self.log_message(f"🔍 检查: {file_path}")

            if os.path.exists(file_path):
                main_file = file_path
                found_main = True
                self.log_message(f"✅ 找到主文件: {main_file}")
                break

        if not found_main:
            self.log_message(f"❌ 未找到主文件！")
            self.log_message(f"📂 当前目录: {os.getcwd()}")
            self.log_message(f"📋 当前目录内容: {os.listdir('.')}")
            if os.path.exists("ksxiaodian"):
                self.log_message(f"📋 ksxiaodian目录内容: {os.listdir('ksxiaodian')}")
            raise Exception(f"❌ 未找到主文件！尝试的路径: {', '.join(possible_main_files)}")

        # 检查PyInstaller是否已安装
        self.progress_signal.emit(5, "检查依赖...")
        try:
            # 在Windows下隐藏控制台窗口
            creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            subprocess.check_call([sys.executable, "-m", "pip", "show", "pyinstaller"],
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL,
                                creationflags=creation_flags)
            self.log_message("已检测到PyInstaller")
        except subprocess.CalledProcessError:
            self.log_message("正在安装PyInstaller...")
            creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            result = subprocess.call([sys.executable, "-m", "pip", "install", "pyinstaller"],
                                   creationflags=creation_flags)
            if result == 0:
                self.log_message("PyInstaller安装完成")
            else:
                raise Exception("PyInstaller安装失败，请手动安装")

        # 安装Pillow库用于转换图标
        try:
            creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            subprocess.check_call([sys.executable, "-m", "pip", "show", "pillow"],
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL,
                                creationflags=creation_flags)
            self.log_message("已检测到Pillow")
        except subprocess.CalledProcessError:
            self.log_message("正在安装Pillow库（用于图标转换）...")
            creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            result = subprocess.call([sys.executable, "-m", "pip", "install", "pillow"],
                                   creationflags=creation_flags)
            if result == 0:
                self.log_message("Pillow安装完成")
            else:
                self.log_message("警告: Pillow安装失败，将不使用图标")

        # 清理旧的构建文件和临时文件
        self.progress_signal.emit(10, "清理旧文件...")
        self.cleanup_old_files()

        # 根据是否复制资源文件决定清理策略
        if self.copy_resources:
            # 如果要复制资源文件，不清理输出目录，直接覆盖
            if os.path.exists(self.output_dir):
                self.log_message(f"输出目录已存在，将覆盖原文件: {self.output_dir}")
        else:
            # 如果不复制资源文件，清理输出目录
            if os.path.exists(self.output_dir):
                self.log_message(f"清理{self.output_dir}目录...")
                shutil.rmtree(self.output_dir)

        # 打包参数
        app_name = "快手小店管理系统"
        # main_file 已在前面检查和设置

        # 准备图标文件 - 使用logo.ico
        icon_file = self.prepare_icon_file()

        # 智能收集存在的数据文件，保持目录结构
        self.log_message("🔍 开始智能收集数据文件...")

        # 使用glob模式来查找文件，更灵活
        import glob

        data_files = []

        # 1. 收集图片资源文件
        image_patterns = [
            "ksxiaodian/config/imges/*.png",
            "ksxiaodian/config/imges/*.ico",
            "ksxiaodian/config/imges/*.jpg",
            "ksxiaodian/config/imges/*.jpeg",
            "config/imges/*.png",
            "config/imges/*.ico",
            "config/imges/*.jpg",
            "config/imges/*.jpeg"
        ]

        for pattern in image_patterns:
            files = glob.glob(pattern)
            for file in files:
                dst = os.path.dirname(file)
                data_files.append((file, dst))
                self.log_message(f"🖼️ 找到图片文件: {file}")

        # 2. 收集Python脚本文件
        python_patterns = [
            "ksxiaodian/*.py",
            "*.py"
        ]

        for pattern in python_patterns:
            python_files = glob.glob(pattern)
            for file in python_files:
                # 跳过打包脚本本身
                if "打包" in file or "package" in file.lower():
                    continue
                # 确定目标目录
                if file.startswith("ksxiaodian/") or file.startswith("ksxiaodian\\"):
                    dst = "."  # ksxiaodian目录下的文件放到根目录
                else:
                    dst = "."
                data_files.append((file, dst))
                self.log_message(f"🐍 找到Python文件: {file}")

        # 3. 收集tool目录下的Python脚本
        tool_patterns = [
            "ksxiaodian/tool/*.py",
            "tool/*.py"
        ]

        for pattern in tool_patterns:
            tool_files = glob.glob(pattern)
            for file in tool_files:
                # 确定目标目录
                if file.startswith("ksxiaodian/") or file.startswith("ksxiaodian\\"):
                    dst = "tool"  # 保持tool目录结构
                else:
                    dst = "tool"
                data_files.append((file, dst))
                self.log_message(f"🔧 找到工具文件: {file}")

        # 4. 收集配置文件
        config_patterns = [
            "ksxiaodian/config/*.json",
            "ksxiaodian/config/*.txt",
            "ksxiaodian/config/*.ini",
            "config/*.json",
            "config/*.txt",
            "config/*.ini"
        ]

        for pattern in config_patterns:
            files = glob.glob(pattern)
            for file in files:
                # 确定目标目录
                if file.startswith("ksxiaodian/") or file.startswith("ksxiaodian\\"):
                    # 移除ksxiaodian前缀
                    relative_path = file[11:] if file.startswith("ksxiaodian/") else file[12:]
                    dst = os.path.dirname(relative_path)
                else:
                    dst = os.path.dirname(file)
                data_files.append((file, dst))
                self.log_message(f"⚙️ 找到配置文件: {file}")

        self.log_message(f"📦 总共找到 {len(data_files)} 个数据文件")

        # 打包每种模式
        total_modes = len(self.package_modes)
        for mode_index, mode in enumerate(self.package_modes):
            # 计算当前进度
            base_progress = 15 + (mode_index * 70 // total_modes)

            # 设置输出名称和日志信息 - 统一使用相同的名称
            output_name = app_name  # 所有模式都使用相同的名称
            if mode == "folder":
                self.log_message(f"\n开始打包文件夹版本...")
                self.progress_signal.emit(base_progress, f"打包文件夹版本 ({mode_index+1}/{total_modes})")
            elif mode == "onefile_embedded":
                self.log_message(f"\n开始打包完全独立单文件版本...")
                self.progress_signal.emit(base_progress, f"打包完全独立版本 ({mode_index+1}/{total_modes})")
            elif mode == "onefile_external":
                self.log_message(f"\n开始打包单文件版本(外部资源)...")
                self.progress_signal.emit(base_progress, f"打包单文件版本 ({mode_index+1}/{total_modes})")

            # 构建命令
            cmd = [
                "pyinstaller",
                f"--name={output_name}",
                f"--distpath={self.output_dir}",  # 设置自定义输出目录
                f"--specpath=ksxiaodian",         # spec文件生成在脚本所在目录
                f"--workpath=ksxiaodian/build",   # 工作目录也在脚本所在目录
            ]

            # 控制台选项
            if not self.show_console:
                cmd.append("--noconsole")

            # 打包模式
            if mode in ["onefile_embedded", "onefile_external"]:
                cmd.append("--onefile")

            # 图标设置 - 智能处理图标路径
            if icon_file and os.path.exists(icon_file):
                # 直接使用找到的图标文件路径
                cmd.append(f"--icon={icon_file}")
                self.log_message(f"🎨 使用图标文件: {icon_file}")

                # 验证图标文件
                try:
                    file_size = os.path.getsize(icon_file)
                    self.log_message(f"   📊 文件大小: {file_size} 字节")

                    # 验证图标文件格式
                    with open(icon_file, 'rb') as f:
                        header = f.read(4)
                        if header == b'\x00\x00\x01\x00':
                            self.log_message(f"   ✅ 有效的ICO文件格式")
                        else:
                            self.log_message(f"   ⚠️ 警告: 可能不是标准ICO格式，文件头: {header.hex()}")

                    if file_size < 1000:
                        self.log_message(f"   ⚠️ 警告: 图标文件较小，可能影响显示效果")
                    else:
                        self.log_message(f"   ✅ 图标文件大小合适")

                except Exception as e:
                    self.log_message(f"   ❌ 警告: 无法读取图标文件信息: {e}")
            else:
                self.log_message("⚠️ 未找到可用的图标文件，exe将使用默认图标")
                self.log_message(f"   当前工作目录: {os.getcwd()}")
                self.log_message(f"   目录内容: {os.listdir('.')[:10]}...")  # 只显示前10个文件

            # 数据文件处理 - 所有模式都添加数据文件，确保图标和资源可用
            # data_files 已经过滤了存在的文件，直接添加即可
            for src, dst in data_files:
                # 在Windows上使用分号，在其他系统上使用冒号
                separator = ";" if sys.platform == "win32" else ":"
                cmd.append(f"--add-data={src}{separator}{dst}")
                if src.endswith('.py'):
                    self.log_message(f"📄 添加Python脚本: {src} -> {dst}")
                else:
                    self.log_message(f"📁 添加数据文件: {src} -> {dst}")

            # 对于完全独立模式，添加特殊说明
            if mode == "onefile_embedded":
                self.log_message("注意: 完全独立模式将所有资源嵌入exe，程序会自动从临时目录读取资源")

            # 隐藏导入 - 添加SSL和网络相关模块以及Selenium相关模块
            cmd.extend([
                "--hidden-import=PyQt5.QtCore",
                "--hidden-import=PyQt5.QtWidgets",
                "--hidden-import=PyQt5.QtGui",
                "--hidden-import=PyQt5.QtNetwork",
                "--hidden-import=requests",
                "--hidden-import=json",
                "--hidden-import=urllib3",
                "--hidden-import=urllib3.exceptions",
                "--hidden-import=urllib3.util",
                "--hidden-import=urllib3.util.ssl_",
                "--hidden-import=certifi",
                "--hidden-import=charset_normalizer",
                "--hidden-import=idna",
                "--hidden-import=cryptography",
                "--hidden-import=cryptography.hazmat",
                "--hidden-import=cryptography.hazmat.primitives",
                "--hidden-import=cryptography.hazmat.backends",
                "--hidden-import=cryptography.hazmat.backends.openssl",
                "--hidden-import=OpenSSL",
                "--hidden-import=OpenSSL.SSL",
                "--hidden-import=chardet",
                "--hidden-import=brotli",
                "--hidden-import=ssl",
                "--hidden-import=_ssl",
                "--hidden-import=warnings",
                "--hidden-import=tool.阿里巴巴接口",
                "--hidden-import=tool.待下单底部",
                "--hidden-import=tool.详情统计",
                "--hidden-import=tool.快手api",  # 添加快手api模块
                "--hidden-import=tool.快手cokie_api",  # 添加快手cokie_api模块
                "--hidden-import=tool.分类树",
                "--hidden-import=tool.内嵌浏览器",
                "--hidden-import=tool.店铺编辑器",
                "--hidden-import=tool.状态管理器",
                "--hidden-import=tool.date",
                "--hidden-import=tool.chromedriver_guardian",
                "--hidden-import=tool.simple_chromedriver_killer",
                # 添加Selenium相关模块，防止浏览器启动时出现控制台窗口
                "--hidden-import=selenium",
                "--hidden-import=selenium.webdriver",
                "--hidden-import=selenium.webdriver.chrome",
                "--hidden-import=selenium.webdriver.chrome.service",
                "--hidden-import=selenium.webdriver.chrome.options",
                "--hidden-import=selenium.webdriver.common.by",
                "--hidden-import=selenium.webdriver.support",
                "--hidden-import=selenium.webdriver.support.ui",
                "--hidden-import=selenium.webdriver.support.expected_conditions",
                "--hidden-import=selenium.common.exceptions",
                "--hidden-import=subprocess",
                "--hidden-import=platform",
                # 添加异步相关模块，解决 base_events 错误
                "--hidden-import=asyncio",
                "--hidden-import=asyncio.base_events",
                "--hidden-import=asyncio.events",
                "--hidden-import=asyncio.futures",
                "--hidden-import=asyncio.tasks",
                "--hidden-import=asyncio.coroutines",
                "--hidden-import=asyncio.protocols",
                "--hidden-import=asyncio.transports",
                "--hidden-import=asyncio.streams",
                "--hidden-import=asyncio.subprocess",
                "--hidden-import=asyncio.locks",
                "--hidden-import=asyncio.queues",
                "--hidden-import=asyncio.selector_events",
                "--hidden-import=asyncio.proactor_events",
                "--hidden-import=asyncio.windows_events",
                "--hidden-import=asyncio.unix_events",
                "--hidden-import=asyncio.base_futures",
                "--hidden-import=asyncio.base_tasks",
                "--hidden-import=asyncio.base_subprocess",
                "--hidden-import=asyncio.constants",
                "--hidden-import=asyncio.log",
                "--hidden-import=asyncio.runners",
                "--hidden-import=asyncio.timeouts",
                "--hidden-import=asyncio.trsock",
                "--hidden-import=asyncio.staggered",
                "--hidden-import=asyncio.threads",
                "--hidden-import=asyncio.windows_utils",
                # 添加并发相关模块
                "--hidden-import=concurrent",
                "--hidden-import=concurrent.futures",
                "--hidden-import=concurrent.futures._base",
                "--hidden-import=concurrent.futures.thread",
                "--hidden-import=concurrent.futures.process",
                # 添加线程相关模块
                "--hidden-import=threading",
                "--hidden-import=_thread",
                "--hidden-import=queue",
                # 添加事件循环相关模块
                "--hidden-import=selectors",
                "--hidden-import=select",
                # 添加网络相关的异步模块
                "--hidden-import=socket",
                "--hidden-import=socketserver",
                # 添加信号处理相关模块
                "--hidden-import=signal",
                "--hidden-import=weakref",
            ])

            # 压缩选项
            if self.compress_level == 0:
                cmd.append("--noupx")
            elif self.compress_level == 2:
                cmd.append("--upx-dir=upx")

            # 其他选项
            cmd.append("--clean")  # 打包前清理PyInstaller缓存

            # 添加主文件
            cmd.append(main_file)

            # 执行命令
            self.log_message(f"🚀 执行打包命令:")
            self.log_message(f"  {' '.join(cmd)}")
            self.log_message(f"📂 当前工作目录: {os.getcwd()}")
            self.log_message(f"🐍 Python版本: {sys.version}")
            self.log_message(f"📋 主文件存在: {os.path.exists(main_file)}")

            # 特别显示图标相关信息
            icon_params = [param for param in cmd if param.startswith('--icon=')]
            if icon_params:
                self.log_message(f"🎨 图标设置: {icon_params[0]}")
            else:
                self.log_message(f"⚠️ 未设置图标参数")

            # 更新进度 - 开始执行打包
            exec_progress = base_progress + 20
            self.progress_signal.emit(exec_progress, f"正在打包 ({mode_index+1}/{total_modes})")

            # 在Windows下隐藏控制台窗口
            creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0

            try:
                # 使用更简单的subprocess.call避免编码问题
                self.log_message("� 开始执行打包命令...")

                # 设置环境变量来处理编码问题
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8:ignore'

                # 正常执行完整的打包命令
                result_code = subprocess.call(cmd, creationflags=creation_flags, env=env)

                self.log_message(f"📊 打包命令返回代码: {result_code}")

            except Exception as e:
                self.log_message(f"❌ 执行打包命令时出错: {str(e)}")
                result_code = 1

            if result_code == 0:
                if mode == "folder":
                    exe_path = f"{self.output_dir}/{output_name}/{output_name}.exe"
                    self.log_message(f"✅ 文件夹版本打包完成!")
                    self.log_message(f"📁 可执行文件位于: {exe_path}")
                else:
                    exe_path = f"{self.output_dir}/{output_name}.exe"
                    self.log_message(f"✅ 单文件版本打包完成!")
                    self.log_message(f"📁 可执行文件位于: {exe_path}")

                    # 对于外部资源模式，根据用户选择决定是否复制资源文件
                    if mode == "onefile_external" and self.copy_resources:
                        self.copy_resources_for_external_mode(output_name)
                        self.log_message("✅ 资源文件已自动复制到输出目录")
                    elif mode == "onefile_external" and not self.copy_resources:
                        self.log_message("ℹ️ 未复制资源文件，请手动复制config目录到exe同目录")

                # 验证生成的exe文件
                self.verify_generated_exe(exe_path, icon_file)

                # 显示打包成功的详细信息
                self.log_package_success_info(mode, exe_path)
            else:
                self.log_message(f"❌ 打包失败，错误代码: {result_code}")
                self.log_message("💡 常见解决方案:")
                self.log_message("   1. 检查Python环境和依赖包是否完整")
                self.log_message("   2. 确保没有其他程序占用相关文件")
                self.log_message("   3. 尝试以管理员权限运行打包工具")
                self.log_message("   4. 检查磁盘空间是否充足")
                self.log_message(f"   5. 检查主文件是否存在: {main_file}")
                self.log_message(f"   6. 检查当前工作目录: {os.getcwd()}")

                # 如果打包失败，尝试简化命令
                if not hasattr(self, '_first_attempt_failed'):
                    self.log_message("🔄 第一次打包失败，将尝试简化的打包命令...")
                    self._first_attempt_failed = True

                    # 构建简化命令并立即执行
                    simple_cmd = [
                        "pyinstaller",
                        f"--name=快手小店管理系统",
                        f"--distpath={self.output_dir}",
                        f"--specpath=ksxiaodian",
                        "--onefile",
                        "--clean",
                        main_file
                    ]

                    # 根据用户设置添加控制台选项
                    if not self.show_console:
                        simple_cmd.insert(-1, "--noconsole")

                    # 如果有图标文件，也添加到简化命令中
                    if icon_file and os.path.exists(icon_file):
                        simple_cmd.insert(-1, f"--icon={icon_file}")

                    self.log_message(f"🔄 执行简化命令: {' '.join(simple_cmd)}")

                    try:
                        # 确保环境变量正确设置
                        simple_env = os.environ.copy()
                        simple_env['PYTHONIOENCODING'] = 'utf-8:ignore'
                        simple_result = subprocess.call(simple_cmd, creationflags=creation_flags, env=simple_env)
                        self.log_message(f"📊 简化命令返回代码: {simple_result}")

                        if simple_result == 0:
                            exe_path = f"{self.output_dir}/快手小店管理系统.exe"
                            self.log_message(f"✅ 简化打包成功!")
                            self.log_message(f"📁 可执行文件位于: {exe_path}")

                            # 验证生成的exe文件
                            if os.path.exists(exe_path):
                                self.verify_generated_exe(exe_path, icon_file)
                                self.log_package_success_info(mode, exe_path)
                                # 简化打包成功，跳出当前模式的处理
                                break
                            else:
                                self.log_message(f"⚠️ 简化打包成功但未找到exe文件: {exe_path}")
                                raise Exception(f"简化打包成功但未找到exe文件")
                        else:
                            raise Exception(f"简化打包也失败，错误代码: {simple_result}")
                    except Exception as e:
                        raise Exception(f"简化打包执行失败: {str(e)}")
                else:
                    # 如果已经尝试过简化命令，直接抛出异常
                    raise Exception(f"打包模式 {mode} 失败，错误代码: {result_code}")

        # 创建一个简单的说明文件
        self.progress_signal.emit(95, "生成说明文件...")
        self.create_readme_file(app_name)

        # 清理.spec文件
        self.progress_signal.emit(98, "清理临时文件...")
        self.cleanup_spec_files(app_name)

        # 打包完成
        self.progress_signal.emit(100, "打包完成！")

    def verify_generated_exe(self, exe_path, icon_file):
        """
        验证生成的exe文件

        参数:
            exe_path (str): exe文件路径
            icon_file (str): 原始图标文件路径
        """
        try:
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path)
                self.log_message(f"🔍 exe文件验证:")
                self.log_message(f"  文件大小: {file_size:,} 字节")

                if icon_file and os.path.exists(icon_file):
                    self.log_message(f"  🎨 使用的图标: {os.path.basename(icon_file)}")
                    self.log_message(f"  💡 提示: 如果exe图标显示不正确，请检查:")
                    self.log_message(f"     1. 图标文件格式是否为标准ICO格式")
                    self.log_message(f"     2. 图标文件是否包含多种尺寸")
                    self.log_message(f"     3. 系统图标缓存是否需要刷新")
                else:
                    self.log_message(f"  ⚠️ 未使用自定义图标，exe将显示默认图标")
            else:
                self.log_message(f"❌ exe文件不存在: {exe_path}")
        except Exception as e:
            self.log_message(f"⚠️ 验证exe文件时出错: {str(e)}")

    def log_package_success_info(self, mode, exe_path):
        """记录打包成功的详细信息"""
        try:
            self.log_message("=" * 50)
            self.log_message("🎉 打包成功详细信息:")

            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path)
                size_mb = file_size / (1024 * 1024)
                self.log_message(f"📦 文件大小: {size_mb:.2f} MB ({file_size:,} 字节)")

            mode_descriptions = {
                "folder": "文件夹模式 - 启动快，需要保持文件夹完整",
                "onefile_embedded": "完全独立模式 - 真正单文件，启动较慢",
                "onefile_external": "外部资源模式 - 单文件+外部资源"
            }

            self.log_message(f"🔧 打包模式: {mode_descriptions.get(mode, mode)}")
            self.log_message(f"📍 输出位置: {os.path.abspath(exe_path)}")

            # 提供使用建议
            if mode == "folder":
                self.log_message("💡 使用建议: 整个文件夹可以移动到其他位置使用")
            elif mode == "onefile_embedded":
                self.log_message("💡 使用建议: 可以单独运行，首次启动会解压临时文件")
            elif mode == "onefile_external":
                self.log_message("💡 使用建议: 需要保持exe和config、tool文件夹在同一目录")

            self.log_message("=" * 50)

        except Exception as e:
            self.log_message(f"⚠️ 记录成功信息时出错: {str(e)}")

    def copy_resources_for_external_mode(self, output_name):
        """为外部资源模式复制资源文件和Python脚本到exe同目录"""
        # 使用自定义输出目录，output_name参数保留用于兼容性
        try:
            exe_dir = os.path.join(self.output_dir)
            config_dir = os.path.join(exe_dir, "config")
            images_dir = os.path.join(config_dir, "imges")
            tool_dir = os.path.join(exe_dir, "tool")

            # 创建目录结构
            os.makedirs(images_dir, exist_ok=True)
            os.makedirs(tool_dir, exist_ok=True)

            # 复制图片资源文件
            resource_files = [
                "config/imges/plus_icon.png",
                "config/imges/minus_icon.png",
                "config/imges/logo.ico",
                "config/imges/ks.png",
                "config/imges/image.png"
            ]

            for src_file in resource_files:
                if os.path.exists(src_file):
                    dst_file = os.path.join(exe_dir, src_file)
                    os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                    shutil.copy2(src_file, dst_file)
                    self.log_message(f"已复制资源文件: {src_file}")

            # 复制Python脚本文件 - 动态导入需要
            python_files = [
                "一键下单.py",
                "商品复制.py",
                "商品管理.py",
                "店铺后台登录.py",
                "数据统计.py",
                "登录窗口.py",
                "订单管理.py"
            ]

            for src_file in python_files:
                if os.path.exists(src_file):
                    dst_file = os.path.join(exe_dir, src_file)
                    shutil.copy2(src_file, dst_file)
                    self.log_message(f"已复制Python脚本: {src_file}")

            # 复制tool目录下的Python脚本
            tool_files = [
                "tool/date.py",
                "tool/内嵌浏览器.py",
                "tool/分类树.py",
                "tool/店铺编辑器.py",
                "tool/待下单底部.py",
                "tool/状态管理器.py",
                "tool/详情统计.py",
                "tool/阿里巴巴接口.py"
            ]

            for src_file in tool_files:
                if os.path.exists(src_file):
                    dst_file = os.path.join(exe_dir, src_file)
                    os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                    shutil.copy2(src_file, dst_file)
                    self.log_message(f"已复制tool脚本: {src_file}")

            self.log_message(f"外部资源模式：所有资源文件和Python脚本已复制到 {exe_dir}")

        except Exception as e:
            self.log_message(f"复制资源文件时出错: {str(e)}")

    def create_readme_file(self, app_name):
        """创建使用说明文件"""
        readme_path = os.path.join(self.output_dir, "使用说明.txt")
        os.makedirs(os.path.dirname(readme_path), exist_ok=True)

        with open(readme_path, "w", encoding="utf-8") as f:
            f.write("快手小店管理系统\n")
            f.write("=================\n\n")

            if "folder" in self.package_modes:
                f.write(f"1. 文件夹版本: 双击 {self.output_dir}/{app_name}/{app_name}.exe 运行程序\n")
                f.write("   优点: 启动速度快，占用内存少\n")
                f.write("   缺点: 需要保持整个文件夹完整\n\n")

            if "onefile_embedded" in self.package_modes:
                f.write(f"2. 完全独立版本: 双击 {self.output_dir}/{app_name}.exe 运行程序\n")
                f.write("   优点: 真正的单文件，不依赖任何外部文件\n")
                f.write("   缺点: 启动较慢，体积较大\n\n")

            if "onefile_external" in self.package_modes:
                f.write(f"3. 单文件版本(外部资源): 双击 {self.output_dir}/{app_name}.exe 运行程序\n")
                f.write("   优点: 单个exe文件 + 同目录资源文件和Python脚本\n")
                if self.copy_resources:
                    f.write("   说明: 资源文件和Python脚本已自动复制到exe同目录\n")
                    f.write("   包含: config文件夹、tool文件夹、主要.py文件\n")
                else:
                    f.write("   说明: 需要手动复制config文件夹和Python脚本到exe同目录\n")
                    f.write("   需要: config文件夹、tool文件夹、主要.py文件\n")
                f.write("   缺点: 需要保持exe和相关文件在同一目录\n\n")

            f.write("注意事项:\n")
            f.write("- 程序首次启动可能较慢\n")
            f.write("- 完全独立版本所有资源都嵌入在exe中\n")
            f.write("- 外部资源版本需要保持exe和以下文件/文件夹在同一目录:\n")
            f.write("  * config文件夹（图片资源）\n")
            f.write("  * tool文件夹（工具脚本）\n")
            f.write("  * 主要Python脚本文件（动态导入需要）\n")
            f.write("- 动态导入的Python脚本已包含在打包中，确保功能正常\n")
            f.write("- 使用过程中遇到问题，请联系开发人员\n")

        self.log_message(f"已创建使用说明文件: {readme_path}")

    def cleanup_spec_files(self, app_name):
        """清理打包过程中生成的.spec文件"""
        try:
            # 查找所有可能的.spec文件（在ksxiaodian目录和根目录）
            spec_files = [
                f"ksxiaodian/{app_name}.spec",
                f"ksxiaodian/快手小店管理系统.spec",
                f"{app_name}.spec",
                "快手小店管理系统.spec"
            ]

            deleted_count = 0
            for spec_file in spec_files:
                if os.path.exists(spec_file):
                    try:
                        os.remove(spec_file)
                        self.log_message(f"✅ 已删除临时文件: {spec_file}")
                        deleted_count += 1
                    except Exception as e:
                        self.log_message(f"⚠️ 删除文件失败 {spec_file}: {str(e)}")

            if deleted_count == 0:
                self.log_message("ℹ️ 未找到需要清理的.spec文件")
            else:
                self.log_message(f"🧹 清理完成，共删除 {deleted_count} 个.spec文件")

        except Exception as e:
            self.log_message(f"❌ 清理.spec文件时出错: {str(e)}")

class PackageUI(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.package_thread = None  # 初始化线程变量
        self.output_dir = "dist"  # 默认输出目录
        self.initUI()

    def initUI(self):
        self.setWindowTitle('快手小店管理系统打包工具')
        self.setGeometry(300, 300, 500, 400)

        # 设置打包工具的图标
        try:
            icon_path = os.path.join("config", "imges", "logo.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QtWidgets.QApplication.style().standardIcon(QtWidgets.QStyle.SP_ComputerIcon))
                # 如果图标文件存在，使用自定义图标
                from PyQt5.QtGui import QIcon
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"设置打包工具图标失败: {e}")
            # 使用系统默认图标作为备用
            self.setWindowIcon(QtWidgets.QApplication.style().standardIcon(QtWidgets.QStyle.SP_ComputerIcon))

        # 创建布局
        layout = QtWidgets.QVBoxLayout()

        # 头部标题
        title_label = QtWidgets.QLabel('快手小店管理系统打包工具 v2.0')
        title_label.setAlignment(Qt.AlignCenter)
        font = title_label.font()
        font.setPointSize(16)
        font.setBold(True)
        title_label.setFont(font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                margin: 10px;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #3498db;
            }
        """)
        layout.addWidget(title_label)

        # 说明文字
        desc_label = QtWidgets.QLabel('🚀 优化版打包工具 - 自动清理临时文件，提供详细打包信息')
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                margin: 5px;
                padding: 5px;
            }
        """)
        layout.addWidget(desc_label)

        layout.addSpacing(20)

        # 打包选项组
        option_group = QtWidgets.QGroupBox('打包选项')
        option_layout = QtWidgets.QVBoxLayout()

        # 打包模式选择
        self.mode_group = QtWidgets.QGroupBox('打包模式')
        mode_layout = QtWidgets.QVBoxLayout()

        self.folder_mode = QtWidgets.QRadioButton('文件夹模式 (启动快，体积大)')
        self.onefile_embedded = QtWidgets.QRadioButton('单文件模式-完全独立 (所有资源嵌入exe)')
        self.onefile_external = QtWidgets.QRadioButton('单文件模式-外部资源 (exe+同目录资源文件)')
        self.all_modes = QtWidgets.QRadioButton('生成所有模式')

        self.onefile_external.setChecked(True)  # 默认选中单文件模式-外部资源

        mode_layout.addWidget(self.folder_mode)
        mode_layout.addWidget(self.onefile_embedded)
        mode_layout.addWidget(self.onefile_external)

        # 添加资源文件复制选项（仅对外部资源模式有效）
        self.copy_resources_checkbox = QtWidgets.QCheckBox('自动复制资源文件到输出目录')
        self.copy_resources_checkbox.setChecked(False)  # 默认不勾选
        self.copy_resources_checkbox.setStyleSheet("QCheckBox { margin-left: 20px; }")  # 缩进显示
        mode_layout.addWidget(self.copy_resources_checkbox)

        mode_layout.addWidget(self.all_modes)

        # 连接单选按钮信号，控制复选框的启用状态
        self.folder_mode.toggled.connect(self.update_copy_resources_state)
        self.onefile_embedded.toggled.connect(self.update_copy_resources_state)
        self.onefile_external.toggled.connect(self.update_copy_resources_state)
        self.all_modes.toggled.connect(self.update_copy_resources_state)

        # 初始化复选框状态
        self.update_copy_resources_state()
        self.mode_group.setLayout(mode_layout)
        option_layout.addWidget(self.mode_group)

        # 控制台选项
        self.console_group = QtWidgets.QGroupBox('控制台选项')
        console_layout = QtWidgets.QVBoxLayout()

        self.hide_console = QtWidgets.QRadioButton('隐藏控制台 (普通用户使用)')
        self.show_console = QtWidgets.QRadioButton('显示控制台 (便于调试)')

        self.hide_console.setChecked(True)

        console_layout.addWidget(self.hide_console)
        console_layout.addWidget(self.show_console)
        self.console_group.setLayout(console_layout)
        option_layout.addWidget(self.console_group)

        # 压缩选项
        self.compress_group = QtWidgets.QGroupBox('压缩选项')
        compress_layout = QtWidgets.QVBoxLayout()

        self.no_compress = QtWidgets.QRadioButton('不压缩 (打包快，启动快)')
        self.normal_compress = QtWidgets.QRadioButton('普通压缩 (平衡)')
        self.max_compress = QtWidgets.QRadioButton('最大压缩 (打包慢，体积小)')

        self.no_compress.setChecked(True)  # 默认选中不压缩

        compress_layout.addWidget(self.no_compress)
        compress_layout.addWidget(self.normal_compress)
        compress_layout.addWidget(self.max_compress)
        self.compress_group.setLayout(compress_layout)
        option_layout.addWidget(self.compress_group)

        # 输出目录选择
        self.output_group = QtWidgets.QGroupBox('输出目录设置')
        output_layout = QtWidgets.QHBoxLayout()

        self.output_label = QtWidgets.QLabel('输出目录:')
        self.output_path_label = QtWidgets.QLabel(self.output_dir)
        self.output_path_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc; }")
        self.output_path_label.setMinimumWidth(200)

        self.select_output_btn = QtWidgets.QPushButton('选择目录')
        self.select_output_btn.clicked.connect(self.select_output_directory)
        self.select_output_btn.setFixedWidth(80)

        self.reset_output_btn = QtWidgets.QPushButton('重置')
        self.reset_output_btn.clicked.connect(self.reset_output_directory)
        self.reset_output_btn.setFixedWidth(60)

        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.output_path_label)
        output_layout.addWidget(self.select_output_btn)
        output_layout.addWidget(self.reset_output_btn)
        output_layout.addStretch()  # 添加弹性空间

        self.output_group.setLayout(output_layout)
        option_layout.addWidget(self.output_group)

        # 应用选项
        option_group.setLayout(option_layout)
        layout.addWidget(option_group)

        # 按钮区域
        button_layout = QtWidgets.QHBoxLayout()

        # 打包按钮
        self.package_btn = QtWidgets.QPushButton('开始打包')
        self.package_btn.setFixedHeight(40)
        self.package_btn.clicked.connect(self.start_packaging)

        # 设置按钮样式，支持进度显示，确保文字清晰可见
        self.default_button_style = """
            QPushButton {
                background-color: #007bff !important;
                color: #ffffff !important;
                border: 3px solid #0056b3 !important;
                border-radius: 8px !important;
                font-size: 16px !important;
                font-weight: bold !important;
                padding: 10px 20px !important;
                text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
            }
            QPushButton:hover {
                background-color: #0056b3 !important;
                border-color: #004085 !important;
                text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
            }
            QPushButton:disabled {
                background-color: #6c757d !important;
                border-color: #5a6268 !important;
                text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
            }
        """
        self.package_btn.setStyleSheet(self.default_button_style)

        button_layout.addWidget(self.package_btn)

        # 打开输出目录按钮
        self.open_dir_btn = QtWidgets.QPushButton('打开输出目录')
        self.open_dir_btn.setFixedHeight(40)
        self.open_dir_btn.setFixedWidth(120)
        self.open_dir_btn.clicked.connect(self.open_output_directory)
        button_layout.addWidget(self.open_dir_btn)

        layout.addLayout(button_layout)

        # 状态显示
        self.status_text = QtWidgets.QTextEdit()
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)

        self.setLayout(layout)

    def log_message(self, message):
        self.status_text.append(message)
        QtWidgets.QApplication.processEvents()

    def update_copy_resources_state(self):
        """更新资源文件复制选项的启用状态"""
        # 只有在选择外部资源模式或所有模式时才启用复选框
        enable_checkbox = self.onefile_external.isChecked() or self.all_modes.isChecked()
        self.copy_resources_checkbox.setEnabled(enable_checkbox)

        # 更新复选框文本，提供更清晰的说明
        if self.onefile_external.isChecked():
            self.copy_resources_checkbox.setText('自动复制资源文件和Python脚本到输出目录 (不勾选需手动复制)')
        elif self.all_modes.isChecked():
            self.copy_resources_checkbox.setText('自动复制资源文件和Python脚本到输出目录 (仅影响外部资源模式)')
        else:
            self.copy_resources_checkbox.setText('自动复制资源文件和Python脚本到输出目录 (仅对外部资源模式有效)')

    def select_output_directory(self):
        """选择输出目录"""
        directory = QtWidgets.QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            self.output_dir,
            QtWidgets.QFileDialog.ShowDirsOnly | QtWidgets.QFileDialog.DontResolveSymlinks
        )

        if directory:
            self.output_dir = directory
            self.output_path_label.setText(self.output_dir)
            self.log_message(f"输出目录已设置为: {self.output_dir}")

    def reset_output_directory(self):
        """重置输出目录为默认值"""
        self.output_dir = "dist"
        self.output_path_label.setText(self.output_dir)
        self.log_message("输出目录已重置为默认值: dist")

    def start_packaging(self):
        self.status_text.clear()
        self.log_message("开始打包快手小店管理系统...")

        # 获取选项
        package_modes = []
        if self.folder_mode.isChecked():
            package_modes.append("folder")
        elif self.onefile_embedded.isChecked():
            package_modes.append("onefile_embedded")
        elif self.onefile_external.isChecked():
            package_modes.append("onefile_external")
        elif self.all_modes.isChecked():
            package_modes.extend(["folder", "onefile_embedded", "onefile_external"])

        show_console = self.show_console.isChecked()

        compress_level = 0
        if self.normal_compress.isChecked():
            compress_level = 1
        elif self.max_compress.isChecked():
            compress_level = 2

        # 获取资源文件复制选项
        copy_resources = self.copy_resources_checkbox.isChecked()

        # 禁用按钮防止重复点击
        self.package_btn.setEnabled(False)
        self.package_btn.setText("准备打包... 0%")

        # 创建并启动打包线程
        self.package_thread = PackageThread(package_modes, show_console, compress_level, self.output_dir, copy_resources)
        self.package_thread.log_signal.connect(self.log_message)
        self.package_thread.finished_signal.connect(self.on_packaging_finished)
        self.package_thread.progress_signal.connect(self.update_progress)
        self.package_thread.start()

    def update_progress(self, percentage, status_text):
        """更新按钮进度显示（修复版本 - 解决文字不可见问题）"""
        # 更新按钮文本显示进度和状态
        if percentage < 100:
            # 截断过长的状态文本，保持按钮美观
            display_status = status_text
            if len(status_text) > 20:
                display_status = status_text[:17] + "..."

            # 设置按钮文字并确保可见
            button_text = f"{display_status} {percentage}%"
            self.package_btn.setText(button_text)
            print(f"设置按钮文字: {button_text}")  # 调试信息

            # 使用简单的纯色背景替代渐变，确保文字清晰可见
            if percentage < 50:
                # 进度较少时使用灰色背景
                bg_color = "#6c757d"
                border_color = "#5a6268"
            else:
                # 进度较多时使用绿色背景
                bg_color = "#28a745"
                border_color = "#20c997"

            # 使用简单可靠的样式，确保文字清晰可见
            self.package_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {bg_color} !important;
                    color: #ffffff !important;
                    border: 3px solid {border_color} !important;
                    border-radius: 8px !important;
                    font-size: 16px !important;
                    font-weight: bold !important;
                    padding: 10px 20px !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
                }}
                QPushButton:disabled {{
                    background-color: {bg_color} !important;
                    color: #ffffff !important;
                    border: 3px solid {border_color} !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
                }}
            """)
        else:
            # 100%完成时显示特殊状态
            self.package_btn.setText("🎉 即将完成...")
            self.package_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745 !important;
                    color: #ffffff !important;
                    border: 3px solid #20c997 !important;
                    border-radius: 8px !important;
                    font-size: 16px !important;
                    font-weight: bold !important;
                    padding: 10px 20px !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
                    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4) !important;
                }
                QPushButton:disabled {
                    background-color: #28a745 !important;
                    color: #ffffff !important;
                    border: 3px solid #20c997 !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
                }
            """)

    def on_packaging_finished(self, success, message):
        """打包完成的回调函数（优化版本）"""
        self.package_btn.setEnabled(True)

        if success:
            # 成功时显示绿色完成状态
            self.package_btn.setText("✅ 打包完成！")
            self.package_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745 !important;
                    color: #ffffff !important;
                    border: 3px solid #20c997 !important;
                    border-radius: 8px !important;
                    font-size: 16px !important;
                    font-weight: bold !important;
                    padding: 10px 20px !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
                }
                QPushButton:hover {
                    background-color: #218838 !important;
                    border-color: #1e7e34 !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
                }
            """)

            self.log_message(f"\n✅ {message}")
            # 打包成功后自动弹出输出目录
            self.open_output_directory()

            # 3秒后恢复按钮原始状态
            QTimer.singleShot(3000, self.reset_button_style)
        else:
            # 失败时显示红色错误状态
            self.package_btn.setText("❌ 打包失败")
            self.package_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545 !important;
                    color: #ffffff !important;
                    border: 3px solid #c82333 !important;
                    border-radius: 8px !important;
                    font-size: 16px !important;
                    font-weight: bold !important;
                    padding: 10px 20px !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
                }
                QPushButton:hover {
                    background-color: #c82333 !important;
                    border-color: #bd2130 !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,1.0) !important;
                }
            """)

            self.log_message(f"\n❌ {message}")

            # 5秒后恢复按钮原始状态
            QTimer.singleShot(5000, self.reset_button_style)

    def reset_button_style(self):
        """重置按钮为原始样式"""
        self.package_btn.setText("开始打包")
        self.package_btn.setStyleSheet(self.default_button_style)

    def open_output_directory(self):
        """打开输出目录"""
        try:
            import platform
            import subprocess

            # 获取绝对路径
            abs_output_dir = os.path.abspath(self.output_dir)

            if not os.path.exists(abs_output_dir):
                self.log_message(f"⚠️ 输出目录不存在: {abs_output_dir}")
                return

            self.log_message(f"📂 正在打开输出目录: {abs_output_dir}")

            # 根据操作系统选择合适的命令
            system = platform.system()
            if system == "Windows":
                # Windows系统使用explorer
                subprocess.Popen(['explorer', abs_output_dir])
            elif system == "Darwin":  # macOS
                # macOS系统使用open
                subprocess.Popen(['open', abs_output_dir])
            elif system == "Linux":
                # Linux系统使用xdg-open
                subprocess.Popen(['xdg-open', abs_output_dir])
            else:
                self.log_message(f"⚠️ 不支持的操作系统: {system}")
                return

            self.log_message("✅ 输出目录已打开")

        except Exception as e:
            self.log_message(f"❌ 打开输出目录失败: {str(e)}")
            self.log_message(f"💡 请手动打开目录: {os.path.abspath(self.output_dir)}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        if self.package_thread and self.package_thread.isRunning():
            reply = QtWidgets.QMessageBox.question(
                self, '确认退出',
                '打包正在进行中，确定要退出吗？\n退出将中断打包过程。',
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                QtWidgets.QMessageBox.No
            )

            if reply == QtWidgets.QMessageBox.Yes:
                self.package_thread.terminate()
                self.package_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    app = QtWidgets.QApplication(sys.argv)
    ex = PackageUI()
    ex.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()